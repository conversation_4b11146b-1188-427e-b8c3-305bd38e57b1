window.__Zone_disable_customElements=!0;var ce=globalThis;function ee(e){return(ce.__Zone_symbol_prefix||"__zone_symbol__")+e}function ht(){let e=ce.performance;function r(N){e&&e.mark&&e.mark(N)}function c(N,_){e&&e.measure&&e.measure(N,_)}r("Zone");let t=(()=>{class N{static __symbol__=ee;static assertZonePatched(){if(ce.Promise!==O.ZoneAwarePromise)throw new Error("Zone.js has detected that ZoneAwarePromise `(window|global).Promise` has been overwritten.\nMost likely cause is that a Promise polyfill has been loaded after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. If you must load one, do so before loading zone.js.)")}static get root(){let n=N.current;for(;n.parent;)n=n.parent;return n}static get current(){return b.zone}static get currentTask(){return S}static __load_patch(n,o,p=!1){if(O.hasOwnProperty(n)){let P=ce[ee("forceDuplicateZoneCheck")]===!0;if(!p&&P)throw Error("Already loaded patch: "+n)}else if(!ce["__Zone_disable_"+n]){let P="Zone:"+n;r(P),O[n]=o(ce,N,w),c(P,P)}}get parent(){return this._parent}get name(){return this._name}_parent;_name;_properties;_zoneDelegate;constructor(n,o){this._parent=n,this._name=o?o.name||"unnamed":"<root>",this._properties=o&&o.properties||{},this._zoneDelegate=new u(this,this._parent&&this._parent._zoneDelegate,o)}get(n){let o=this.getZoneWith(n);if(o)return o._properties[n]}getZoneWith(n){let o=this;for(;o;){if(o._properties.hasOwnProperty(n))return o;o=o._parent}return null}fork(n){if(!n)throw new Error("ZoneSpec required!");return this._zoneDelegate.fork(this,n)}wrap(n,o){if(typeof n!="function")throw new Error("Expecting function got: "+n);let p=this._zoneDelegate.intercept(this,n,o),P=this;return function(){return P.runGuarded(p,this,arguments,o)}}run(n,o,p,P){b={parent:b,zone:this};try{return this._zoneDelegate.invoke(this,n,o,p,P)}finally{b=b.parent}}runGuarded(n,o=null,p,P){b={parent:b,zone:this};try{try{return this._zoneDelegate.invoke(this,n,o,p,P)}catch(q){if(this._zoneDelegate.handleError(this,q))throw q}}finally{b=b.parent}}runTask(n,o,p){if(n.zone!=this)throw new Error("A task can only be run in the zone of creation! (Creation: "+(n.zone||K).name+"; Execution: "+this.name+")");let P=n,{type:q,data:{isPeriodic:A=!1,isRefreshable:_e=!1}={}}=n;if(n.state===X&&(q===U||q===g))return;let ae=n.state!=j;ae&&P._transitionTo(j,h);let le=S;S=P,b={parent:b,zone:this};try{q==g&&n.data&&!A&&!_e&&(n.cancelFn=void 0);try{return this._zoneDelegate.invokeTask(this,P,o,p)}catch(ne){if(this._zoneDelegate.handleError(this,ne))throw ne}}finally{let ne=n.state;if(ne!==X&&ne!==Y)if(q==U||A||_e&&ne===y)ae&&P._transitionTo(h,j,y);else{let f=P._zoneDelegates;this._updateTaskCount(P,-1),ae&&P._transitionTo(X,j,X),_e&&(P._zoneDelegates=f)}b=b.parent,S=le}}scheduleTask(n){if(n.zone&&n.zone!==this){let p=this;for(;p;){if(p===n.zone)throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${n.zone.name}`);p=p.parent}}n._transitionTo(y,X);let o=[];n._zoneDelegates=o,n._zone=this;try{n=this._zoneDelegate.scheduleTask(this,n)}catch(p){throw n._transitionTo(Y,y,X),this._zoneDelegate.handleError(this,p),p}return n._zoneDelegates===o&&this._updateTaskCount(n,1),n.state==y&&n._transitionTo(h,y),n}scheduleMicroTask(n,o,p,P){return this.scheduleTask(new E(F,n,o,p,P,void 0))}scheduleMacroTask(n,o,p,P,q){return this.scheduleTask(new E(g,n,o,p,P,q))}scheduleEventTask(n,o,p,P,q){return this.scheduleTask(new E(U,n,o,p,P,q))}cancelTask(n){if(n.zone!=this)throw new Error("A task can only be cancelled in the zone of creation! (Creation: "+(n.zone||K).name+"; Execution: "+this.name+")");if(!(n.state!==h&&n.state!==j)){n._transitionTo(x,h,j);try{this._zoneDelegate.cancelTask(this,n)}catch(o){throw n._transitionTo(Y,x),this._zoneDelegate.handleError(this,o),o}return this._updateTaskCount(n,-1),n._transitionTo(X,x),n.runCount=-1,n}}_updateTaskCount(n,o){let p=n._zoneDelegates;o==-1&&(n._zoneDelegates=null);for(let P=0;P<p.length;P++)p[P]._updateTaskCount(n.type,o)}}return N})(),i={name:"",onHasTask:(N,_,n,o)=>N.hasTask(n,o),onScheduleTask:(N,_,n,o)=>N.scheduleTask(n,o),onInvokeTask:(N,_,n,o,p,P)=>N.invokeTask(n,o,p,P),onCancelTask:(N,_,n,o)=>N.cancelTask(n,o)};class u{get zone(){return this._zone}_zone;_taskCounts={microTask:0,macroTask:0,eventTask:0};_parentDelegate;_forkDlgt;_forkZS;_forkCurrZone;_interceptDlgt;_interceptZS;_interceptCurrZone;_invokeDlgt;_invokeZS;_invokeCurrZone;_handleErrorDlgt;_handleErrorZS;_handleErrorCurrZone;_scheduleTaskDlgt;_scheduleTaskZS;_scheduleTaskCurrZone;_invokeTaskDlgt;_invokeTaskZS;_invokeTaskCurrZone;_cancelTaskDlgt;_cancelTaskZS;_cancelTaskCurrZone;_hasTaskDlgt;_hasTaskDlgtOwner;_hasTaskZS;_hasTaskCurrZone;constructor(_,n,o){this._zone=_,this._parentDelegate=n,this._forkZS=o&&(o&&o.onFork?o:n._forkZS),this._forkDlgt=o&&(o.onFork?n:n._forkDlgt),this._forkCurrZone=o&&(o.onFork?this._zone:n._forkCurrZone),this._interceptZS=o&&(o.onIntercept?o:n._interceptZS),this._interceptDlgt=o&&(o.onIntercept?n:n._interceptDlgt),this._interceptCurrZone=o&&(o.onIntercept?this._zone:n._interceptCurrZone),this._invokeZS=o&&(o.onInvoke?o:n._invokeZS),this._invokeDlgt=o&&(o.onInvoke?n:n._invokeDlgt),this._invokeCurrZone=o&&(o.onInvoke?this._zone:n._invokeCurrZone),this._handleErrorZS=o&&(o.onHandleError?o:n._handleErrorZS),this._handleErrorDlgt=o&&(o.onHandleError?n:n._handleErrorDlgt),this._handleErrorCurrZone=o&&(o.onHandleError?this._zone:n._handleErrorCurrZone),this._scheduleTaskZS=o&&(o.onScheduleTask?o:n._scheduleTaskZS),this._scheduleTaskDlgt=o&&(o.onScheduleTask?n:n._scheduleTaskDlgt),this._scheduleTaskCurrZone=o&&(o.onScheduleTask?this._zone:n._scheduleTaskCurrZone),this._invokeTaskZS=o&&(o.onInvokeTask?o:n._invokeTaskZS),this._invokeTaskDlgt=o&&(o.onInvokeTask?n:n._invokeTaskDlgt),this._invokeTaskCurrZone=o&&(o.onInvokeTask?this._zone:n._invokeTaskCurrZone),this._cancelTaskZS=o&&(o.onCancelTask?o:n._cancelTaskZS),this._cancelTaskDlgt=o&&(o.onCancelTask?n:n._cancelTaskDlgt),this._cancelTaskCurrZone=o&&(o.onCancelTask?this._zone:n._cancelTaskCurrZone),this._hasTaskZS=null,this._hasTaskDlgt=null,this._hasTaskDlgtOwner=null,this._hasTaskCurrZone=null;let p=o&&o.onHasTask,P=n&&n._hasTaskZS;(p||P)&&(this._hasTaskZS=p?o:i,this._hasTaskDlgt=n,this._hasTaskDlgtOwner=this,this._hasTaskCurrZone=this._zone,o.onScheduleTask||(this._scheduleTaskZS=i,this._scheduleTaskDlgt=n,this._scheduleTaskCurrZone=this._zone),o.onInvokeTask||(this._invokeTaskZS=i,this._invokeTaskDlgt=n,this._invokeTaskCurrZone=this._zone),o.onCancelTask||(this._cancelTaskZS=i,this._cancelTaskDlgt=n,this._cancelTaskCurrZone=this._zone))}fork(_,n){return this._forkZS?this._forkZS.onFork(this._forkDlgt,this.zone,_,n):new t(_,n)}intercept(_,n,o){return this._interceptZS?this._interceptZS.onIntercept(this._interceptDlgt,this._interceptCurrZone,_,n,o):n}invoke(_,n,o,p,P){return this._invokeZS?this._invokeZS.onInvoke(this._invokeDlgt,this._invokeCurrZone,_,n,o,p,P):n.apply(o,p)}handleError(_,n){return this._handleErrorZS?this._handleErrorZS.onHandleError(this._handleErrorDlgt,this._handleErrorCurrZone,_,n):!0}scheduleTask(_,n){let o=n;if(this._scheduleTaskZS)this._hasTaskZS&&o._zoneDelegates.push(this._hasTaskDlgtOwner),o=this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt,this._scheduleTaskCurrZone,_,n),o||(o=n);else if(n.scheduleFn)n.scheduleFn(n);else if(n.type==F)z(n);else throw new Error("Task is missing scheduleFn.");return o}invokeTask(_,n,o,p){return this._invokeTaskZS?this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt,this._invokeTaskCurrZone,_,n,o,p):n.callback.apply(o,p)}cancelTask(_,n){let o;if(this._cancelTaskZS)o=this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt,this._cancelTaskCurrZone,_,n);else{if(!n.cancelFn)throw Error("Task is not cancelable");o=n.cancelFn(n)}return o}hasTask(_,n){try{this._hasTaskZS&&this._hasTaskZS.onHasTask(this._hasTaskDlgt,this._hasTaskCurrZone,_,n)}catch(o){this.handleError(_,o)}}_updateTaskCount(_,n){let o=this._taskCounts,p=o[_],P=o[_]=p+n;if(P<0)throw new Error("More tasks executed then were scheduled.");if(p==0||P==0){let q={microTask:o.microTask>0,macroTask:o.macroTask>0,eventTask:o.eventTask>0,change:_};this.hasTask(this._zone,q)}}}class E{type;source;invoke;callback;data;scheduleFn;cancelFn;_zone=null;runCount=0;_zoneDelegates=null;_state="notScheduled";constructor(_,n,o,p,P,q){if(this.type=_,this.source=n,this.data=p,this.scheduleFn=P,this.cancelFn=q,!o)throw new Error("callback is not defined");this.callback=o;let A=this;_===U&&p&&p.useG?this.invoke=E.invokeTask:this.invoke=function(){return E.invokeTask.call(ce,A,this,arguments)}}static invokeTask(_,n,o){_||(_=this),Q++;try{return _.runCount++,_.zone.runTask(_,n,o)}finally{Q==1&&J(),Q--}}get zone(){return this._zone}get state(){return this._state}cancelScheduleRequest(){this._transitionTo(X,y)}_transitionTo(_,n,o){if(this._state===n||this._state===o)this._state=_,_==X&&(this._zoneDelegates=null);else throw new Error(`${this.type} '${this.source}': can not transition to '${_}', expecting state '${n}'${o?" or '"+o+"'":""}, was '${this._state}'.`)}toString(){return this.data&&typeof this.data.handleId<"u"?this.data.handleId.toString():Object.prototype.toString.call(this)}toJSON(){return{type:this.type,state:this.state,source:this.source,zone:this.zone.name,runCount:this.runCount}}}let T=ee("setTimeout"),m=ee("Promise"),D=ee("then"),d=[],R=!1,M;function V(N){if(M||ce[m]&&(M=ce[m].resolve(0)),M){let _=M[D];_||(_=M.then),_.call(M,N)}else ce[T](N,0)}function z(N){Q===0&&d.length===0&&V(J),N&&d.push(N)}function J(){if(!R){for(R=!0;d.length;){let N=d;d=[];for(let _=0;_<N.length;_++){let n=N[_];try{n.zone.runTask(n,null,null)}catch(o){w.onUnhandledError(o)}}}w.microtaskDrainDone(),R=!1}}let K={name:"NO ZONE"},X="notScheduled",y="scheduling",h="scheduled",j="running",x="canceling",Y="unknown",F="microTask",g="macroTask",U="eventTask",O={},w={symbol:ee,currentZoneFrame:()=>b,onUnhandledError:W,microtaskDrainDone:W,scheduleMicroTask:z,showUncaughtError:()=>!t[ee("ignoreConsoleErrorUncaughtError")],patchEventTarget:()=>[],patchOnProperties:W,patchMethod:()=>W,bindArguments:()=>[],patchThen:()=>W,patchMacroTask:()=>W,patchEventPrototype:()=>W,isIEOrEdge:()=>!1,getGlobalObjects:()=>{},ObjectDefineProperty:()=>W,ObjectGetOwnPropertyDescriptor:()=>{},ObjectCreate:()=>{},ArraySlice:()=>[],patchClass:()=>W,wrapWithCurrentZone:()=>W,filterProperties:()=>[],attachOriginToPatched:()=>W,_redefineProperty:()=>W,patchCallbacks:()=>W,nativeScheduleMicroTask:V},b={parent:null,zone:new t(null,null)},S=null,Q=0;function W(){}return c("Zone","Zone"),t}function dt(){let e=globalThis,r=e[ee("forceDuplicateZoneCheck")]===!0;if(e.Zone&&(r||typeof e.Zone.__symbol__!="function"))throw new Error("Zone already loaded.");return e.Zone??=ht(),e.Zone}var ye=Object.getOwnPropertyDescriptor,Me=Object.defineProperty,Ae=Object.getPrototypeOf,_t=Object.create,Tt=Array.prototype.slice,je="addEventListener",He="removeEventListener",Ne=ee(je),Ze=ee(He),ue="true",fe="false",ve=ee("");function Ve(e,r){return Zone.current.wrap(e,r)}function xe(e,r,c,t,i){return Zone.current.scheduleMacroTask(e,r,c,t,i)}var H=ee,Ce=typeof window<"u",be=Ce?window:void 0,$=Ce&&be||globalThis,Et="removeAttribute";function Fe(e,r){for(let c=e.length-1;c>=0;c--)typeof e[c]=="function"&&(e[c]=Ve(e[c],r+"_"+c));return e}function gt(e,r){let c=e.constructor.name;for(let t=0;t<r.length;t++){let i=r[t],u=e[i];if(u){let E=ye(e,i);if(!et(E))continue;e[i]=(T=>{let m=function(){return T.apply(this,Fe(arguments,c+"."+i))};return de(m,T),m})(u)}}}function et(e){return e?e.writable===!1?!1:!(typeof e.get=="function"&&typeof e.set>"u"):!0}var tt=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope,De=!("nw"in $)&&typeof $.process<"u"&&$.process.toString()==="[object process]",Ge=!De&&!tt&&!!(Ce&&be.HTMLElement),nt=typeof $.process<"u"&&$.process.toString()==="[object process]"&&!tt&&!!(Ce&&be.HTMLElement),we={},kt=H("enable_beforeunload"),Xe=function(e){if(e=e||$.event,!e)return;let r=we[e.type];r||(r=we[e.type]=H("ON_PROPERTY"+e.type));let c=this||e.target||$,t=c[r],i;if(Ge&&c===be&&e.type==="error"){let u=e;i=t&&t.call(this,u.message,u.filename,u.lineno,u.colno,u.error),i===!0&&e.preventDefault()}else i=t&&t.apply(this,arguments),e.type==="beforeunload"&&$[kt]&&typeof i=="string"?e.returnValue=i:i!=null&&!i&&e.preventDefault();return i};function Ye(e,r,c){let t=ye(e,r);if(!t&&c&&ye(c,r)&&(t={enumerable:!0,configurable:!0}),!t||!t.configurable)return;let i=H("on"+r+"patched");if(e.hasOwnProperty(i)&&e[i])return;delete t.writable,delete t.value;let u=t.get,E=t.set,T=r.slice(2),m=we[T];m||(m=we[T]=H("ON_PROPERTY"+T)),t.set=function(D){let d=this;if(!d&&e===$&&(d=$),!d)return;typeof d[m]=="function"&&d.removeEventListener(T,Xe),E?.call(d,null),d[m]=D,typeof D=="function"&&d.addEventListener(T,Xe,!1)},t.get=function(){let D=this;if(!D&&e===$&&(D=$),!D)return null;let d=D[m];if(d)return d;if(u){let R=u.call(this);if(R)return t.set.call(this,R),typeof D[Et]=="function"&&D.removeAttribute(r),R}return null},Me(e,r,t),e[i]=!0}function rt(e,r,c){if(r)for(let t=0;t<r.length;t++)Ye(e,"on"+r[t],c);else{let t=[];for(let i in e)i.slice(0,2)=="on"&&t.push(i);for(let i=0;i<t.length;i++)Ye(e,t[i],c)}}var oe=H("originalInstance");function pe(e){let r=$[e];if(!r)return;$[H(e)]=r,$[e]=function(){let i=Fe(arguments,e);switch(i.length){case 0:this[oe]=new r;break;case 1:this[oe]=new r(i[0]);break;case 2:this[oe]=new r(i[0],i[1]);break;case 3:this[oe]=new r(i[0],i[1],i[2]);break;case 4:this[oe]=new r(i[0],i[1],i[2],i[3]);break;default:throw new Error("Arg list too long.")}},de($[e],r);let c=new r(function(){}),t;for(t in c)e==="XMLHttpRequest"&&t==="responseBlob"||function(i){typeof c[i]=="function"?$[e].prototype[i]=function(){return this[oe][i].apply(this[oe],arguments)}:Me($[e].prototype,i,{set:function(u){typeof u=="function"?(this[oe][i]=Ve(u,e+"."+i),de(this[oe][i],u)):this[oe][i]=u},get:function(){return this[oe][i]}})}(t);for(t in r)t!=="prototype"&&r.hasOwnProperty(t)&&($[e][t]=r[t])}function he(e,r,c){let t=e;for(;t&&!t.hasOwnProperty(r);)t=Ae(t);!t&&e[r]&&(t=e);let i=H(r),u=null;if(t&&(!(u=t[i])||!t.hasOwnProperty(i))){u=t[i]=t[r];let E=t&&ye(t,r);if(et(E)){let T=c(u,i,r);t[r]=function(){return T(this,arguments)},de(t[r],u)}}return u}function mt(e,r,c){let t=null;function i(u){let E=u.data;return E.args[E.cbIdx]=function(){u.invoke.apply(this,arguments)},t.apply(E.target,E.args),u}t=he(e,r,u=>function(E,T){let m=c(E,T);return m.cbIdx>=0&&typeof T[m.cbIdx]=="function"?xe(m.name,T[m.cbIdx],m,i):u.apply(E,T)})}function de(e,r){e[H("OriginalDelegate")]=r}var $e=!1,Le=!1;function pt(){if($e)return Le;$e=!0;try{let e=be.navigator.userAgent;(e.indexOf("MSIE ")!==-1||e.indexOf("Trident/")!==-1||e.indexOf("Edge/")!==-1)&&(Le=!0)}catch{}return Le}function Je(e){return typeof e=="function"}function Ke(e){return typeof e=="number"}var yt={useG:!0},te={},ot={},st=new RegExp("^"+ve+"(\\w+)(true|false)$"),it=H("propagationStopped");function ct(e,r){let c=(r?r(e):e)+fe,t=(r?r(e):e)+ue,i=ve+c,u=ve+t;te[e]={},te[e][fe]=i,te[e][ue]=u}function vt(e,r,c,t){let i=t&&t.add||je,u=t&&t.rm||He,E=t&&t.listeners||"eventListeners",T=t&&t.rmAll||"removeAllListeners",m=H(i),D="."+i+":",d="prependListener",R="."+d+":",M=function(y,h,j){if(y.isRemoved)return;let x=y.callback;typeof x=="object"&&x.handleEvent&&(y.callback=g=>x.handleEvent(g),y.originalDelegate=x);let Y;try{y.invoke(y,h,[j])}catch(g){Y=g}let F=y.options;if(F&&typeof F=="object"&&F.once){let g=y.originalDelegate?y.originalDelegate:y.callback;h[u].call(h,j.type,g,F)}return Y};function V(y,h,j){if(h=h||e.event,!h)return;let x=y||h.target||e,Y=x[te[h.type][j?ue:fe]];if(Y){let F=[];if(Y.length===1){let g=M(Y[0],x,h);g&&F.push(g)}else{let g=Y.slice();for(let U=0;U<g.length&&!(h&&h[it]===!0);U++){let O=M(g[U],x,h);O&&F.push(O)}}if(F.length===1)throw F[0];for(let g=0;g<F.length;g++){let U=F[g];r.nativeScheduleMicroTask(()=>{throw U})}}}let z=function(y){return V(this,y,!1)},J=function(y){return V(this,y,!0)};function K(y,h){if(!y)return!1;let j=!0;h&&h.useG!==void 0&&(j=h.useG);let x=h&&h.vh,Y=!0;h&&h.chkDup!==void 0&&(Y=h.chkDup);let F=!1;h&&h.rt!==void 0&&(F=h.rt);let g=y;for(;g&&!g.hasOwnProperty(i);)g=Ae(g);if(!g&&y[i]&&(g=y),!g||g[m])return!1;let U=h&&h.eventNameToString,O={},w=g[m]=g[i],b=g[H(u)]=g[u],S=g[H(E)]=g[E],Q=g[H(T)]=g[T],W;h&&h.prepend&&(W=g[H(h.prepend)]=g[h.prepend]);function N(s,l){return l?typeof s=="boolean"?{capture:s,passive:!0}:s?typeof s=="object"&&s.passive!==!1?{...s,passive:!0}:s:{passive:!0}:s}let _=function(s){if(!O.isExisting)return w.call(O.target,O.eventName,O.capture?J:z,O.options)},n=function(s){if(!s.isRemoved){let l=te[s.eventName],v;l&&(v=l[s.capture?ue:fe]);let C=v&&s.target[v];if(C){for(let k=0;k<C.length;k++)if(C[k]===s){C.splice(k,1),s.isRemoved=!0,s.removeAbortListener&&(s.removeAbortListener(),s.removeAbortListener=null),C.length===0&&(s.allRemoved=!0,s.target[v]=null);break}}}if(s.allRemoved)return b.call(s.target,s.eventName,s.capture?J:z,s.options)},o=function(s){return w.call(O.target,O.eventName,s.invoke,O.options)},p=function(s){return W.call(O.target,O.eventName,s.invoke,O.options)},P=function(s){return b.call(s.target,s.eventName,s.invoke,s.options)},q=j?_:o,A=j?n:P,_e=function(s,l){let v=typeof l;return v==="function"&&s.callback===l||v==="object"&&s.originalDelegate===l},ae=h?.diff||_e,le=Zone[H("UNPATCHED_EVENTS")],ne=e[H("PASSIVE_EVENTS")];function f(s){if(typeof s=="object"&&s!==null){let l={...s};return s.signal&&(l.signal=s.signal),l}return s}let a=function(s,l,v,C,k=!1,Z=!1){return function(){let L=this||e,I=arguments[0];h&&h.transferEventName&&(I=h.transferEventName(I));let G=arguments[1];if(!G)return s.apply(this,arguments);if(De&&I==="uncaughtException")return s.apply(this,arguments);let B=!1;if(typeof G!="function"){if(!G.handleEvent)return s.apply(this,arguments);B=!0}if(x&&!x(s,G,L,arguments))return;let Te=!!ne&&ne.indexOf(I)!==-1,se=f(N(arguments[2],Te)),Ee=se?.signal;if(Ee?.aborted)return;if(le){for(let ie=0;ie<le.length;ie++)if(I===le[ie])return Te?s.call(L,I,G,se):s.apply(this,arguments)}let Se=se?typeof se=="boolean"?!0:se.capture:!1,Be=se&&typeof se=="object"?se.once:!1,ft=Zone.current,Oe=te[I];Oe||(ct(I,U),Oe=te[I]);let ze=Oe[Se?ue:fe],ge=L[ze],Ue=!1;if(ge){if(Ue=!0,Y){for(let ie=0;ie<ge.length;ie++)if(ae(ge[ie],G))return}}else ge=L[ze]=[];let Pe,We=L.constructor.name,qe=ot[We];qe&&(Pe=qe[I]),Pe||(Pe=We+l+(U?U(I):I)),O.options=se,Be&&(O.options.once=!1),O.target=L,O.capture=Se,O.eventName=I,O.isExisting=Ue;let me=j?yt:void 0;me&&(me.taskData=O),Ee&&(O.options.signal=void 0);let re=ft.scheduleEventTask(Pe,G,me,v,C);if(Ee){O.options.signal=Ee;let ie=()=>re.zone.cancelTask(re);s.call(Ee,"abort",ie,{once:!0}),re.removeAbortListener=()=>Ee.removeEventListener("abort",ie)}if(O.target=null,me&&(me.taskData=null),Be&&(O.options.once=!0),typeof re.options!="boolean"&&(re.options=se),re.target=L,re.capture=Se,re.eventName=I,B&&(re.originalDelegate=G),Z?ge.unshift(re):ge.push(re),k)return L}};return g[i]=a(w,D,q,A,F),W&&(g[d]=a(W,R,p,A,F,!0)),g[u]=function(){let s=this||e,l=arguments[0];h&&h.transferEventName&&(l=h.transferEventName(l));let v=arguments[2],C=v?typeof v=="boolean"?!0:v.capture:!1,k=arguments[1];if(!k)return b.apply(this,arguments);if(x&&!x(b,k,s,arguments))return;let Z=te[l],L;Z&&(L=Z[C?ue:fe]);let I=L&&s[L];if(I)for(let G=0;G<I.length;G++){let B=I[G];if(ae(B,k)){if(I.splice(G,1),B.isRemoved=!0,I.length===0&&(B.allRemoved=!0,s[L]=null,!C&&typeof l=="string")){let Te=ve+"ON_PROPERTY"+l;s[Te]=null}return B.zone.cancelTask(B),F?s:void 0}}return b.apply(this,arguments)},g[E]=function(){let s=this||e,l=arguments[0];h&&h.transferEventName&&(l=h.transferEventName(l));let v=[],C=at(s,U?U(l):l);for(let k=0;k<C.length;k++){let Z=C[k],L=Z.originalDelegate?Z.originalDelegate:Z.callback;v.push(L)}return v},g[T]=function(){let s=this||e,l=arguments[0];if(l){h&&h.transferEventName&&(l=h.transferEventName(l));let v=te[l];if(v){let C=v[fe],k=v[ue],Z=s[C],L=s[k];if(Z){let I=Z.slice();for(let G=0;G<I.length;G++){let B=I[G],Te=B.originalDelegate?B.originalDelegate:B.callback;this[u].call(this,l,Te,B.options)}}if(L){let I=L.slice();for(let G=0;G<I.length;G++){let B=I[G],Te=B.originalDelegate?B.originalDelegate:B.callback;this[u].call(this,l,Te,B.options)}}}}else{let v=Object.keys(s);for(let C=0;C<v.length;C++){let k=v[C],Z=st.exec(k),L=Z&&Z[1];L&&L!=="removeListener"&&this[T].call(this,L)}this[T].call(this,"removeListener")}if(F)return this},de(g[i],w),de(g[u],b),Q&&de(g[T],Q),S&&de(g[E],S),!0}let X=[];for(let y=0;y<c.length;y++)X[y]=K(c[y],t);return X}function at(e,r){if(!r){let u=[];for(let E in e){let T=st.exec(E),m=T&&T[1];if(m&&(!r||m===r)){let D=e[E];if(D)for(let d=0;d<D.length;d++)u.push(D[d])}}return u}let c=te[r];c||(ct(r),c=te[r]);let t=e[c[fe]],i=e[c[ue]];return t?i?t.concat(i):t.slice():i?i.slice():[]}function bt(e,r){let c=e.Event;c&&c.prototype&&r.patchMethod(c.prototype,"stopImmediatePropagation",t=>function(i,u){i[it]=!0,t&&t.apply(i,u)})}function Pt(e,r){r.patchMethod(e,"queueMicrotask",c=>function(t,i){Zone.current.scheduleMicroTask("queueMicrotask",i[0])})}var Re=H("zoneTask");function ke(e,r,c,t){let i=null,u=null;r+=t,c+=t;let E={};function T(D){let d=D.data;d.args[0]=function(){return D.invoke.apply(this,arguments)};let R=i.apply(e,d.args);return Ke(R)?d.handleId=R:(d.handle=R,d.isRefreshable=Je(R.refresh)),D}function m(D){let{handle:d,handleId:R}=D.data;return u.call(e,d??R)}i=he(e,r,D=>function(d,R){if(Je(R[0])){let M={isRefreshable:!1,isPeriodic:t==="Interval",delay:t==="Timeout"||t==="Interval"?R[1]||0:void 0,args:R},V=R[0];R[0]=function(){try{return V.apply(this,arguments)}finally{let{handle:j,handleId:x,isPeriodic:Y,isRefreshable:F}=M;!Y&&!F&&(x?delete E[x]:j&&(j[Re]=null))}};let z=xe(r,R[0],M,T,m);if(!z)return z;let{handleId:J,handle:K,isRefreshable:X,isPeriodic:y}=z.data;if(J)E[J]=z;else if(K&&(K[Re]=z,X&&!y)){let h=K.refresh;K.refresh=function(){let{zone:j,state:x}=z;return x==="notScheduled"?(z._state="scheduled",j._updateTaskCount(z,1)):x==="running"&&(z._state="scheduling"),h.call(this)}}return K??J??z}else return D.apply(e,R)}),u=he(e,c,D=>function(d,R){let M=R[0],V;Ke(M)?(V=E[M],delete E[M]):(V=M?.[Re],V?M[Re]=null:V=M),V?.type?V.cancelFn&&V.zone.cancelTask(V):D.apply(e,R)})}function Rt(e,r){let{isBrowser:c,isMix:t}=r.getGlobalObjects();if(!c&&!t||!e.customElements||!("customElements"in e))return;let i=["connectedCallback","disconnectedCallback","adoptedCallback","attributeChangedCallback","formAssociatedCallback","formDisabledCallback","formResetCallback","formStateRestoreCallback"];r.patchCallbacks(r,e.customElements,"customElements","define",i)}function wt(e,r){if(Zone[r.symbol("patchEventTarget")])return;let{eventNames:c,zoneSymbolEventNames:t,TRUE_STR:i,FALSE_STR:u,ZONE_SYMBOL_PREFIX:E}=r.getGlobalObjects();for(let m=0;m<c.length;m++){let D=c[m],d=D+u,R=D+i,M=E+d,V=E+R;t[D]={},t[D][u]=M,t[D][i]=V}let T=e.EventTarget;if(!(!T||!T.prototype))return r.patchEventTarget(e,r,[T&&T.prototype]),!0}function Ct(e,r){r.patchEventPrototype(e,r)}function lt(e,r,c){if(!c||c.length===0)return r;let t=c.filter(u=>u.target===e);if(t.length===0)return r;let i=t[0].ignoreProperties;return r.filter(u=>i.indexOf(u)===-1)}function Qe(e,r,c,t){if(!e)return;let i=lt(e,r,c);rt(e,i,t)}function Ie(e){return Object.getOwnPropertyNames(e).filter(r=>r.startsWith("on")&&r.length>2).map(r=>r.substring(2))}function Dt(e,r){if(De&&!nt||Zone[e.symbol("patchEvents")])return;let c=r.__Zone_ignore_on_properties,t=[];if(Ge){let i=window;t=t.concat(["Document","SVGElement","Element","HTMLElement","HTMLBodyElement","HTMLMediaElement","HTMLFrameSetElement","HTMLFrameElement","HTMLIFrameElement","HTMLMarqueeElement","Worker"]);let u=[];Qe(i,Ie(i),c&&c.concat(u),Ae(i))}t=t.concat(["XMLHttpRequest","XMLHttpRequestEventTarget","IDBIndex","IDBRequest","IDBOpenDBRequest","IDBDatabase","IDBTransaction","IDBCursor","WebSocket"]);for(let i=0;i<t.length;i++){let u=r[t[i]];u?.prototype&&Qe(u.prototype,Ie(u.prototype),c)}}function St(e){e.__load_patch("legacy",r=>{let c=r[e.__symbol__("legacyPatch")];c&&c()}),e.__load_patch("timers",r=>{let c="set",t="clear";ke(r,c,t,"Timeout"),ke(r,c,t,"Interval"),ke(r,c,t,"Immediate")}),e.__load_patch("requestAnimationFrame",r=>{ke(r,"request","cancel","AnimationFrame"),ke(r,"mozRequest","mozCancel","AnimationFrame"),ke(r,"webkitRequest","webkitCancel","AnimationFrame")}),e.__load_patch("blocking",(r,c)=>{let t=["alert","prompt","confirm"];for(let i=0;i<t.length;i++){let u=t[i];he(r,u,(E,T,m)=>function(D,d){return c.current.run(E,r,d,m)})}}),e.__load_patch("EventTarget",(r,c,t)=>{Ct(r,t),wt(r,t);let i=r.XMLHttpRequestEventTarget;i&&i.prototype&&t.patchEventTarget(r,t,[i.prototype])}),e.__load_patch("MutationObserver",(r,c,t)=>{pe("MutationObserver"),pe("WebKitMutationObserver")}),e.__load_patch("IntersectionObserver",(r,c,t)=>{pe("IntersectionObserver")}),e.__load_patch("FileReader",(r,c,t)=>{pe("FileReader")}),e.__load_patch("on_property",(r,c,t)=>{Dt(t,r)}),e.__load_patch("customElements",(r,c,t)=>{Rt(r,t)}),e.__load_patch("XHR",(r,c)=>{D(r);let t=H("xhrTask"),i=H("xhrSync"),u=H("xhrListener"),E=H("xhrScheduled"),T=H("xhrURL"),m=H("xhrErrorBeforeScheduled");function D(d){let R=d.XMLHttpRequest;if(!R)return;let M=R.prototype;function V(w){return w[t]}let z=M[Ne],J=M[Ze];if(!z){let w=d.XMLHttpRequestEventTarget;if(w){let b=w.prototype;z=b[Ne],J=b[Ze]}}let K="readystatechange",X="scheduled";function y(w){let b=w.data,S=b.target;S[E]=!1,S[m]=!1;let Q=S[u];z||(z=S[Ne],J=S[Ze]),Q&&J.call(S,K,Q);let W=S[u]=()=>{if(S.readyState===S.DONE)if(!b.aborted&&S[E]&&w.state===X){let _=S[c.__symbol__("loadfalse")];if(S.status!==0&&_&&_.length>0){let n=w.invoke;w.invoke=function(){let o=S[c.__symbol__("loadfalse")];for(let p=0;p<o.length;p++)o[p]===w&&o.splice(p,1);!b.aborted&&w.state===X&&n.call(w)},_.push(w)}else w.invoke()}else!b.aborted&&S[E]===!1&&(S[m]=!0)};return z.call(S,K,W),S[t]||(S[t]=w),U.apply(S,b.args),S[E]=!0,w}function h(){}function j(w){let b=w.data;return b.aborted=!0,O.apply(b.target,b.args)}let x=he(M,"open",()=>function(w,b){return w[i]=b[2]==!1,w[T]=b[1],x.apply(w,b)}),Y="XMLHttpRequest.send",F=H("fetchTaskAborting"),g=H("fetchTaskScheduling"),U=he(M,"send",()=>function(w,b){if(c.current[g]===!0||w[i])return U.apply(w,b);{let S={target:w,url:w[T],isPeriodic:!1,args:b,aborted:!1},Q=xe(Y,h,S,y,j);w&&w[m]===!0&&!S.aborted&&Q.state===X&&Q.invoke()}}),O=he(M,"abort",()=>function(w,b){let S=V(w);if(S&&typeof S.type=="string"){if(S.cancelFn==null||S.data&&S.data.aborted)return;S.zone.cancelTask(S)}else if(c.current[F]===!0)return O.apply(w,b)})}}),e.__load_patch("geolocation",r=>{r.navigator&&r.navigator.geolocation&&gt(r.navigator.geolocation,["getCurrentPosition","watchPosition"])}),e.__load_patch("PromiseRejectionEvent",(r,c)=>{function t(i){return function(u){at(r,i).forEach(T=>{let m=r.PromiseRejectionEvent;if(m){let D=new m(i,{promise:u.promise,reason:u.rejection});T.invoke(D)}})}}r.PromiseRejectionEvent&&(c[H("unhandledPromiseRejectionHandler")]=t("unhandledrejection"),c[H("rejectionHandledHandler")]=t("rejectionhandled"))}),e.__load_patch("queueMicrotask",(r,c,t)=>{Pt(r,t)})}function Ot(e){e.__load_patch("ZoneAwarePromise",(r,c,t)=>{let i=Object.getOwnPropertyDescriptor,u=Object.defineProperty;function E(f){if(f&&f.toString===Object.prototype.toString){let a=f.constructor&&f.constructor.name;return(a||"")+": "+JSON.stringify(f)}return f?f.toString():Object.prototype.toString.call(f)}let T=t.symbol,m=[],D=r[T("DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION")]!==!1,d=T("Promise"),R=T("then"),M="__creationTrace__";t.onUnhandledError=f=>{if(t.showUncaughtError()){let a=f&&f.rejection;a?console.error("Unhandled Promise rejection:",a instanceof Error?a.message:a,"; Zone:",f.zone.name,"; Task:",f.task&&f.task.source,"; Value:",a,a instanceof Error?a.stack:void 0):console.error(f)}},t.microtaskDrainDone=()=>{for(;m.length;){let f=m.shift();try{f.zone.runGuarded(()=>{throw f.throwOriginal?f.rejection:f})}catch(a){z(a)}}};let V=T("unhandledPromiseRejectionHandler");function z(f){t.onUnhandledError(f);try{let a=c[V];typeof a=="function"&&a.call(this,f)}catch{}}function J(f){return f&&typeof f.then=="function"}function K(f){return f}function X(f){return A.reject(f)}let y=T("state"),h=T("value"),j=T("finally"),x=T("parentPromiseValue"),Y=T("parentPromiseState"),F="Promise.then",g=null,U=!0,O=!1,w=0;function b(f,a){return s=>{try{N(f,a,s)}catch(l){N(f,!1,l)}}}let S=function(){let f=!1;return function(s){return function(){f||(f=!0,s.apply(null,arguments))}}},Q="Promise resolved with itself",W=T("currentTaskTrace");function N(f,a,s){let l=S();if(f===s)throw new TypeError(Q);if(f[y]===g){let v=null;try{(typeof s=="object"||typeof s=="function")&&(v=s&&s.then)}catch(C){return l(()=>{N(f,!1,C)})(),f}if(a!==O&&s instanceof A&&s.hasOwnProperty(y)&&s.hasOwnProperty(h)&&s[y]!==g)n(s),N(f,s[y],s[h]);else if(a!==O&&typeof v=="function")try{v.call(s,l(b(f,a)),l(b(f,!1)))}catch(C){l(()=>{N(f,!1,C)})()}else{f[y]=a;let C=f[h];if(f[h]=s,f[j]===j&&a===U&&(f[y]=f[Y],f[h]=f[x]),a===O&&s instanceof Error){let k=c.currentTask&&c.currentTask.data&&c.currentTask.data[M];k&&u(s,W,{configurable:!0,enumerable:!1,writable:!0,value:k})}for(let k=0;k<C.length;)o(f,C[k++],C[k++],C[k++],C[k++]);if(C.length==0&&a==O){f[y]=w;let k=s;try{throw new Error("Uncaught (in promise): "+E(s)+(s&&s.stack?`
`+s.stack:""))}catch(Z){k=Z}D&&(k.throwOriginal=!0),k.rejection=s,k.promise=f,k.zone=c.current,k.task=c.currentTask,m.push(k),t.scheduleMicroTask()}}}return f}let _=T("rejectionHandledHandler");function n(f){if(f[y]===w){try{let a=c[_];a&&typeof a=="function"&&a.call(this,{rejection:f[h],promise:f})}catch{}f[y]=O;for(let a=0;a<m.length;a++)f===m[a].promise&&m.splice(a,1)}}function o(f,a,s,l,v){n(f);let C=f[y],k=C?typeof l=="function"?l:K:typeof v=="function"?v:X;a.scheduleMicroTask(F,()=>{try{let Z=f[h],L=!!s&&j===s[j];L&&(s[x]=Z,s[Y]=C);let I=a.run(k,void 0,L&&k!==X&&k!==K?[]:[Z]);N(s,!0,I)}catch(Z){N(s,!1,Z)}},s)}let p="function ZoneAwarePromise() { [native code] }",P=function(){},q=r.AggregateError;class A{static toString(){return p}static resolve(a){return a instanceof A?a:N(new this(null),U,a)}static reject(a){return N(new this(null),O,a)}static withResolvers(){let a={};return a.promise=new A((s,l)=>{a.resolve=s,a.reject=l}),a}static any(a){if(!a||typeof a[Symbol.iterator]!="function")return Promise.reject(new q([],"All promises were rejected"));let s=[],l=0;try{for(let k of a)l++,s.push(A.resolve(k))}catch{return Promise.reject(new q([],"All promises were rejected"))}if(l===0)return Promise.reject(new q([],"All promises were rejected"));let v=!1,C=[];return new A((k,Z)=>{for(let L=0;L<s.length;L++)s[L].then(I=>{v||(v=!0,k(I))},I=>{C.push(I),l--,l===0&&(v=!0,Z(new q(C,"All promises were rejected")))})})}static race(a){let s,l,v=new this((Z,L)=>{s=Z,l=L});function C(Z){s(Z)}function k(Z){l(Z)}for(let Z of a)J(Z)||(Z=this.resolve(Z)),Z.then(C,k);return v}static all(a){return A.allWithCallback(a)}static allSettled(a){return(this&&this.prototype instanceof A?this:A).allWithCallback(a,{thenCallback:l=>({status:"fulfilled",value:l}),errorCallback:l=>({status:"rejected",reason:l})})}static allWithCallback(a,s){let l,v,C=new this((I,G)=>{l=I,v=G}),k=2,Z=0,L=[];for(let I of a){J(I)||(I=this.resolve(I));let G=Z;try{I.then(B=>{L[G]=s?s.thenCallback(B):B,k--,k===0&&l(L)},B=>{s?(L[G]=s.errorCallback(B),k--,k===0&&l(L)):v(B)})}catch(B){v(B)}k++,Z++}return k-=2,k===0&&l(L),C}constructor(a){let s=this;if(!(s instanceof A))throw new Error("Must be an instanceof Promise.");s[y]=g,s[h]=[];try{let l=S();a&&a(l(b(s,U)),l(b(s,O)))}catch(l){N(s,!1,l)}}get[Symbol.toStringTag](){return"Promise"}get[Symbol.species](){return A}then(a,s){let l=this.constructor?.[Symbol.species];(!l||typeof l!="function")&&(l=this.constructor||A);let v=new l(P),C=c.current;return this[y]==g?this[h].push(C,v,a,s):o(this,C,v,a,s),v}catch(a){return this.then(null,a)}finally(a){let s=this.constructor?.[Symbol.species];(!s||typeof s!="function")&&(s=A);let l=new s(P);l[j]=j;let v=c.current;return this[y]==g?this[h].push(v,l,a,a):o(this,v,l,a,a),l}}A.resolve=A.resolve,A.reject=A.reject,A.race=A.race,A.all=A.all;let _e=r[d]=r.Promise;r.Promise=A;let ae=T("thenPatched");function le(f){let a=f.prototype,s=i(a,"then");if(s&&(s.writable===!1||!s.configurable))return;let l=a.then;a[R]=l,f.prototype.then=function(v,C){return new A((Z,L)=>{l.call(this,Z,L)}).then(v,C)},f[ae]=!0}t.patchThen=le;function ne(f){return function(a,s){let l=f.apply(a,s);if(l instanceof A)return l;let v=l.constructor;return v[ae]||le(v),l}}return _e&&(le(_e),he(r,"fetch",f=>ne(f))),Promise[c.__symbol__("uncaughtPromiseErrors")]=m,A})}function Nt(e){e.__load_patch("toString",r=>{let c=Function.prototype.toString,t=H("OriginalDelegate"),i=H("Promise"),u=H("Error"),E=function(){if(typeof this=="function"){let d=this[t];if(d)return typeof d=="function"?c.call(d):Object.prototype.toString.call(d);if(this===Promise){let R=r[i];if(R)return c.call(R)}if(this===Error){let R=r[u];if(R)return c.call(R)}}return c.call(this)};E[t]=c,Function.prototype.toString=E;let T=Object.prototype.toString,m="[object Promise]";Object.prototype.toString=function(){return typeof Promise=="function"&&this instanceof Promise?m:T.call(this)}})}function Zt(e,r,c,t,i){let u=Zone.__symbol__(t);if(r[u])return;let E=r[u]=r[t];r[t]=function(T,m,D){return m&&m.prototype&&i.forEach(function(d){let R=`${c}.${t}::`+d,M=m.prototype;try{if(M.hasOwnProperty(d)){let V=e.ObjectGetOwnPropertyDescriptor(M,d);V&&V.value?(V.value=e.wrapWithCurrentZone(V.value,R),e._redefineProperty(m.prototype,d,V)):M[d]&&(M[d]=e.wrapWithCurrentZone(M[d],R))}else M[d]&&(M[d]=e.wrapWithCurrentZone(M[d],R))}catch{}}),E.call(r,T,m,D)},e.attachOriginToPatched(r[t],E)}function Lt(e){e.__load_patch("util",(r,c,t)=>{let i=Ie(r);t.patchOnProperties=rt,t.patchMethod=he,t.bindArguments=Fe,t.patchMacroTask=mt;let u=c.__symbol__("BLACK_LISTED_EVENTS"),E=c.__symbol__("UNPATCHED_EVENTS");r[E]&&(r[u]=r[E]),r[u]&&(c[u]=c[E]=r[u]),t.patchEventPrototype=bt,t.patchEventTarget=vt,t.isIEOrEdge=pt,t.ObjectDefineProperty=Me,t.ObjectGetOwnPropertyDescriptor=ye,t.ObjectCreate=_t,t.ArraySlice=Tt,t.patchClass=pe,t.wrapWithCurrentZone=Ve,t.filterProperties=lt,t.attachOriginToPatched=de,t._redefineProperty=Object.defineProperty,t.patchCallbacks=Zt,t.getGlobalObjects=()=>({globalSources:ot,zoneSymbolEventNames:te,eventNames:i,isBrowser:Ge,isMix:nt,isNode:De,TRUE_STR:ue,FALSE_STR:fe,ZONE_SYMBOL_PREFIX:ve,ADD_EVENT_LISTENER_STR:je,REMOVE_EVENT_LISTENER_STR:He})})}function It(e){Ot(e),Nt(e),Lt(e)}var ut=dt();It(ut);St(ut);
