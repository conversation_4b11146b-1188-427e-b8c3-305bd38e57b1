import{a as H,c as ze,e as te}from"./chunk-JHI3MBHO.js";var Pt=ze({"./ion-accordion_2.entry.js":()=>import("./chunk-W2UZGI5N.js"),"./ion-action-sheet.entry.js":()=>import("./chunk-3OVIDIG7.js"),"./ion-alert.entry.js":()=>import("./chunk-PTQKE4SQ.js"),"./ion-app_8.entry.js":()=>import("./chunk-I3EXE7LU.js"),"./ion-avatar_3.entry.js":()=>import("./chunk-3QT53X5L.js"),"./ion-back-button.entry.js":()=>import("./chunk-5BQF4WOG.js"),"./ion-backdrop.entry.js":()=>import("./chunk-7SD4CQBR.js"),"./ion-breadcrumb_2.entry.js":()=>import("./chunk-MAOYJ7ZP.js"),"./ion-button_2.entry.js":()=>import("./chunk-SPUJS6ZU.js"),"./ion-card_5.entry.js":()=>import("./chunk-74IC6RZP.js"),"./ion-checkbox.entry.js":()=>import("./chunk-H5ZT5OFU.js"),"./ion-chip.entry.js":()=>import("./chunk-LIFJMD7S.js"),"./ion-col_3.entry.js":()=>import("./chunk-GNEQ6X76.js"),"./ion-datetime-button.entry.js":()=>import("./chunk-GBL5IFQN.js"),"./ion-datetime_3.entry.js":()=>import("./chunk-5IYG6FO3.js"),"./ion-fab_3.entry.js":()=>import("./chunk-FWO2V3DW.js"),"./ion-img.entry.js":()=>import("./chunk-KPHRXRAY.js"),"./ion-infinite-scroll_2.entry.js":()=>import("./chunk-7BHCL7S3.js"),"./ion-input-otp.entry.js":()=>import("./chunk-PABSXYUU.js"),"./ion-input-password-toggle.entry.js":()=>import("./chunk-B4K7UMLE.js"),"./ion-input.entry.js":()=>import("./chunk-TSYGOVJA.js"),"./ion-item-option_3.entry.js":()=>import("./chunk-JTFGGAEH.js"),"./ion-item_8.entry.js":()=>import("./chunk-QJF7GBIU.js"),"./ion-loading.entry.js":()=>import("./chunk-T2X2K5BK.js"),"./ion-menu_3.entry.js":()=>import("./chunk-GHQL6CDK.js"),"./ion-modal.entry.js":()=>import("./chunk-UNR5HW6A.js"),"./ion-nav_2.entry.js":()=>import("./chunk-YOEQOUDH.js"),"./ion-picker-column-option.entry.js":()=>import("./chunk-TI3EUR4Y.js"),"./ion-picker-column.entry.js":()=>import("./chunk-TMIYJSIY.js"),"./ion-picker.entry.js":()=>import("./chunk-72IEYIS3.js"),"./ion-popover.entry.js":()=>import("./chunk-JRQY76EU.js"),"./ion-progress-bar.entry.js":()=>import("./chunk-W7EZWAUM.js"),"./ion-radio_2.entry.js":()=>import("./chunk-Z5XSXWKP.js"),"./ion-range.entry.js":()=>import("./chunk-NE6IOVOT.js"),"./ion-refresher_2.entry.js":()=>import("./chunk-AX7UL672.js"),"./ion-reorder_2.entry.js":()=>import("./chunk-3IDARKRX.js"),"./ion-ripple-effect.entry.js":()=>import("./chunk-UY7XOJOV.js"),"./ion-route_4.entry.js":()=>import("./chunk-GXNTWJSS.js"),"./ion-searchbar.entry.js":()=>import("./chunk-VWMYQEBP.js"),"./ion-segment-content.entry.js":()=>import("./chunk-R3AXNM6O.js"),"./ion-segment-view.entry.js":()=>import("./chunk-M7FJ7JVT.js"),"./ion-segment_2.entry.js":()=>import("./chunk-6UYAN6GD.js"),"./ion-select-modal.entry.js":()=>import("./chunk-JNSU323F.js"),"./ion-select_3.entry.js":()=>import("./chunk-L3M4BT5G.js"),"./ion-spinner.entry.js":()=>import("./chunk-MT77VGSD.js"),"./ion-split-pane.entry.js":()=>import("./chunk-FG6CH3Q5.js"),"./ion-tab-bar_2.entry.js":()=>import("./chunk-2BBA6SBU.js"),"./ion-tab_2.entry.js":()=>import("./chunk-AIZVOCRI.js"),"./ion-text.entry.js":()=>import("./chunk-QZWZFW2H.js"),"./ion-textarea.entry.js":()=>import("./chunk-QZQONZIP.js"),"./ion-toast.entry.js":()=>import("./chunk-TUBJT37T.js"),"./ion-toggle.entry.js":()=>import("./chunk-ZKKBIVAF.js")});var Mt="ionic",Y={experimentalSlotFixes:!0,hydratedSelectorName:"hydrated",lazyLoad:!0,shadowDom:!0,slotRelocation:!0,updatable:!0},be=class{constructor(){this.m=new Map}reset(t){this.m=new Map(Object.entries(t))}get(t,s){let n=this.m.get(t);return n!==void 0?n:s}getBoolean(t,s=!1){let n=this.m.get(t);return n===void 0?s:typeof n=="string"?n==="true":!!n}getNumber(t,s){let n=parseFloat(this.m.get(t));return isNaN(n)?s!==void 0?s:NaN:n}set(t,s){this.m.set(t,s)}},N=new be,zt=e=>{try{let t=e.sessionStorage.getItem(it);return t!==null?JSON.parse(t):{}}catch{return{}}},Ht=(e,t)=>{try{e.sessionStorage.setItem(it,JSON.stringify(t))}catch{return}},Ut=e=>{let t={};return e.location.search.slice(1).split("&").map(s=>s.split("=")).map(([s,n])=>{try{return[decodeURIComponent(s),decodeURIComponent(n)]}catch{return["",""]}}).filter(([s])=>Wt(s,He)).map(([s,n])=>[s.slice(He.length),n]).forEach(([s,n])=>{t[s]=n}),t},Wt=(e,t)=>e.substr(0,t.length)===t,He="ionic:",it="ionic-persist-config",q=function(e){return e.OFF="OFF",e.ERROR="ERROR",e.WARN="WARN",e}(q||{}),Ft=(e,...t)=>{let s=N.get("logLevel",q.WARN);if([q.WARN].includes(s))return console.warn(`[Ionic Warning]: ${e}`,...t)},Nn=(e,...t)=>{let s=N.get("logLevel",q.ERROR);if([q.ERROR,q.WARN].includes(s))return console.error(`[Ionic Error]: ${e}`,...t)},kn=(e,...t)=>console.error(`<${e.tagName.toLowerCase()}> must be used inside ${t.join(" or ")}.`),qt=e=>ot(e),Gt=(e,t)=>(typeof e=="string"&&(t=e,e=void 0),qt(e).includes(t)),ot=(e=window)=>{if(typeof e>"u")return[];e.Ionic=e.Ionic||{};let t=e.Ionic.platforms;return t==null&&(t=e.Ionic.platforms=Yt(e),t.forEach(s=>e.document.documentElement.classList.add(`plt-${s}`))),t},Yt=e=>{let t=N.get("platform");return Object.keys(Ue).filter(s=>{let n=t?.[s];return typeof n=="function"?n(e):Ue[s](e)})},Xt=e=>de(e)&&!at(e),ke=e=>!!(P(e,/iPad/i)||P(e,/Macintosh/i)&&de(e)),Jt=e=>P(e,/iPhone/i),Kt=e=>P(e,/iPhone|iPod/i)||ke(e),lt=e=>P(e,/android|sink/i),Zt=e=>lt(e)&&!P(e,/mobile/i),Qt=e=>{let t=e.innerWidth,s=e.innerHeight,n=Math.min(t,s),r=Math.max(t,s);return n>390&&n<520&&r>620&&r<800},Vt=e=>{let t=e.innerWidth,s=e.innerHeight,n=Math.min(t,s),r=Math.max(t,s);return ke(e)||Zt(e)||n>460&&n<820&&r>780&&r<1400},de=e=>ns(e,"(any-pointer:coarse)"),es=e=>!de(e),at=e=>ct(e)||$t(e),ct=e=>!!(e.cordova||e.phonegap||e.PhoneGap),$t=e=>{let t=e.Capacitor;return!!(t?.isNative||t?.isNativePlatform&&t.isNativePlatform())},ts=e=>P(e,/electron/i),ss=e=>{var t;return!!(!((t=e.matchMedia)===null||t===void 0)&&t.call(e,"(display-mode: standalone)").matches||e.navigator.standalone)},P=(e,t)=>t.test(e.navigator.userAgent),ns=(e,t)=>{var s;return(s=e.matchMedia)===null||s===void 0?void 0:s.call(e,t).matches},Ue={ipad:ke,iphone:Jt,ios:Kt,android:lt,phablet:Qt,tablet:Vt,cordova:ct,capacitor:$t,electron:ts,pwa:ss,mobile:de,mobileweb:Xt,desktop:es,hybrid:at},U,jn=e=>e&&rn(e)||U,rs=(e={})=>{if(typeof window>"u")return;let t=window.document,s=window,n=s.Ionic=s.Ionic||{},r=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},zt(s)),{persistConfig:!1}),n.config),Ut(s)),e);N.reset(r),N.getBoolean("persistConfig")&&Ht(s,r),ot(s),n.config=N,n.mode=U=N.get("mode",t.documentElement.getAttribute("mode")||(Gt(s,"ios")?"ios":"md")),N.set("mode",U),t.documentElement.setAttribute("mode",U),t.documentElement.classList.add(U),N.getBoolean("_testing")&&N.set("animated",!1);let i=o=>{var a;return(a=o.tagName)===null||a===void 0?void 0:a.startsWith("ION-")},c=o=>["ios","md"].includes(o);nn(o=>{for(;o;){let a=o.mode||o.getAttribute("mode");if(a){if(c(a))return a;i(o)&&Ft('Invalid ionic mode: "'+a+'", expected: "ios" or "md"')}o=o.parentElement}return U})},Rn=rs,is="",os=Object.defineProperty,ls=(e,t)=>{for(var s in t)os(e,s,{get:t[s],enumerable:!0})},Cn={isBrowser:!0},as="http://www.w3.org/2000/svg",cs="http://www.w3.org/1999/xhtml",ft=(e=>(e.Undefined="undefined",e.Null="null",e.String="string",e.Number="number",e.SpecialNumber="number",e.Boolean="boolean",e.BigInt="bigint",e))(ft||{}),dt=(e=>(e.Array="array",e.Date="date",e.Map="map",e.Object="object",e.RegularExpression="regexp",e.Set="set",e.Channel="channel",e.Symbol="symbol",e))(dt||{}),ye="type",Se="value",me="serialized:",T=e=>{if(e.__stencil__getHostRef)return e.__stencil__getHostRef()},wn=(e,t)=>{e.__stencil__getHostRef=()=>t,t.$lazyInstance$=e},$s=(e,t)=>{let s={$flags$:0,$hostElement$:e,$cmpMeta$:t,$instanceValues$:new Map};s.$onInstancePromise$=new Promise(r=>s.$onInstanceResolve$=r),s.$onReadyPromise$=new Promise(r=>s.$onReadyResolve$=r),e["s-p"]=[],e["s-rc"]=[];let n=s;return e.__stencil__getHostRef=()=>n,n},We=(e,t)=>t in e,z=(e,t)=>(0,console.error)(e,t),Fe=new Map,fs=(e,t,s)=>{let n=e.$tagName$.replace(/-/g,"_"),r=e.$lazyBundleId$;if(!r)return;let i=Fe.get(r);if(i)return i[n];return Pt(`./${r}.entry.js`).then(c=>(Fe.set(r,c),c[n]),c=>{z(c,t.$hostElement$)})},re=new Map,ut=[],ds="r",us="o",hs="s",ps="t",gs="c",K="s-id",ie="sty-id",qe="c-id",vs="{visibility:hidden}.hydrated{visibility:inherit}",ht="slot-fb{display:contents}slot-fb[hidden]{display:none}",Ge="http://www.w3.org/1999/xlink",v=typeof window<"u"?window:{},Dn=v.HTMLElement||class{},p={$flags$:0,$resourcesUrl$:"",jmp:e=>e(),raf:e=>requestAnimationFrame(e),ael:(e,t,s,n)=>e.addEventListener(t,s,n),rel:(e,t,s,n)=>e.removeEventListener(t,s,n),ce:(e,t)=>new CustomEvent(e,t)},ys=Y.shadowDom,Ss=(()=>{var e;let t=!1;try{(e=v.document)==null||e.addEventListener("e",null,Object.defineProperty({},"passive",{get(){t=!0}}))}catch{}return t})(),_s=e=>Promise.resolve(e),je=(()=>{try{return new CSSStyleSheet,typeof new CSSStyleSheet().replaceSync=="function"}catch{}return!1})(),xe=!1,Ie=[],pt=[],gt=(e,t)=>s=>{e.push(s),xe||(xe=!0,t&&p.$flags$&4?Re(Ee):p.raf(Ee))},Ye=e=>{for(let t=0;t<e.length;t++)try{e[t](performance.now())}catch(s){z(s)}e.length=0},Ee=()=>{Ye(Ie),Ye(pt),(xe=Ie.length>0)&&p.raf(Ee)},Re=e=>_s().then(e),Bn=gt(Ie,!1),bs=gt(pt,!0),Pn=e=>{let t=new URL(e,p.$resourcesUrl$);return t.origin!==v.location.origin?t.href:t.pathname},ms=e=>e!=null&&e!==void 0,Ce=e=>(e=typeof e,e==="object"||e==="function");function vt(e){var t,s,n;return(n=(s=(t=e.head)==null?void 0:t.querySelector('meta[name="csp-nonce"]'))==null?void 0:s.getAttribute("content"))!=null?n:void 0}var xs=e=>e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),Is=class B{static fromLocalValue(t){let s=t[ye],n=Se in t?t[Se]:void 0;switch(s){case"string":return n;case"boolean":return n;case"bigint":return BigInt(n);case"undefined":return;case"null":return null;case"number":return n==="NaN"?NaN:n==="-0"?-0:n==="Infinity"?1/0:n==="-Infinity"?-1/0:n;case"array":return n.map(l=>B.fromLocalValue(l));case"date":return new Date(n);case"map":let r=new Map;for(let[l,$]of n){let d=typeof l=="object"&&l!==null?B.fromLocalValue(l):l,f=B.fromLocalValue($);r.set(d,f)}return r;case"object":let i={};for(let[l,$]of n)i[l]=B.fromLocalValue($);return i;case"regexp":let{pattern:c,flags:o}=n;return new RegExp(c,o);case"set":let a=new Set;for(let l of n)a.add(B.fromLocalValue(l));return a;case"symbol":return Symbol(n);default:throw new Error(`Unsupported type: ${s}`)}}static fromLocalValueArray(t){return t.map(s=>B.fromLocalValue(s))}static isLocalValueObject(t){if(typeof t!="object"||t===null||!t.hasOwnProperty(ye))return!1;let s=t[ye];return Object.values(H(H({},ft),dt)).includes(s)?s!=="null"&&s!=="undefined"?t.hasOwnProperty(Se):!0:!1}},Es={};ls(Es,{err:()=>yt,map:()=>Ts,ok:()=>Te,unwrap:()=>Ls,unwrapErr:()=>As});var Te=e=>({isOk:!0,isErr:!1,value:e}),yt=e=>({isOk:!1,isErr:!0,value:e});function Ts(e,t){if(e.isOk){let s=t(e.value);return s instanceof Promise?s.then(n=>Te(n)):Te(s)}if(e.isErr){let s=e.value;return yt(s)}throw"should never get here"}var Ls=e=>{if(e.isOk)return e.value;throw e.value},As=e=>{if(e.isErr)return e.value;throw e.value};function Os(e){return typeof e!="string"||!e.startsWith(me)?e:Is.fromLocalValue(JSON.parse(atob(e.slice(me.length))))}function Ns(e){let t=this.attachShadow({mode:"open",delegatesFocus:!!(e.$flags$&16)});if(je){let s=new CSSStyleSheet;s.replaceSync(is),t.adoptedStyleSheets.push(s)}}var ue=e=>{let t=L(e,"childNodes");e.tagName&&e.tagName.includes("-")&&e["s-cr"]&&e.tagName!=="SLOT-FB"&&V(t,e.tagName).forEach(n=>{n.nodeType===1&&n.tagName==="SLOT-FB"&&(we(n,X(n),!1).length?n.hidden=!0:n.hidden=!1)});let s=0;for(s=0;s<t.length;s++){let n=t[s];n.nodeType===1&&L(n,"childNodes").length&&ue(n)}},Z=e=>{let t=[];for(let s=0;s<e.length;s++){let n=e[s]["s-nr"]||void 0;n&&n.isConnected&&t.push(n)}return t};function V(e,t,s){let n=0,r=[],i;for(;n<e.length;n++){if(i=e[n],i["s-sr"]&&(!t||i["s-hn"]===t)&&(s===void 0||X(i)===s)&&(r.push(i),typeof s<"u"))return r;r=[...r,...V(i.childNodes,t,s)]}return r}var we=(e,t,s=!0)=>{let n=[];(s&&e["s-sr"]||!e["s-sr"])&&n.push(e);let r=e;for(;r=r.nextSibling;)X(r)===t&&(s||!r["s-sr"])&&n.push(r);return n},Xe=(e,t)=>e.nodeType===1?e.getAttribute("slot")===null&&t===""||e.getAttribute("slot")===t:e["s-sn"]===t?!0:t==="",he=(e,t,s,n)=>{if(e["s-ol"]&&e["s-ol"].isConnected)return;let r=document.createTextNode("");if(r["s-nr"]=e,!t["s-cr"]||!t["s-cr"].parentNode)return;let i=t["s-cr"].parentNode,c=s?L(i,"prepend"):L(i,"appendChild");if(typeof n<"u"){r["s-oo"]=n;let o=L(i,"childNodes"),a=[r];o.forEach(l=>{l["s-nr"]&&a.push(l)}),a.sort((l,$)=>!l["s-oo"]||l["s-oo"]<($["s-oo"]||0)?-1:!$["s-oo"]||$["s-oo"]<l["s-oo"]?1:0),a.forEach(l=>c.call(i,l))}else c.call(i,r);e["s-ol"]=r,e["s-sh"]=t["s-hn"]},X=e=>typeof e["s-sn"]=="string"?e["s-sn"]:e.nodeType===1&&e.getAttribute("slot")||void 0;function St(e){if(e.assignedElements||e.assignedNodes||!e["s-sr"])return;let t=s=>(function(n){let r=[],i=this["s-sn"];n?.flatten&&console.error(`
          Flattening is not supported for Stencil non-shadow slots. 
          You can use \`.childNodes\` to nested slot fallback content.
          If you have a particular use case, please open an issue on the Stencil repo.
        `);let c=this["s-cr"].parentElement;return(c.__childNodes?c.childNodes:Z(c.childNodes)).forEach(a=>{i===X(a)&&r.push(a)}),s?r.filter(a=>a.nodeType===1):r}).bind(e);e.assignedElements=t(!0),e.assignedNodes=t(!1)}function pe(e){e.dispatchEvent(new CustomEvent("slotchange",{bubbles:!1,cancelable:!1,composed:!1}))}function De(e,t){var s;if(t=t||((s=e["s-ol"])==null?void 0:s.parentElement),!t)return{slotNode:null,slotName:""};let n=e["s-sn"]=X(e)||"",r=L(t,"childNodes");return{slotNode:V(r,t.tagName,n)[0],slotName:n}}var ks=e=>{js(e),Rs(e),Ds(e),ws(e),zs(e),Bs(e),Ps(e),Ms(e),Hs(e),Us(e),Cs(e)},js=e=>{let t=e.cloneNode;e.cloneNode=function(s){let n=this,r=n.shadowRoot&&ys,i=t.call(n,r?s:!1);if(!r&&s){let c=0,o,a,l=["s-id","s-cr","s-lr","s-rc","s-sc","s-p","s-cn","s-sr","s-sn","s-hn","s-ol","s-nr","s-si","s-rf","s-scs"],$=this.__childNodes||this.childNodes;for(;c<$.length;c++)o=$[c]["s-nr"],a=l.every(d=>!$[c][d]),o&&(i.__appendChild?i.__appendChild(o.cloneNode(!0)):i.appendChild(o.cloneNode(!0))),a&&i.appendChild($[c].cloneNode(!0))}return i}},Rs=e=>{e.__appendChild=e.appendChild,e.appendChild=function(t){let{slotName:s,slotNode:n}=De(t,this);if(n){he(t,n);let r=we(n,s),i=r[r.length-1],c=L(i,"parentNode"),o=L(c,"insertBefore")(t,i.nextSibling);return pe(n),ue(this),o}return this.__appendChild(t)}},Cs=e=>{e.__removeChild=e.removeChild,e.removeChild=function(t){if(t&&typeof t["s-sn"]<"u"){let s=this.__childNodes||this.childNodes;if(V(s,this.tagName,t["s-sn"])&&t.isConnected){t.remove(),ue(this);return}}return this.__removeChild(t)}},ws=e=>{e.__prepend=e.prepend,e.prepend=function(...t){t.forEach(s=>{typeof s=="string"&&(s=this.ownerDocument.createTextNode(s));let n=(s["s-sn"]=X(s))||"",r=L(this,"childNodes"),i=V(r,this.tagName,n)[0];if(i){he(s,i,!0);let o=we(i,n)[0],a=L(o,"parentNode"),l=L(a,"insertBefore")(s,L(o,"nextSibling"));return pe(i),l}return s.nodeType===1&&s.getAttribute("slot")&&(s.hidden=!0),e.__prepend(s)})}},Ds=e=>{e.__append=e.append,e.append=function(...t){t.forEach(s=>{typeof s=="string"&&(s=this.ownerDocument.createTextNode(s)),this.appendChild(s)})}},Bs=e=>{let t=e.insertAdjacentHTML;e.insertAdjacentHTML=function(s,n){if(s!=="afterbegin"&&s!=="beforeend")return t.call(this,s,n);let r=this.ownerDocument.createElement("_"),i;if(r.innerHTML=n,s==="afterbegin")for(;i=r.firstChild;)this.prepend(i);else if(s==="beforeend")for(;i=r.firstChild;)this.append(i)}},Ps=e=>{e.insertAdjacentText=function(t,s){this.insertAdjacentHTML(t,s)}},Ms=e=>{let t=e;t.__insertBefore||(t.__insertBefore=e.insertBefore,e.insertBefore=function(s,n){let{slotName:r,slotNode:i}=De(s,this),c=this.__childNodes?this.childNodes:Z(this.childNodes);if(i){let a=!1;if(c.forEach(l=>{if(l===n||n===null){if(a=!0,n===null||r!==n["s-sn"]){this.appendChild(s);return}if(r===n["s-sn"]){he(s,i);let $=L(n,"parentNode");L($,"insertBefore")(s,n),pe(i)}return}}),a)return s}let o=n?.__parentNode;return o&&!this.isSameNode(o)?this.appendChild(s):this.__insertBefore(s,n)})},zs=e=>{let t=e.insertAdjacentElement;e.insertAdjacentElement=function(s,n){return s!=="afterbegin"&&s!=="beforeend"?t.call(this,s,n):s==="afterbegin"?(this.prepend(n),n):(s==="beforeend"&&this.append(n),n)}},Hs=e=>{k("textContent",e),Object.defineProperty(e,"textContent",{get:function(){let t="";return(this.__childNodes?this.childNodes:Z(this.childNodes)).forEach(n=>t+=n.textContent||""),t},set:function(t){(this.__childNodes?this.childNodes:Z(this.childNodes)).forEach(n=>{n["s-ol"]&&n["s-ol"].remove(),n.remove()}),this.insertAdjacentHTML("beforeend",t)}})},Us=e=>{class t extends Array{item(n){return this[n]}}k("children",e),Object.defineProperty(e,"children",{get(){return this.childNodes.filter(s=>s.nodeType===1)}}),Object.defineProperty(e,"childElementCount",{get(){return this.children.length}}),k("firstChild",e),Object.defineProperty(e,"firstChild",{get(){return this.childNodes[0]}}),k("lastChild",e),Object.defineProperty(e,"lastChild",{get(){return this.childNodes[this.childNodes.length-1]}}),k("childNodes",e),Object.defineProperty(e,"childNodes",{get(){let s=new t;return s.push(...Z(this.__childNodes)),s}})},Ws=e=>{!e||e.__nextSibling!==void 0||!globalThis.Node||(Fs(e),Gs(e),_t(e),e.nodeType===Node.ELEMENT_NODE&&(qs(e),Ys(e)))},Fs=e=>{!e||e.__nextSibling||(k("nextSibling",e),Object.defineProperty(e,"nextSibling",{get:function(){var t;let s=(t=this["s-ol"])==null?void 0:t.parentNode.childNodes,n=s?.indexOf(this);return s&&n>-1?s[n+1]:this.__nextSibling}}))},qs=e=>{!e||e.__nextElementSibling||(k("nextElementSibling",e),Object.defineProperty(e,"nextElementSibling",{get:function(){var t;let s=(t=this["s-ol"])==null?void 0:t.parentNode.children,n=s?.indexOf(this);return s&&n>-1?s[n+1]:this.__nextElementSibling}}))},Gs=e=>{!e||e.__previousSibling||(k("previousSibling",e),Object.defineProperty(e,"previousSibling",{get:function(){var t;let s=(t=this["s-ol"])==null?void 0:t.parentNode.childNodes,n=s?.indexOf(this);return s&&n>-1?s[n-1]:this.__previousSibling}}))},Ys=e=>{!e||e.__previousElementSibling||(k("previousElementSibling",e),Object.defineProperty(e,"previousElementSibling",{get:function(){var t;let s=(t=this["s-ol"])==null?void 0:t.parentNode.children,n=s?.indexOf(this);return s&&n>-1?s[n-1]:this.__previousElementSibling}}))},_t=e=>{!e||e.__parentNode||(k("parentNode",e),Object.defineProperty(e,"parentNode",{get:function(){var t;return((t=this["s-ol"])==null?void 0:t.parentNode)||this.__parentNode},set:function(t){this.__parentNode=t}}))},Xs=["children","nextElementSibling","previousElementSibling"],Js=["childNodes","firstChild","lastChild","nextSibling","previousSibling","textContent","parentNode"];function k(e,t){let s;Xs.includes(e)?s=Object.getOwnPropertyDescriptor(Element.prototype,e):Js.includes(e)&&(s=Object.getOwnPropertyDescriptor(Node.prototype,e)),s||(s=Object.getOwnPropertyDescriptor(t,e)),s&&Object.defineProperty(t,"__"+e,s)}function L(e,t){if("__"+t in e){let s=e["__"+t];return typeof s!="function"?s:s.bind(e)}else return typeof e[t]!="function"?e[t]:e[t].bind(e)}var j=(e,t="")=>()=>{},Ks=(e,t)=>()=>{},bt=(e,t,...s)=>{let n=null,r=null,i=null,c=!1,o=!1,a=[],l=d=>{for(let f=0;f<d.length;f++)n=d[f],Array.isArray(n)?l(n):n!=null&&typeof n!="boolean"&&((c=typeof e!="function"&&!Ce(n))&&(n=String(n)),c&&o?a[a.length-1].$text$+=n:a.push(c?G(null,n):n),o=c)};if(l(s),t){t.key&&(r=t.key),t.name&&(i=t.name);{let d=t.className||t.class;d&&(t.class=typeof d!="object"?d:Object.keys(d).filter(f=>d[f]).join(" "))}}if(typeof e=="function")return e(t===null?{}:t,a,Vs);let $=G(e,null);return $.$attrs$=t,a.length>0&&($.$children$=a),$.$key$=r,$.$name$=i,$},G=(e,t)=>{let s={$flags$:0,$tag$:e,$text$:t,$elm$:null,$children$:null};return s.$attrs$=null,s.$key$=null,s.$name$=null,s},Zs={},Qs=e=>e&&e.$tag$===Zs,Vs={forEach:(e,t)=>e.map(Je).forEach(t),map:(e,t)=>e.map(Je).map(t).map(en)},Je=e=>({vattrs:e.$attrs$,vchildren:e.$children$,vkey:e.$key$,vname:e.$name$,vtag:e.$tag$,vtext:e.$text$}),en=e=>{if(typeof e.vtag=="function"){let s=H({},e.vattrs);return e.vkey&&(s.key=e.vkey),e.vname&&(s.name=e.vname),bt(e.vtag,s,...e.vchildren||[])}let t=G(e.vtag,e.vtext);return t.$attrs$=e.vattrs,t.$children$=e.vchildren,t.$key$=e.vkey,t.$name$=e.vname,t},tn=(e,t,s,n)=>{var r;let i=j("hydrateClient",t),c=e.shadowRoot,o=[],a=[],l=[],$=c?[]:null,d=G(t,null);d.$elm$=e,Object.entries(((r=n.$cmpMeta$)==null?void 0:r.$members$)||{}).forEach(([b,[A,I]])=>{var ee;if(!(A&31))return;let Dt=I||b,Me=e.getAttribute(Dt);if(Me!==null){let Bt=oe(Me,A);(ee=n?.$instanceValues$)==null||ee.set(b,Bt)}});let h;{let b=n.$cmpMeta$;b&&b.$flags$&10&&e["s-sc"]?(h=e["s-sc"],e.classList.add(h+"-h")):e["s-sc"]&&delete e["s-sc"]}v.document&&(!p.$orgLocNodes$||!p.$orgLocNodes$.size)&&Ae(v.document.body,p.$orgLocNodes$=new Map),e[K]=s,e.removeAttribute(K),n.$vnode$=Le(d,o,a,$,e,e,s,l);let u=0,S=o.length,g;for(u;u<S;u++){g=o[u];let b=g.$hostId$+"."+g.$nodeId$,A=p.$orgLocNodes$.get(b),I=g.$elm$;c||(I["s-hn"]=t.toUpperCase(),g.$tag$==="slot"&&(I["s-cr"]=e["s-cr"])),g.$tag$==="slot"&&(g.$name$=g.$elm$["s-sn"]||g.$elm$.name||null,g.$children$?(g.$flags$|=2,g.$elm$.childNodes.length||g.$children$.forEach(ee=>{g.$elm$.appendChild(ee.$elm$)})):g.$flags$|=1),A&&A.isConnected&&(c&&A["s-en"]===""&&A.parentNode.insertBefore(I,A.nextSibling),A.parentNode.removeChild(A),c||(I["s-oo"]=parseInt(g.$nodeId$))),p.$orgLocNodes$.delete(b)}let y=[],O=l.length,R=0,m,_,J,x;for(R;R<O;R++)if(m=l[R],!(!m||!m.length))for(J=m.length,_=0,_;_<J;_++){if(x=m[_],y[x.hostId]||(y[x.hostId]=p.$orgLocNodes$.get(x.hostId)),!y[x.hostId])continue;let b=y[x.hostId];(!b.shadowRoot||!c)&&(x.slot["s-cr"]=b["s-cr"],!x.slot["s-cr"]&&b.shadowRoot?x.slot["s-cr"]=b:x.slot["s-cr"]=(b.__childNodes||b.childNodes)[0],he(x.node,x.slot,!1,x.node["s-oo"]),Ws(x.node)),b.shadowRoot&&x.node.parentElement!==b&&b.appendChild(x.node)}if(h&&a.length&&a.forEach(b=>{b.$elm$.parentElement.classList.add(h+"-s")}),c&&!c.childNodes.length){let b=0,A=$.length;if(A){for(b;b<A;b++)c.appendChild($[b]);Array.from(e.childNodes).forEach(I=>{typeof I["s-sn"]!="string"&&(I.nodeType===1&&I.slot&&I.hidden?I.removeAttribute("hidden"):(I.nodeType===8||I.nodeType===3&&!I.wholeText.trim())&&I.parentNode.removeChild(I))})}}p.$orgLocNodes$.delete(e["s-id"]),n.$hostElement$=e,i()},Le=(e,t,s,n,r,i,c,o=[])=>{let a,l,$,d,f=r["s-sc"];if(i.nodeType===1){if(a=i.getAttribute(qe),a&&(l=a.split("."),l[0]===c||l[0]==="0")){$=Ke({$flags$:0,$hostId$:l[0],$nodeId$:l[1],$depth$:l[2],$index$:l[3],$tag$:i.tagName.toLowerCase(),$elm$:i,$attrs$:{class:i.className||""}}),t.push($),i.removeAttribute(qe),e.$children$||(e.$children$=[]),f&&(i["s-si"]=f,$.$attrs$.class+=" "+f);let u=$.$elm$.getAttribute("s-sn");typeof u=="string"&&($.$tag$==="slot-fb"&&(Ze(u,l[2],$,i,e,t,s,n,o),f&&i.classList.add(f)),$.$elm$["s-sn"]=u,$.$elm$.removeAttribute("s-sn")),$.$index$!==void 0&&(e.$children$[$.$index$]=$),e=$,n&&$.$depth$==="0"&&(n[$.$index$]=$.$elm$)}if(i.shadowRoot)for(d=i.shadowRoot.childNodes.length-1;d>=0;d--)Le(e,t,s,n,r,i.shadowRoot.childNodes[d],c,o);let h=i.__childNodes||i.childNodes;for(d=h.length-1;d>=0;d--)Le(e,t,s,n,r,h[d],c,o)}else if(i.nodeType===8){if(l=i.nodeValue.split("."),l[1]===c||l[1]==="0"){if(a=l[0],$=Ke({$hostId$:l[1],$nodeId$:l[2],$depth$:l[3],$index$:l[4]||"0",$elm$:i,$attrs$:null,$children$:null,$key$:null,$name$:null,$tag$:null,$text$:null}),a===ps)$.$elm$=Ve(i,3),$.$elm$&&$.$elm$.nodeType===3&&($.$text$=$.$elm$.textContent,t.push($),i.remove(),c===$.$hostId$&&(e.$children$||(e.$children$=[]),e.$children$[$.$index$]=$),n&&$.$depth$==="0"&&(n[$.$index$]=$.$elm$));else if(a===gs)$.$elm$=Ve(i,8),$.$elm$&&$.$elm$.nodeType===8&&(t.push($),i.remove());else if($.$hostId$===c)if(a===hs){let h=i["s-sn"]=l[5]||"";Ze(h,l[2],$,i,e,t,s,n,o)}else a===ds&&(n?i.remove():(r["s-cr"]=i,i["s-cn"]=!0))}}else if(e&&e.$tag$==="style"){let h=G(null,i.textContent);h.$elm$=i,h.$index$="0",e.$children$=[h]}else i.nodeType===3&&!i.wholeText.trim()&&i.remove();return e},Ae=(e,t)=>{if(e.nodeType===1){let s=e[K]||e.getAttribute(K);s&&t.set(s,e);let n=0;if(e.shadowRoot)for(;n<e.shadowRoot.childNodes.length;n++)Ae(e.shadowRoot.childNodes[n],t);let r=e.__childNodes||e.childNodes;for(n=0;n<r.length;n++)Ae(r[n],t)}else if(e.nodeType===8){let s=e.nodeValue.split(".");s[0]===us&&(t.set(s[1]+"."+s[2],e),e.nodeValue="",e["s-en"]=s[3])}},Ke=e=>H(H({},{$flags$:0,$hostId$:null,$nodeId$:null,$depth$:null,$index$:"0",$elm$:null,$attrs$:null,$children$:null,$key$:null,$name$:null,$tag$:null,$text$:null}),e);function Ze(e,t,s,n,r,i,c,o,a){n["s-sr"]=!0,s.$name$=e||null,s.$tag$="slot";let l=r?.$elm$?r.$elm$["s-id"]||r.$elm$.getAttribute("s-id"):"";if(o&&v.document){let $=s.$elm$=v.document.createElement(s.$tag$);s.$name$&&s.$elm$.setAttribute("name",e),l&&l!==s.$hostId$?r.$elm$.insertBefore($,r.$elm$.children[0]):n.parentNode.insertBefore(s.$elm$,n),Qe(a,t,e,n,s.$hostId$),n.remove(),s.$depth$==="0"&&(o[s.$index$]=s.$elm$)}else{let $=s.$elm$,d=l&&l!==s.$hostId$&&r.$elm$.shadowRoot;Qe(a,t,e,n,d?l:s.$hostId$),St(n),d&&r.$elm$.insertBefore($,r.$elm$.children[0]),i.push(s)}c.push(s),r.$children$||(r.$children$=[]),r.$children$[s.$index$]=s}var Qe=(e,t,s,n,r)=>{let i=n.nextSibling;for(e[t]=e[t]||[];i&&((i.getAttribute&&i.getAttribute("slot")||i["s-sn"])===s||s===""&&!i["s-sn"]&&(i.nodeType===8&&i.nodeValue.indexOf(".")!==1||i.nodeType===3));)i["s-sn"]=s,e[t].push({slot:n,node:i,hostId:r}),i=i.nextSibling},Ve=(e,t)=>{let s=e;do s=s.nextSibling;while(s&&(s.nodeType!==t||!s.nodeValue));return s},Be=e=>{let t=xs(e);return new RegExp(`(^|[^@]|@(?!supports\\s+selector\\s*\\([^{]*?${t}))(${t}\\b)`,"g")};Be("::slotted");Be(":host");Be(":host-context");var sn=e=>ut.map(t=>t(e)).find(t=>!!t),nn=e=>ut.push(e),rn=e=>T(e).$modeName$,oe=(e,t)=>{if(typeof e=="string"&&(e.startsWith("{")&&e.endsWith("}")||e.startsWith("[")&&e.endsWith("]")))try{return e=JSON.parse(e),e}catch{}return typeof e=="string"&&e.startsWith(me)?(e=Os(e),e):e!=null&&!Ce(e)?t&4?e==="false"?!1:e===""||!!e:t&2?typeof e=="string"?parseFloat(e):typeof e=="number"?e:NaN:t&1?String(e):e:e},on=e=>T(e).$hostElement$,Mn=(e,t,s)=>{let n=on(e);return{emit:r=>mt(n,t,{bubbles:!!(s&4),composed:!!(s&2),cancelable:!!(s&1),detail:r})}},mt=(e,t,s)=>{let n=p.ce(t,s);return e.dispatchEvent(n),n},F=new WeakMap,xt=(e,t,s)=>{let n=re.get(e);je&&s?(n=n||new CSSStyleSheet,typeof n=="string"?n=t:n.replaceSync(t)):n=t,re.set(e,n)},It=(e,t,s)=>{var n;let r=Pe(t,s),i=re.get(r);if(!v.document)return r;if(e=e.nodeType===11?e:v.document,i)if(typeof i=="string"){e=e.head||e;let c=F.get(e),o;if(c||F.set(e,c=new Set),!c.has(r)){if(e.host&&(o=e.querySelector(`[${ie}="${r}"]`)))o.innerHTML=i;else{o=document.querySelector(`[${ie}="${r}"]`)||v.document.createElement("style"),o.innerHTML=i;let a=(n=p.$nonce$)!=null?n:vt(v.document);if(a!=null&&o.setAttribute("nonce",a),!(t.$flags$&1))if(e.nodeName==="HEAD"){let l=e.querySelectorAll("link[rel=preconnect]"),$=l.length>0?l[l.length-1].nextSibling:e.querySelector("style");e.insertBefore(o,$?.parentNode===e?$:null)}else if("host"in e)if(je){let l=new CSSStyleSheet;l.replaceSync(i),e.adoptedStyleSheets=[l,...e.adoptedStyleSheets]}else{let l=e.querySelector("style");l?l.innerHTML=i+l.innerHTML:e.prepend(o)}else e.append(o);t.$flags$&1&&e.insertBefore(o,null)}t.$flags$&4&&(o.innerHTML+=ht),c&&c.add(r)}}else e.adoptedStyleSheets.includes(i)||(e.adoptedStyleSheets=[...e.adoptedStyleSheets,i]);return r},ln=e=>{let t=e.$cmpMeta$,s=e.$hostElement$,n=t.$flags$,r=j("attachStyles",t.$tagName$),i=It(s.shadowRoot?s.shadowRoot:s.getRootNode(),t,e.$modeName$);n&10&&(s["s-sc"]=i,s.classList.add(i+"-h")),r()},Pe=(e,t)=>"sc-"+(t&&e.$flags$&32?e.$tagName$+"-"+t:e.$tagName$),an=e=>e.replace(/\/\*!@([^\/]+)\*\/[^\{]+\{/g,"$1{"),cn=()=>{if(!v.document)return;let e=v.document.querySelectorAll(`[${ie}]`),t=0;for(;t<e.length;t++)xt(e[t].getAttribute(ie),an(e[t].innerHTML),!0)},et=(e,t,s,n,r,i,c)=>{if(s===n)return;let o=We(e,t),a=t.toLowerCase();if(t==="class"){let l=e.classList,$=tt(s),d=tt(n);e["s-si"]&&c?(d.push(e["s-si"]),$.forEach(f=>{f.startsWith(e["s-si"])&&d.push(f)}),d=[...new Set(d)],l.add(...d)):(l.remove(...$.filter(f=>f&&!d.includes(f))),l.add(...d.filter(f=>f&&!$.includes(f))))}else if(t==="style"){for(let l in s)(!n||n[l]==null)&&(l.includes("-")?e.style.removeProperty(l):e.style[l]="");for(let l in n)(!s||n[l]!==s[l])&&(l.includes("-")?e.style.setProperty(l,n[l]):e.style[l]=n[l])}else if(t!=="key")if(t==="ref")n&&n(e);else if(!o&&t[0]==="o"&&t[1]==="n"){if(t[2]==="-"?t=t.slice(3):We(v,a)?t=a.slice(2):t=a[2]+t.slice(3),s||n){let l=t.endsWith(Et);t=t.replace(fn,""),s&&p.rel(e,t,s,l),n&&p.ael(e,t,n,l)}}else{let l=Ce(n);if((o||l&&n!==null)&&!r)try{if(e.tagName.includes("-"))e[t]!==n&&(e[t]=n);else{let d=n??"";t==="list"?o=!1:(s==null||e[t]!=d)&&(typeof e.__lookupSetter__(t)=="function"?e[t]=d:e.setAttribute(t,d))}}catch{}let $=!1;a!==(a=a.replace(/^xlink\:?/,""))&&(t=a,$=!0),n==null||n===!1?(n!==!1||e.getAttribute(t)==="")&&($?e.removeAttributeNS(Ge,t):e.removeAttribute(t)):(!o||i&4||r)&&!l&&e.nodeType===1&&(n=n===!0?"":n,$?e.setAttributeNS(Ge,t,n):e.setAttribute(t,n))}},$n=/\s/,tt=e=>(typeof e=="object"&&e&&"baseVal"in e&&(e=e.baseVal),!e||typeof e!="string"?[]:e.split($n)),Et="Capture",fn=new RegExp(Et+"$"),Oe=(e,t,s,n)=>{let r=t.$elm$.nodeType===11&&t.$elm$.host?t.$elm$.host:t.$elm$,i=e&&e.$attrs$||{},c=t.$attrs$||{};for(let o of st(Object.keys(i)))o in c||et(r,o,i[o],void 0,s,t.$flags$,n);for(let o of st(Object.keys(c)))et(r,o,i[o],c[o],s,t.$flags$,n)};function st(e){return e.includes("ref")?[...e.filter(t=>t!=="ref"),"ref"]:e}var ne,le,M,ae=!1,ce=!1,ge=!1,E=!1,$e=(e,t,s)=>{var n;let r=t.$children$[s],i=0,c,o,a;if(ae||(ge=!0,r.$tag$==="slot"&&(r.$flags$|=r.$children$?2:1)),r.$text$!==null)c=r.$elm$=v.document.createTextNode(r.$text$);else if(r.$flags$&1)c=r.$elm$=v.document.createTextNode(""),Oe(null,r,E);else{if(E||(E=r.$tag$==="svg"),!v.document)throw new Error("You are trying to render a Stencil component in an environment that doesn't support the DOM. Make sure to populate the [`window`](https://developer.mozilla.org/en-US/docs/Web/API/Window/window) object before rendering a component.");if(c=r.$elm$=v.document.createElementNS(E?as:cs,!ae&&Y.slotRelocation&&r.$flags$&2?"slot-fb":r.$tag$),E&&r.$tag$==="foreignObject"&&(E=!1),Oe(null,r,E),ms(ne)&&c["s-si"]!==ne&&c.classList.add(c["s-si"]=ne),r.$children$)for(i=0;i<r.$children$.length;++i)o=$e(e,r,i),o&&c.appendChild(o);r.$tag$==="svg"?E=!1:c.tagName==="foreignObject"&&(E=!0)}return c["s-hn"]=M,r.$flags$&3&&(c["s-sr"]=!0,c["s-cr"]=le,c["s-sn"]=r.$name$||"",c["s-rf"]=(n=r.$attrs$)==null?void 0:n.ref,St(c),a=e&&e.$children$&&e.$children$[s],a&&a.$tag$===r.$tag$&&e.$elm$&&Tt(e.$elm$),kt(le,c,t.$elm$,e?.$elm$)),c},Tt=e=>{p.$flags$|=1;let t=e.closest(M.toLowerCase());if(t!=null){let s=Array.from(t.__childNodes||t.childNodes).find(r=>r["s-cr"]),n=Array.from(e.__childNodes||e.childNodes);for(let r of s?n.reverse():n)r["s-sh"]!=null&&(D(t,r,s??null),r["s-sh"]=void 0,ge=!0)}p.$flags$&=-2},fe=(e,t)=>{p.$flags$|=1;let s=Array.from(e.__childNodes||e.childNodes);if(e["s-sr"]&&Y.experimentalSlotFixes){let n=e;for(;n=n.nextSibling;)n&&n["s-sn"]===e["s-sn"]&&n["s-sh"]===M&&s.push(n)}for(let n=s.length-1;n>=0;n--){let r=s[n];r["s-hn"]!==M&&r["s-ol"]&&(D(Q(r).parentNode,r,Q(r)),r["s-ol"].remove(),r["s-ol"]=void 0,r["s-sh"]=void 0,ge=!0),t&&fe(r,t)}p.$flags$&=-2},Lt=(e,t,s,n,r,i)=>{let c=e["s-cr"]&&e["s-cr"].parentNode||e,o;for(c.shadowRoot&&c.tagName===M&&(c=c.shadowRoot);r<=i;++r)n[r]&&(o=$e(null,s,r),o&&(n[r].$elm$=o,D(c,o,Q(t))))},At=(e,t,s)=>{for(let n=t;n<=s;++n){let r=e[n];if(r){let i=r.$elm$;Nt(r),i&&(ce=!0,i["s-ol"]?i["s-ol"].remove():fe(i,!0),i.remove())}}},dn=(e,t,s,n,r=!1)=>{let i=0,c=0,o=0,a=0,l=t.length-1,$=t[0],d=t[l],f=n.length-1,h=n[0],u=n[f],S,g;for(;i<=l&&c<=f;)if($==null)$=t[++i];else if(d==null)d=t[--l];else if(h==null)h=n[++c];else if(u==null)u=n[--f];else if(se($,h,r))W($,h,r),$=t[++i],h=n[++c];else if(se(d,u,r))W(d,u,r),d=t[--l],u=n[--f];else if(se($,u,r))($.$tag$==="slot"||u.$tag$==="slot")&&fe($.$elm$.parentNode,!1),W($,u,r),D(e,$.$elm$,d.$elm$.nextSibling),$=t[++i],u=n[--f];else if(se(d,h,r))($.$tag$==="slot"||u.$tag$==="slot")&&fe(d.$elm$.parentNode,!1),W(d,h,r),D(e,d.$elm$,$.$elm$),d=t[--l],h=n[++c];else{for(o=-1,a=i;a<=l;++a)if(t[a]&&t[a].$key$!==null&&t[a].$key$===h.$key$){o=a;break}o>=0?(g=t[o],g.$tag$!==h.$tag$?S=$e(t&&t[c],s,o):(W(g,h,r),t[o]=void 0,S=g.$elm$),h=n[++c]):(S=$e(t&&t[c],s,c),h=n[++c]),S&&D(Q($.$elm$).parentNode,S,Q($.$elm$))}i>l?Lt(e,n[f+1]==null?null:n[f+1].$elm$,s,n,c,f):c>f&&At(t,i,l)},se=(e,t,s=!1)=>e.$tag$===t.$tag$?e.$tag$==="slot"?e.$name$===t.$name$:s?(s&&!e.$key$&&t.$key$&&(e.$key$=t.$key$),!0):e.$key$===t.$key$:!1,Q=e=>e&&e["s-ol"]||e,W=(e,t,s=!1)=>{let n=t.$elm$=e.$elm$,r=e.$children$,i=t.$children$,c=t.$tag$,o=t.$text$,a;o===null?(E=c==="svg"?!0:c==="foreignObject"?!1:E,c==="slot"&&!ae&&e.$name$!==t.$name$&&(t.$elm$["s-sn"]=t.$name$||"",Tt(t.$elm$.parentElement)),Oe(e,t,E,s),r!==null&&i!==null?dn(n,r,t,i,s):i!==null?(e.$text$!==null&&(n.textContent=""),Lt(n,null,t,i,0,i.length-1)):!s&&Y.updatable&&r!==null&&At(r,0,r.length-1),E&&c==="svg"&&(E=!1)):(a=n["s-cr"])?a.parentNode.textContent=o:e.$text$!==o&&(n.data=o)},C=[],Ot=e=>{let t,s,n,r=e.__childNodes||e.childNodes;for(let i of r){if(i["s-sr"]&&(t=i["s-cr"])&&t.parentNode){s=t.parentNode.__childNodes||t.parentNode.childNodes;let c=i["s-sn"];for(n=s.length-1;n>=0;n--)if(t=s[n],!t["s-cn"]&&!t["s-nr"]&&t["s-hn"]!==i["s-hn"]&&(!t["s-sh"]||t["s-sh"]!==i["s-hn"]))if(Xe(t,c)){let o=C.find(a=>a.$nodeToRelocate$===t);ce=!0,t["s-sn"]=t["s-sn"]||c,o?(o.$nodeToRelocate$["s-sh"]=i["s-hn"],o.$slotRefNode$=i):(t["s-sh"]=i["s-hn"],C.push({$slotRefNode$:i,$nodeToRelocate$:t})),t["s-sr"]&&C.map(a=>{Xe(a.$nodeToRelocate$,t["s-sn"])&&(o=C.find(l=>l.$nodeToRelocate$===t),o&&!a.$slotRefNode$&&(a.$slotRefNode$=o.$slotRefNode$))})}else C.some(o=>o.$nodeToRelocate$===t)||C.push({$nodeToRelocate$:t})}i.nodeType===1&&Ot(i)}},Nt=e=>{e.$attrs$&&e.$attrs$.ref&&e.$attrs$.ref(null),e.$children$&&e.$children$.map(Nt)},D=(e,t,s)=>{if(typeof t["s-sn"]=="string"&&t["s-sr"]&&t["s-cr"])kt(t["s-cr"],t,e,t.parentElement);else if(typeof t["s-sn"]=="string"){e.getRootNode().nodeType!==11&&_t(t),e.insertBefore(t,s);let{slotNode:n}=De(t);return n&&pe(n),t}return e.__insertBefore?e.__insertBefore(t,s):e?.insertBefore(t,s)};function kt(e,t,s,n){var r,i;let c;if(e&&typeof t["s-sn"]=="string"&&t["s-sr"]&&e.parentNode&&e.parentNode["s-sc"]&&(c=t["s-si"]||e.parentNode["s-sc"])){let o=t["s-sn"],a=t["s-hn"];if((r=s.classList)==null||r.add(c+"-s"),n&&((i=n.classList)!=null&&i.contains(c+"-s"))){let l=(n.__childNodes||n.childNodes)[0],$=!1;for(;l;){if(l["s-sn"]!==o&&l["s-hn"]===a&&l["s-sr"]){$=!0;break}l=l.nextSibling}$||n.classList.remove(c+"-s")}}}var un=(e,t,s=!1)=>{var n,r,i,c,o;let a=e.$hostElement$,l=e.$cmpMeta$,$=e.$vnode$||G(null,null),f=Qs(t)?t:bt(null,null,t);if(M=a.tagName,l.$attrsToReflect$&&(f.$attrs$=f.$attrs$||{},l.$attrsToReflect$.map(([h,u])=>f.$attrs$[u]=a[h])),s&&f.$attrs$)for(let h of Object.keys(f.$attrs$))a.hasAttribute(h)&&!["key","ref","style","class"].includes(h)&&(f.$attrs$[h]=a[h]);f.$tag$=null,f.$flags$|=4,e.$vnode$=f,f.$elm$=$.$elm$=a.shadowRoot||a,ne=a["s-sc"],ae=!!(l.$flags$&1)&&!(l.$flags$&128),le=a["s-cr"],ce=!1,W($,f,s);{if(p.$flags$|=1,ge){Ot(f.$elm$);for(let h of C){let u=h.$nodeToRelocate$;if(!u["s-ol"]&&v.document){let S=v.document.createTextNode("");S["s-nr"]=u,D(u.parentNode,u["s-ol"]=S,u)}}for(let h of C){let u=h.$nodeToRelocate$,S=h.$slotRefNode$;if(S){let g=S.parentNode,y=S.nextSibling;if(y&&y.nodeType===1){let m=(n=u["s-ol"])==null?void 0:n.previousSibling;for(;m;){let _=(r=m["s-nr"])!=null?r:null;if(_&&_["s-sn"]===u["s-sn"]&&g===(_.__parentNode||_.parentNode)){for(_=_.nextSibling;_===u||_?.["s-sr"];)_=_?.nextSibling;if(!_||!_["s-nr"]){y=_;break}}m=m.previousSibling}}let O=u.__parentNode||u.parentNode,R=u.__nextSibling||u.nextSibling;(!y&&g!==O||R!==y)&&u!==y&&(D(g,u,y),u.nodeType===1&&u.tagName!=="SLOT-FB"&&(u.hidden=(i=u["s-ih"])!=null?i:!1)),u&&typeof S["s-rf"]=="function"&&S["s-rf"](S)}else u.nodeType===1&&(s&&(u["s-ih"]=(c=u.hidden)!=null?c:!1),u.hidden=!0)}}ce&&ue(f.$elm$),p.$flags$&=-2,C.length=0}if(l.$flags$&2){let h=f.$elm$.__childNodes||f.$elm$.childNodes;for(let u of h)u["s-hn"]!==M&&!u["s-sh"]&&(s&&u["s-ih"]==null&&(u["s-ih"]=(o=u.hidden)!=null?o:!1),u.hidden=!0)}le=void 0},jt=(e,t)=>{if(t&&!e.$onRenderResolve$&&t["s-p"]){let s=t["s-p"].push(new Promise(n=>e.$onRenderResolve$=()=>{t["s-p"].splice(s-1,1),n()}))}},ve=(e,t)=>{if(e.$flags$|=16,e.$flags$&4){e.$flags$|=512;return}return jt(e,e.$ancestorComponent$),bs(()=>hn(e,t))},hn=(e,t)=>{let s=e.$hostElement$,n=j("scheduleUpdate",e.$cmpMeta$.$tagName$),r=e.$lazyInstance$;if(!r)throw new Error(`Can't render component <${s.tagName.toLowerCase()} /> with invalid Stencil runtime! Make sure this imported component is compiled with a \`externalRuntime: true\` flag. For more information, please refer to https://stenciljs.com/docs/custom-elements#externalruntime`);let i;return t?(e.$flags$|=256,e.$queuedListeners$&&(e.$queuedListeners$.map(([c,o])=>w(r,c,o,s)),e.$queuedListeners$=void 0),i=w(r,"componentWillLoad",void 0,s)):i=w(r,"componentWillUpdate",void 0,s),i=nt(i,()=>w(r,"componentWillRender",void 0,s)),n(),nt(i,()=>gn(e,r,t))},nt=(e,t)=>pn(e)?e.then(t).catch(s=>{console.error(s),t()}):t(),pn=e=>e instanceof Promise||e&&e.then&&typeof e.then=="function",gn=(e,t,s)=>te(null,null,function*(){var n;let r=e.$hostElement$,i=j("update",e.$cmpMeta$.$tagName$),c=r["s-rc"];s&&ln(e);let o=j("render",e.$cmpMeta$.$tagName$);vn(e,t,r,s),c&&(c.map(a=>a()),r["s-rc"]=void 0),o(),i();{let a=(n=r["s-p"])!=null?n:[],l=()=>yn(e);a.length===0?l():(Promise.all(a).then(l),e.$flags$|=4,a.length=0)}}),vn=(e,t,s,n)=>{try{t=t.render&&t.render(),e.$flags$&=-17,e.$flags$|=2,un(e,t,n)}catch(r){z(r,e.$hostElement$)}return null},yn=e=>{let t=e.$cmpMeta$.$tagName$,s=e.$hostElement$,n=j("postUpdate",t),r=e.$lazyInstance$,i=e.$ancestorComponent$;w(r,"componentDidRender",void 0,s),e.$flags$&64?(w(r,"componentDidUpdate",void 0,s),n()):(e.$flags$|=64,Sn(s),w(r,"componentDidLoad",void 0,s),n(),e.$onReadyResolve$(s),i||Rt()),e.$onInstanceResolve$(s),e.$onRenderResolve$&&(e.$onRenderResolve$(),e.$onRenderResolve$=void 0),e.$flags$&512&&Re(()=>ve(e,!1)),e.$flags$&=-517},zn=e=>{{let t=T(e),s=t.$hostElement$.isConnected;return s&&(t.$flags$&18)===2&&ve(t,!1),s}},Rt=e=>{Re(()=>mt(v,"appload",{detail:{namespace:Mt}}))},w=(e,t,s,n)=>{if(e&&e[t])try{return e[t](s)}catch(r){z(r,n)}},Sn=e=>{var t;return e.classList.add((t=Y.hydratedSelectorName)!=null?t:"hydrated")},_n=(e,t)=>T(e).$instanceValues$.get(t),_e=(e,t,s,n)=>{let r=T(e);if(!r)throw new Error(`Couldn't find host element for "${n.$tagName$}" as it is unknown to this Stencil runtime. This usually happens when integrating a 3rd party Stencil component with another Stencil component or application. Please reach out to the maintainers of the 3rd party Stencil component or report this on the Stencil Discord server (https://chat.stenciljs.com) or comment on this similar [GitHub issue](https://github.com/stenciljs/core/issues/5457).`);let i=r.$hostElement$,c=r.$instanceValues$.get(t),o=r.$flags$,a=r.$lazyInstance$;s=oe(s,n.$members$[t][0]);let l=Number.isNaN(c)&&Number.isNaN(s),$=s!==c&&!l;if((!(o&8)||c===void 0)&&$&&(r.$instanceValues$.set(t,s),a)){if(n.$watchers$&&o&128){let d=n.$watchers$[t];d&&d.map(f=>{try{a[f](s,c,t)}catch(h){z(h,i)}})}if((o&18)===2){if(a.componentShouldUpdate&&a.componentShouldUpdate(s,c,t)===!1)return;ve(r,!1)}}},Ct=(e,t,s)=>{var n,r;let i=e.prototype;if(t.$members$||t.$watchers$||e.watchers){e.watchers&&!t.$watchers$&&(t.$watchers$=e.watchers);let c=Object.entries((n=t.$members$)!=null?n:{});if(c.map(([o,[a]])=>{if(a&31||s&2&&a&32){let{get:l,set:$}=Object.getOwnPropertyDescriptor(i,o)||{};l&&(t.$members$[o][0]|=2048),$&&(t.$members$[o][0]|=4096),(s&1||!l)&&Object.defineProperty(i,o,{get(){{if((t.$members$[o][0]&2048)===0)return _n(this,o);let d=T(this),f=d?d.$lazyInstance$:i;return f?f[o]:void 0}},configurable:!0,enumerable:!0}),Object.defineProperty(i,o,{set(d){let f=T(this);if($){let h=a&32?this[o]:f.$hostElement$[o];typeof h>"u"&&f.$instanceValues$.get(o)?d=f.$instanceValues$.get(o):!f.$instanceValues$.get(o)&&h&&f.$instanceValues$.set(o,h),$.apply(this,[oe(d,a)]),d=a&32?this[o]:f.$hostElement$[o],_e(this,o,d,t);return}{if((s&1)===0||(t.$members$[o][0]&4096)===0){_e(this,o,d,t),s&1&&!f.$lazyInstance$&&f.$onReadyPromise$.then(()=>{t.$members$[o][0]&4096&&f.$lazyInstance$[o]!==f.$instanceValues$.get(o)&&(f.$lazyInstance$[o]=d)});return}let h=()=>{let u=f.$lazyInstance$[o];!f.$instanceValues$.get(o)&&u&&f.$instanceValues$.set(o,u),f.$lazyInstance$[o]=oe(d,a),_e(this,o,f.$lazyInstance$[o],t)};f.$lazyInstance$?h():f.$onReadyPromise$.then(()=>h())}}})}else s&1&&a&64&&Object.defineProperty(i,o,{value(...l){var $;let d=T(this);return($=d?.$onInstancePromise$)==null?void 0:$.then(()=>{var f;return(f=d.$lazyInstance$)==null?void 0:f[o](...l)})}})}),s&1){let o=new Map;i.attributeChangedCallback=function(a,l,$){p.jmp(()=>{var d;let f=o.get(a);if(this.hasOwnProperty(f)&&Y.lazyLoad)$=this[f],delete this[f];else{if(i.hasOwnProperty(f)&&typeof this[f]=="number"&&this[f]==$)return;if(f==null){let u=T(this),S=u?.$flags$;if(S&&!(S&8)&&S&128&&$!==l){let g=u.$lazyInstance$,y=(d=t.$watchers$)==null?void 0:d[a];y?.forEach(O=>{g[O]!=null&&g[O].call(g,$,l,a)})}return}}let h=Object.getOwnPropertyDescriptor(i,f);$=$===null&&typeof this[f]=="boolean"?!1:$,$!==this[f]&&(!h.get||h.set)&&(this[f]=$)})},e.observedAttributes=Array.from(new Set([...Object.keys((r=t.$watchers$)!=null?r:{}),...c.filter(([a,l])=>l[0]&15).map(([a,l])=>{var $;let d=l[1]||a;return o.set(d,a),l[0]&512&&(($=t.$attrsToReflect$)==null||$.push([a,d])),d})]))}}return e},bn=(e,t,s,n)=>te(null,null,function*(){let r;if((t.$flags$&32)===0){if(t.$flags$|=32,s.$lazyBundleId$){let a=fs(s,t);if(a&&"then"in a){let $=Ks();r=yield a,$()}else r=a;if(!r)throw new Error(`Constructor for "${s.$tagName$}#${t.$modeName$}" was not found`);r.isProxied||(s.$watchers$=r.watchers,Ct(r,s,2),r.isProxied=!0);let l=j("createInstance",s.$tagName$);t.$flags$|=8;try{new r(t)}catch($){z($,e)}t.$flags$&=-9,t.$flags$|=128,l(),Ne(t.$lazyInstance$,e)}else{r=e.constructor;let a=e.localName;customElements.whenDefined(a).then(()=>t.$flags$|=128)}if(r&&r.style){let a;typeof r.style=="string"?a=r.style:typeof r.style!="string"&&(t.$modeName$=sn(e),t.$modeName$&&(a=r.style[t.$modeName$]));let l=Pe(s,t.$modeName$);if(!re.has(l)){let $=j("registerStyles",s.$tagName$);xt(l,a,!!(s.$flags$&1)),$()}}}let i=t.$ancestorComponent$,c=()=>ve(t,!0);i&&i["s-rc"]?i["s-rc"].push(c):c()}),Ne=(e,t)=>{w(e,"connectedCallback",void 0,t)},mn=e=>{if((p.$flags$&1)===0){let t=T(e),s=t.$cmpMeta$,n=j("connectedCallback",s.$tagName$);if(t.$flags$&1)wt(e,t,s.$listeners$),t?.$lazyInstance$?Ne(t.$lazyInstance$,e):t?.$onReadyPromise$&&t.$onReadyPromise$.then(()=>Ne(t.$lazyInstance$,e));else{t.$flags$|=1;let r;if(r=e.getAttribute(K),r){if(s.$flags$&1){let i=It(e.shadowRoot,s,e.getAttribute("s-mode"));e.classList.remove(i+"-h",i+"-s")}else if(s.$flags$&2){let i=Pe(s,e.getAttribute("s-mode"));e["s-sc"]=i}tn(e,s.$tagName$,r,t)}r||s.$flags$&12&&xn(e);{let i=e;for(;i=i.parentNode||i.host;)if(i.nodeType===1&&i.hasAttribute("s-id")&&i["s-p"]||i["s-p"]){jt(t,t.$ancestorComponent$=i);break}}s.$members$&&Object.entries(s.$members$).map(([i,[c]])=>{if(c&31&&e.hasOwnProperty(i)){let o=e[i];delete e[i],e[i]=o}}),bn(e,t,s)}n()}},xn=e=>{if(!v.document)return;let t=e["s-cr"]=v.document.createComment("");t["s-cn"]=!0,D(e,t,e.firstChild)},rt=(e,t)=>{w(e,"disconnectedCallback",void 0,t||e)},In=e=>te(null,null,function*(){if((p.$flags$&1)===0){let t=T(e);t.$rmListeners$&&(t.$rmListeners$.map(s=>s()),t.$rmListeners$=void 0),t?.$lazyInstance$?rt(t.$lazyInstance$,e):t?.$onReadyPromise$&&t.$onReadyPromise$.then(()=>rt(t.$lazyInstance$,e))}F.has(e)&&F.delete(e),e.shadowRoot&&F.has(e.shadowRoot)&&F.delete(e.shadowRoot)}),Hn=(e,t={})=>{var s;if(!v.document){console.warn("Stencil: No document found. Skipping bootstrapping lazy components.");return}let n=j(),r=[],i=t.exclude||[],c=v.customElements,o=v.document.head,a=o.querySelector("meta[charset]"),l=v.document.createElement("style"),$=[],d,f=!0;Object.assign(p,t),p.$resourcesUrl$=new URL(t.resourcesUrl||"./",v.document.baseURI).href,p.$flags$|=2,cn();let h=!1;if(e.map(u=>{u[1].map(S=>{var g;let y={$flags$:S[0],$tagName$:S[1],$members$:S[2],$listeners$:S[3]};y.$flags$&4&&(h=!0),y.$members$=S[2],y.$listeners$=S[3],y.$attrsToReflect$=[],y.$watchers$=(g=S[4])!=null?g:{};let O=y.$tagName$,R=class extends HTMLElement{constructor(m){if(super(m),this.hasRegisteredEventListeners=!1,m=this,$s(m,y),y.$flags$&1){if(!m.shadowRoot)Ns.call(m,y);else if(m.shadowRoot.mode!=="open")throw new Error(`Unable to re-use existing shadow root for ${y.$tagName$}! Mode is set to ${m.shadowRoot.mode} but Stencil only supports open shadow roots.`)}}connectedCallback(){let m=T(this);this.hasRegisteredEventListeners||(this.hasRegisteredEventListeners=!0,wt(this,m,y.$listeners$)),d&&(clearTimeout(d),d=null),f?$.push(this):p.jmp(()=>mn(this))}disconnectedCallback(){p.jmp(()=>In(this)),p.raf(()=>{var m;let _=T(this),J=$.findIndex(x=>x===this);J>-1&&$.splice(J,1),((m=_?.$vnode$)==null?void 0:m.$elm$)instanceof Node&&!_.$vnode$.$elm$.isConnected&&delete _.$vnode$.$elm$})}componentOnReady(){return T(this).$onReadyPromise$}};y.$flags$&2&&ks(R.prototype),y.$lazyBundleId$=u[0],!i.includes(O)&&!c.get(O)&&(r.push(O),c.define(O,Ct(R,y,1)))})}),r.length>0&&(h&&(l.textContent+=ht),l.textContent+=r.sort()+vs,l.innerHTML.length)){l.setAttribute("data-styles","");let u=(s=p.$nonce$)!=null?s:vt(v.document);u!=null&&l.setAttribute("nonce",u),o.insertBefore(l,a?a.nextSibling:o.firstChild)}f=!1,$.length?$.map(u=>u.connectedCallback()):p.jmp(()=>d=setTimeout(Rt,30)),n()},Un=(e,t)=>t,wt=(e,t,s,n)=>{s&&v.document&&s.map(([r,i,c])=>{let o=Tn(v.document,e,r),a=En(t,c),l=Ln(r);p.ael(o,i,a,l),(t.$rmListeners$=t.$rmListeners$||[]).push(()=>p.rel(o,i,a,l))})},En=(e,t)=>s=>{var n;try{e.$flags$&256?(n=e.$lazyInstance$)==null||n[t](s):(e.$queuedListeners$=e.$queuedListeners$||[]).push([t,s])}catch(r){z(r,e.$hostElement$)}},Tn=(e,t,s)=>s&4?e:s&8?v:s&16?e.body:t,Ln=e=>Ss?{passive:(e&1)!==0,capture:(e&2)!==0}:(e&2)!==0;export{N as a,Ft as b,Nn as c,kn as d,Gt as e,jn as f,Rn as g,Cn as h,wn as i,Bn as j,bs as k,Pn as l,bt as m,Zs as n,on as o,Mn as p,zn as q,Hn as r,Un as s};
