
.dashboard-container {
  padding: 0;
  background: transparent;
  min-height: 100vh;
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
}

/* Dashboard Header */
.dashboard-header {
  margin-bottom: clamp(20px, 5vw, 32px);

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: clamp(16px, 4vw, 24px);
    flex-wrap: wrap;

    .welcome-section {
      flex: 1;
      min-width: 0;

      .dashboard-title {
        font-size: clamp(24px, 6vw, 32px);
        font-weight: 800;
        color: #c8102e;
        margin: 0 0 clamp(4px, 1vw, 8px) 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        line-height: 1.2;
        word-break: break-word;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;

      .action-btn {
        padding: 12px 20px;
        border: none;
        border-radius: 12px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;

        ion-icon {
          font-size: 18px;
        }

        
      }
    }
  }
}

/* Statistics Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(min(280px, 100%), 1fr));
  gap: clamp(16px, 4vw, 24px);
  margin-bottom: clamp(20px, 5vw, 32px);
  width: 100%;

  .stat-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: clamp(16px, 4vw, 20px);
    padding: clamp(16px, 4vw, 24px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: clamp(3px, 1vw, 4px);
      background: linear-gradient(90deg, #c8102e);
    }

    &:hover {
      transform: translateY(clamp(-4px, -1vw, -8px));
      box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
    }

    &:active {
      transform: translateY(clamp(-2px, -0.5vw, -4px)) scale(0.98);
    }

    .stat-content {
      display: flex;
      align-items: center;
      gap: clamp(12px, 3vw, 20px);
      width: 100%;

      .stat-icon {
        width: clamp(48px, 12vw, 64px);
        height: clamp(48px, 12vw, 64px);
        border-radius: clamp(12px, 3vw, 16px);
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        flex-shrink: 0;

        ion-icon {
          font-size: clamp(20px, 5vw, 28px);
          color: white;
        }
      }

      .stat-info {
        flex: 1;
        min-width: 0;

        .stat-value {
          font-size: clamp(20px, 5vw, 28px);
          font-weight: 800;
          color: #2d3748;
          margin: 0 0 clamp(2px, 0.5vw, 4px) 0;
          line-height: 1.1;
          word-break: break-word;
        }

        .stat-label {
          font-size: clamp(12px, 3vw, 14px);
          color: #718096;
          margin: 0 0 clamp(4px, 1vw, 8px) 0;
          font-weight: 500;
          line-height: 1.3;
          word-break: break-word;
        }
      }
    }
  }
}

/* Analytics Section */
.analytics-section {
  margin-bottom: 32px;

  .analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 24px;

    .chart-card {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 20px;
      padding: 24px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        h3 {
          font-size: 18px;
          font-weight: 700;
          color: #2d3748;
          margin: 0;
        }

        .chart-actions {
          .chart-btn {
            background: none;
            border: none;
            padding: 8px;
            border-radius: 8px;
            cursor: pointer;
            color: #718096;
            transition: all 0.3s ease;

            &:hover {
              background: rgba(102, 126, 234, 0.1);
              color: #667eea;
            }

            ion-icon {
              font-size: 20px;
            }
          }
        }
      }

      .chart-container {
        height: 200px;
        position: relative;

        canvas {
          width: 100%;
          height: 100%;
          border-radius: 12px;
        }
      }
    }
  }
}

/* Bottom Section */
.bottom-section {
  .bottom-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(350px, 100%), 1fr));
    gap: clamp(16px, 4vw, 24px);
    width: 100%;

    .activity-card,
    .performers-card {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: clamp(16px, 4vw, 20px);
      padding: clamp(16px, 4vw, 24px);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      touch-action: manipulation;
      -webkit-tap-highlight-color: transparent;

      &:hover {
        transform: translateY(clamp(-2px, -0.5vw, -4px));
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
      }

      &:active {
        transform: translateY(clamp(-1px, -0.25vw, -2px)) scale(0.98);
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: clamp(16px, 4vw, 20px);
        gap: clamp(8px, 2vw, 12px);
        flex-wrap: wrap;

        h3 {
          font-size: clamp(16px, 4vw, 18px);
          font-weight: 700;
          color: #2d3748;
          margin: 0;
          line-height: 1.3;
          word-break: break-word;
        }

        .view-all {
          color: #c8102e;
          text-decoration: none;
          font-size: clamp(12px, 3vw, 14px);
          font-weight: 600;
          transition: all 0.3s ease;
          white-space: nowrap;
          padding: clamp(4px, 1vw, 6px) clamp(8px, 2vw, 12px);
          border-radius: clamp(6px, 1.5vw, 8px);
          touch-action: manipulation;

          &:hover {
            background: rgba(200, 16, 46, 0.1);
            color: #c8102e;
          }

          &:active {
            transform: scale(0.95);
          }
        }
      }
    }

    .activity-list {
      .activity-item {
        display: flex;
        align-items: center;
        gap: clamp(12px, 3vw, 16px);
        padding: clamp(12px, 3vw, 16px) 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        transition: background 0.2s ease;

        &:last-child {
          border-bottom: none;
        }

        &:hover {
          background: rgba(200, 16, 46, 0.02);
          border-radius: clamp(6px, 1.5vw, 8px);
          margin: 0 clamp(-8px, -2vw, -12px);
          padding: clamp(12px, 3vw, 16px) clamp(8px, 2vw, 12px);
        }

        .activity-icon {
          width: clamp(36px, 8vw, 40px);
          height: clamp(36px, 8vw, 40px);
          border-radius: clamp(8px, 2vw, 10px);
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          flex-shrink: 0;

          ion-icon {
            font-size: clamp(16px, 4vw, 18px);
            color: white;
          }
        }

        .activity-content {
          flex: 1;
          min-width: 0;

          .activity-text {
            font-size: clamp(13px, 3vw, 14px);
            color: #4a5568;
            margin: 0 0 clamp(2px, 0.5vw, 4px) 0;
            font-weight: 500;
            line-height: 1.4;
            word-break: break-word;
          }

          .activity-time {
            font-size: clamp(11px, 2.5vw, 12px);
            color: #a0aec0;
            font-weight: 400;
            line-height: 1.3;
          }
        }
      }
    }

    .performers-list {
      .performer-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: clamp(12px, 3vw, 16px) 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        transition: background 0.2s ease;
        gap: clamp(8px, 2vw, 12px);

        &:last-child {
          border-bottom: none;
        }

        &:hover {
          background: rgba(200, 16, 46, 0.02);
          border-radius: clamp(6px, 1.5vw, 8px);
          margin: 0 clamp(-8px, -2vw, -12px);
          padding: clamp(12px, 3vw, 16px) clamp(8px, 2vw, 12px);
        }

        .performer-info {
          display: flex;
          align-items: center;
          gap: clamp(8px, 2vw, 12px);
          flex: 1;
          min-width: 0;

          .performer-avatar {
            width: clamp(36px, 8vw, 40px);
            height: clamp(36px, 8vw, 40px);
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #c8102e;
            flex-shrink: 0;
          }

          .performer-details {
            flex: 1;
            min-width: 0;

            h4 {
              font-size: clamp(13px, 3vw, 14px);
              font-weight: 600;
              color: #2d3748;
              margin: 0 0 clamp(1px, 0.25vw, 2px) 0;
              line-height: 1.3;
              word-break: break-word;
            }

            p {
              font-size: clamp(11px, 2.5vw, 12px);
              color: #718096;
              margin: 0;
              line-height: 1.3;
              word-break: break-word;
            }
          }
        }

        .performer-rating {
          display: flex;
          align-items: center;
          gap: clamp(6px, 1.5vw, 8px);
          flex-shrink: 0;

          .rating-stars {
            display: flex;
            gap: clamp(1px, 0.25vw, 2px);

            ion-icon {
              font-size: clamp(12px, 3vw, 14px);
              color: #e2e8f0;

              &.filled {
                color: #fbbf24;
              }
            }
          }

          .rating-value {
            font-size: clamp(11px, 2.5vw, 12px);
            font-weight: 600;
            color: #4a5568;
            white-space: nowrap;
          }
        }
      }
    }
  }
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.stat-card {
  animation: fadeInUp 0.6s ease forwards;
}

.chart-card {
  animation: slideInFromRight 0.8s ease forwards;
}

.activity-card,
.performers-card {
  animation: fadeInUp 1s ease forwards;
}

/* Responsive Design - Mobile First Approach */

/* Large tablets and small desktops */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(min(250px, 100%), 1fr));
  }

  .bottom-grid {
    grid-template-columns: repeat(auto-fit, minmax(min(320px, 100%), 1fr));
  }
}

/* Tablets */
@media (max-width: 768px) {
  .dashboard-header {
    margin-bottom: 20px;

    .header-content {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;

      .welcome-section {
        text-align: center;

        .dashboard-title {
          font-size: 28px;
        }
      }
    }
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .bottom-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .activity-item,
  .performer-item {
    padding: 14px 0;

    &:hover {
      margin: 0 -12px;
      padding: 14px 12px;
    }
  }
}

/* Mobile devices */
@media (max-width: 480px) {
  .dashboard-container {
    padding: 0;
  }

  .dashboard-header {
    margin-bottom: 16px;

    .header-content {
      gap: 8px;

      .welcome-section {
        .dashboard-title {
          font-size: 24px;
        }
      }
    }
  }

  .stats-grid {
    gap: 12px;
    margin-bottom: 20px;
  }

  .stat-card {
    padding: 16px;
    border-radius: 16px;

    .stat-content {
      gap: 12px;

      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;

        ion-icon {
          font-size: 20px;
        }
      }

      .stat-info {
        .stat-value {
          font-size: 20px;
        }

        .stat-label {
          font-size: 12px;
        }
      }
    }
  }

  .bottom-grid {
    gap: 12px;
  }

  .activity-card,
  .performers-card {
    padding: 16px;
    border-radius: 16px;

    .card-header {
      margin-bottom: 16px;
      flex-direction: column;
      align-items: stretch;
      gap: 8px;

      h3 {
        font-size: 16px;
        text-align: center;
      }

      .view-all {
        align-self: center;
        font-size: 12px;
      }
    }
  }

  .activity-item {
    padding: 12px 0;
    gap: 12px;

    &:hover {
      margin: 0 -8px;
      padding: 12px 8px;
    }

    .activity-icon {
      width: 36px;
      height: 36px;
      border-radius: 8px;

      ion-icon {
        font-size: 16px;
      }
    }

    .activity-content {
      .activity-text {
        font-size: 13px;
      }

      .activity-time {
        font-size: 11px;
      }
    }
  }

  .performer-item {
    padding: 12px 0;
    gap: 8px;

    &:hover {
      margin: 0 -8px;
      padding: 12px 8px;
    }

    .performer-info {
      gap: 8px;

      .performer-avatar {
        width: 36px;
        height: 36px;
      }

      .performer-details {
        h4 {
          font-size: 13px;
        }

        p {
          font-size: 11px;
        }
      }
    }

    .performer-rating {
      gap: 6px;

      .rating-stars {
        gap: 1px;

        ion-icon {
          font-size: 12px;
        }
      }

      .rating-value {
        font-size: 11px;
      }
    }
  }
}

/* Extra small devices */
@media (max-width: 360px) {
  .dashboard-header {
    .header-content {
      .welcome-section {
        .dashboard-title {
          font-size: 20px;
        }
      }
    }
  }

  .stat-card {
    padding: 12px;

    .stat-content {
      gap: 8px;

      .stat-icon {
        width: 40px;
        height: 40px;

        ion-icon {
          font-size: 18px;
        }
      }

      .stat-info {
        .stat-value {
          font-size: 18px;
        }

        .stat-label {
          font-size: 11px;
        }
      }
    }
  }

  .activity-card,
  .performers-card {
    padding: 12px;

    .card-header {
      h3 {
        font-size: 14px;
      }
    }
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .stat-card {
    &:hover {
      transform: none;
    }

    &:active {
      transform: scale(0.98);
    }
  }

  .activity-card,
  .performers-card {
    &:hover {
      transform: none;
    }

    &:active {
      transform: scale(0.98);
    }
  }

  .activity-item,
  .performer-item {
    &:hover {
      background: none;
      margin: 0;
      padding: clamp(12px, 3vw, 16px) 0;
    }

    &:active {
      background: rgba(200, 16, 46, 0.05);
    }
  }

  .view-all {
    &:hover {
      background: none;
    }

    &:active {
      background: rgba(200, 16, 46, 0.1);
    }
  }
}
