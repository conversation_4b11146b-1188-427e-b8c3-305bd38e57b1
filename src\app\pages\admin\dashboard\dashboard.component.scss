
.dashboard-container {
  padding: 0;
  background: transparent;
  min-height: 100vh;
}

/* Dashboard Header */
.dashboard-header {
  margin-bottom: 32px;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 24px;

    .welcome-section {
      .dashboard-title {
        font-size: 32px;
        font-weight: 800;
        color: #c8102e;
        margin: 0 0 8px 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

    
    }

    .header-actions {
      display: flex;
      gap: 12px;

      .action-btn {
        padding: 12px 20px;
        border: none;
        border-radius: 12px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;

        ion-icon {
          font-size: 18px;
        }

        
      }
    }
  }
}

/* Statistics Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;

  .stat-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 24px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg,#c8102e);
    }

    &:hover {
      transform: translateY(-8px);
      box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
    }

    .stat-content {
      display: flex;
      align-items: center;
      gap: 20px;

      .stat-icon {
        width: 64px;
        height: 64px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);

        ion-icon {  
          font-size: 28px;
          color: white;
        }
      }

      .stat-info {
        flex: 1;

        .stat-value {
          font-size: 28px;
          font-weight: 800;
          color: #2d3748;
          margin: 0 0 4px 0;
          line-height: 1;
        }

        .stat-label {
          font-size: 14px;
          color: #718096;
          margin: 0 0 8px 0;
          font-weight: 500;
        }
      }
    }
  }
}

/* Analytics Section */
.analytics-section {
  margin-bottom: 32px;

  .analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 24px;

    .chart-card {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 20px;
      padding: 24px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        h3 {
          font-size: 18px;
          font-weight: 700;
          color: #2d3748;
          margin: 0;
        }

        .chart-actions {
          .chart-btn {
            background: none;
            border: none;
            padding: 8px;
            border-radius: 8px;
            cursor: pointer;
            color: #718096;
            transition: all 0.3s ease;

            &:hover {
              background: rgba(102, 126, 234, 0.1);
              color: #667eea;
            }

            ion-icon {
              font-size: 20px;
            }
          }
        }
      }

      .chart-container {
        height: 200px;
        position: relative;

        canvas {
          width: 100%;
          height: 100%;
          border-radius: 12px;
        }
      }
    }
  }
}

/* Bottom Section */
.bottom-section {
  .bottom-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 24px;

    .activity-card,
    .performers-card {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 20px;
      padding: 24px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        h3 {
          font-size: 18px;
          font-weight: 700;
          color: #2d3748;
          margin: 0;
        }

        .view-all {
          color: #667eea;
          text-decoration: none;
          font-size: 14px;
          font-weight: 600;
          transition: all 0.3s ease;

          &:hover {
            color: #764ba2;
          }
        }
      }
    }

    .activity-list {
      .activity-item {
        display: flex;
        align-items: center;
        gap: 16px;
        padding: 16px 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);

        &:last-child {
          border-bottom: none;
        }

        .activity-icon {
          width: 40px;
          height: 40px;
          border-radius: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

          ion-icon {
            font-size: 18px;
            color: white;
          }
        }

        .activity-content {
          flex: 1;

          .activity-text {
            font-size: 14px;
            color: #4a5568;
            margin: 0 0 4px 0;
            font-weight: 500;
          }

          .activity-time {
            font-size: 12px;
            color: #a0aec0;
            font-weight: 400;
          }
        }
      }
    }

    .performers-list {
      .performer-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);

        &:last-child {
          border-bottom: none;
        }

        .performer-info {
          display: flex;
          align-items: center;
          gap: 12px;

          .performer-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #667eea;
          }

          .performer-details {
            h4 {
              font-size: 14px;
              font-weight: 600;
              color: #2d3748;
              margin: 0 0 2px 0;
            }

            p {
              font-size: 12px;
              color: #718096;
              margin: 0;
            }
          }
        }

        .performer-rating {
          display: flex;
          align-items: center;
          gap: 8px;

          .rating-stars {
            display: flex;
            gap: 2px;

            ion-icon {
              font-size: 14px;
              color: #e2e8f0;

              &.filled {
                color: #fbbf24;
              }
            }
          }

          .rating-value {
            font-size: 12px;
            font-weight: 600;
            color: #4a5568;
          }
        }
      }
    }
  }
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.stat-card {
  animation: fadeInUp 0.6s ease forwards;
}

.chart-card {
  animation: slideInFromRight 0.8s ease forwards;
}

.activity-card,
.performers-card {
  animation: fadeInUp 1s ease forwards;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .analytics-grid {
    grid-template-columns: 1fr;
  }

  .bottom-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    .header-content {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;

      .welcome-section {
        .dashboard-title {
          font-size: 24px;
        }

        .dashboard-subtitle {
          font-size: 14px;
        }
      }

      .header-actions {
        justify-content: center;

        .action-btn {
          flex: 1;
          justify-content: center;
        }
      }
    }
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .analytics-grid {
    grid-template-columns: 1fr;
    gap: 16px;

    .chart-card {
      .chart-container {
        height: 150px;
      }
    }
  }

  .bottom-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .dashboard-container {
    padding: 0;
  }

  .stat-card {
    padding: 16px;

    .stat-content {
      gap: 12px;

      .stat-icon {
        width: 48px;
        height: 48px;

        ion-icon {
          font-size: 20px;
        }
      }

      .stat-info {
        .stat-value {
          font-size: 20px;
        }
      }
    }
  }

  .chart-card,
  .activity-card,
  .performers-card {
    padding: 16px;
  }

  .header-actions {
    flex-direction: column;

    .action-btn {
      width: 100%;
    }
  }
}
