import{c as U,d as z,f as ht,g as Z,h as xt,i as tt,j as v}from"./chunk-X3KNIMLA.js";import"./chunk-5ANC4TAO.js";import"./chunk-QUJFQN2Y.js";import"./chunk-7HYD6CC7.js";import"./chunk-5VDVK6VQ.js";import"./chunk-423VJ54E.js";import"./chunk-7FY2OE2O.js";import"./chunk-RC522WYB.js";import"./chunk-LMF7SRCC.js";import"./chunk-AWU72VOH.js";import"./chunk-W5XPJHSR.js";import"./chunk-3AW3VJFF.js";import"./chunk-MTHZ7MWU.js";import"./chunk-WI5MSH4N.js";import"./chunk-UT7HCVIF.js";import"./chunk-CKP3SGE2.js";import"./chunk-I4XHNRTT.js";import{$ as _,A as S,Aa as dt,B as Q,E as P,I as C,J as e,K as n,L as p,P as y,Q as O,R as M,V as rt,W as st,X as gt,Y as lt,Z as h,_ as s,_a as G,a as N,aa as k,ba as R,bb as K,ca as B,da as q,db as J,e as Ot,ea as H,g as E,ga as mt,i as F,j as A,ja as j,k as $,ka as D,kb as X,ma as x,o as m,oa as T,p as u,pa as Y,ta as ut,v as bt,w as ft,wa as W,x as g,y as b}from"./chunk-XPS4RP6N.js";import"./chunk-YJ7VB3TT.js";import"./chunk-QDU6VP7L.js";import"./chunk-ICSGBKZQ.js";import"./chunk-YSN7XVTD.js";import"./chunk-R5HL6L5F.js";import"./chunk-M2X7KQLB.js";import"./chunk-REYR55MP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-42C7ZIID.js";import{a as Pt,b as Mt,e as w}from"./chunk-JHI3MBHO.js";var V=(()=>{let i=class i{constructor(t){this.http=t,this.baseUrl="http://localhost:8080/api/v1",this.clientsSubject=new N([]),this.clients$=this.clientsSubject.asObservable()}getClients(t=1,o=10,a){let l=new T().set("page",Math.max(0,t-1).toString()).set("limit",o.toString());return a&&Object.keys(a).forEach(d=>{let f=a[d];if(f!=null&&f!==""){if(typeof f=="string"&&f.trim()==="")return;l=l.set(d,f instanceof Date?f.toISOString():f.toString())}}),this.http.get(`${this.baseUrl}/clients`,{params:l}).pipe(E(d=>{this.clientsSubject.next(d.content)}))}getActiveClients(){return this.http.get(`${this.baseUrl}sessions/clients-actifs`)}getClientsCount(){return this.http.get(`${this.baseUrl}/clients/count`)}getClientsActifCount(){return this.http.get(`${this.baseUrl}/sessions/clients-actifs/count`)}getClient(t){return this.http.get(`${this.baseUrl}/clients/${t}`)}createClient(t){return this.http.post(`${this.baseUrl}/clients`,t).pipe(E(()=>{this.refreshClients()}))}updateClient(t,o){return this.http.put(`${this.baseUrl}/clients/${t}`,o).pipe(E(()=>{this.refreshClients()}))}deleteClient(t){return this.http.delete(`${this.baseUrl}/clients/${t}`,{responseType:"text"}).pipe(E(()=>{this.refreshClients()}))}getClientReservations(t){return this.http.get(`${this.baseUrl}/clients/${t}/reservations`)}searchClients(t){let o=new T().set("search",t);return this.http.get(`${this.baseUrl}/clients/search`,{params:o})}getClientStats(){return this.http.get(`${this.baseUrl}/clients/stats`)}exportClients(t="csv"){let o=new T().set("format",t);return this.http.get(`${this.baseUrl}/clients/export`,{params:o,responseType:"blob"})}toggleClientStatus(t,o){return this.http.patch(`${this.baseUrl}/clients/${t}/status`,{statut:o}).pipe(E(()=>{this.refreshClients()}))}sendNotification(t,o,a="email"){return this.http.post(`${this.baseUrl}/clients/${t}/notify`,{message:o,type:a})}getTopClients(t=10){let o=new T().set("limit",t.toString());return this.http.get(`${this.baseUrl}/clients/top`,{params:o})}refreshClients(){this.getClients().subscribe()}getMockClients(){return[{id:1,nom:"Ben Ali",prenom:"Ahmed",email:"<EMAIL>",telephone:"+216 20 123 456",cin:12345678},{id:2,nom:"Trabelsi",prenom:"Fatma",email:"<EMAIL>",telephone:"+216 22 987 654",cin:87654321},{id:3,nom:"Karray",prenom:"Mohamed",email:"<EMAIL>",telephone:"+216 25 555 777",cin:11223344}]}};i.\u0275fac=function(o){return new(o||i)($(Y))},i.\u0275prov=F({token:i,factory:i.\u0275fac,providedIn:"root"});let c=i;return c})();var L=(()=>{let i=class i{constructor(t){this.http=t,this.baseUrl="http://localhost:8080/api/v1",this.chauffeursSubject=new N([]),this.chauffeurs$=this.chauffeursSubject.asObservable()}getChauffeurs(t=1,o=100,a){let l=new T().set("page",(t-1).toString()).set("limit",o.toString());return a&&Object.keys(a).forEach(d=>{let f=a[d];f!=null&&f!==""&&(f instanceof Date?l=l.set(d,f.toISOString()):l=l.set(d,f.toString()))}),this.http.get(`${this.baseUrl}/chauffeurs`,{params:l}).pipe(E(d=>this.chauffeursSubject.next(d.content)))}getActiveChauffeurs(){return this.http.get(`${this.baseUrl}/sessions/chauffeurs-actifs`)}getChauffeursCount(){return this.http.get(`${this.baseUrl}/chauffeurs/count`)}getActiveChauffeursCount(){return this.http.get(`${this.baseUrl}/sessions/chauffeurs-actifs/count`)}getChauffeur(t){return this.http.get(`${this.baseUrl}/chauffeurs/${t}`)}deleteChauffeur(t){return this.http.delete(`${this.baseUrl}/chauffeurs/${t}`,{responseType:"text"})}};i.\u0275fac=function(o){return new(o||i)($(Y))},i.\u0275prov=F({token:i,factory:i.\u0275fac,providedIn:"root"});let c=i;return c})();function St(c,i){c&1&&(e(0,"div",13)(1,"h3"),s(2,"Louage Admin"),n(),e(3,"p"),s(4,"Tableau de bord"),n()())}function zt(c,i){c&1&&(e(0,"h4",14),s(1,"Navigation"),n())}function It(c,i){if(c&1&&(e(0,"span",21),s(1),n()),c&2){let r=M().$implicit;g(),_(r.title)}}function Et(c,i){if(c&1&&(e(0,"div",22),s(1),n()),c&2){let r=M().$implicit;g(),k(" ",r.title," ")}}function jt(c,i){if(c&1){let r=y();e(0,"div",15),O("click",function(){let o=m(r).$implicit,a=M();return u(a.navigateTo(o.route))}),e(1,"div",16)(2,"div",17),p(3,"ion-icon",18),n(),P(4,It,2,1,"span",19),n(),P(5,Et,2,1,"div",20),n()}if(c&2){let r=i.$implicit,t=M();h("active",r.active),g(3),C("name",r.icon),g(),C("ngIf",!t.collapsed),g(),C("ngIf",t.collapsed)}}function Tt(c,i){c&1&&(e(0,"div",7)(1,"h4",14),s(2,"Actions rapides"),n(),e(3,"div",23)(4,"button",24),p(5,"ion-icon",25),s(6," Nouveau client "),n(),e(7,"button",26),p(8,"ion-icon",25),s(9," Nouveau chauffeur "),n(),e(10,"button",26),p(11,"ion-icon",27),s(12," Nouvelle station "),n()()())}function Ft(c,i){if(c&1&&(e(0,"div",28)(1,"div",29)(2,"div",30)(3,"div",31)(4,"span",32),s(5),n(),e(6,"span",33),s(7,"Clients actifs"),n()(),e(8,"div",31)(9,"span",32),s(10),n(),e(11,"span",33),s(12,"Chauffeurs actifs"),n()()()()()),c&2){let r=M();g(5),_(r.activeClientsCount),g(5),_(r.activeChauffeursCount)}}var et=(()=>{let i=class i{constructor(t,o,a){this.router=t,this.clientService=o,this.chauffeurService=a,this.collapsed=!1,this.activeClientsCount=0,this.activeChauffeursCount=0,this.menuItems=[{title:"Dashboard",icon:"grid-outline",route:"/admin/dashboard"},{title:"Clients",icon:"people-outline",route:"/admin/clients"},{title:"Chauffeurs",icon:"car-outline",route:"/admin/chauffeurs"},{title:"Stations",icon:"bus-outline",route:"/admin/stations"},{title:"R\xE9clamation",icon:"warning-outline",route:"/admin/reservations"},{title:"Rapports",icon:"bar-chart-outline",route:"/admin/reports"}],this.router.events.pipe(Ot(l=>l instanceof ut)).subscribe(l=>{this.updateActiveMenuItem(l.url)}),this.updateActiveMenuItem(this.router.url)}ngOnInit(){this.hetActiveClients(),this.hetActiveChauffeurs()}updateActiveMenuItem(t){this.menuItems.forEach(o=>{o.active=t.includes(o.route)})}navigateTo(t){this.router.navigate([t])}hetActiveClients(){this.clientService.getClientsActifCount().subscribe({next:t=>{this.activeClientsCount=t,this.menuItems[1].badge=t},error:t=>{console.error("Erreur lors de la r\xE9cup\xE9ration du nombre de clients actifs :",t)}})}hetActiveChauffeurs(){this.chauffeurService.getActiveChauffeursCount().subscribe({next:t=>{this.activeChauffeursCount=t},error:t=>{console.error("Erreur lors de la r\xE9cup\xE9ration du nombre de chauffeurs actifs :",t)}})}};i.\u0275fac=function(o){return new(o||i)(b(W),b(V),b(L))},i.\u0275cmp=S({type:i,selectors:[["app-sidebar"]],inputs:{collapsed:"collapsed"},decls:13,vars:7,consts:[[1,"sidebar-container"],[1,"sidebar-header"],[1,"logo-container"],[1,"logo-icon"],["name","car-sport-outline"],["class","logo-text",4,"ngIf"],[1,"sidebar-nav"],[1,"nav-section"],["class","section-title",4,"ngIf"],[1,"menu-items"],["class","menu-item",3,"active","click",4,"ngFor","ngForOf"],["class","nav-section",4,"ngIf"],["class","sidebar-footer",4,"ngIf"],[1,"logo-text"],[1,"section-title"],[1,"menu-item",3,"click"],[1,"item-content"],[1,"item-icon"],[3,"name"],["class","item-title",4,"ngIf"],["class","tooltip",4,"ngIf"],[1,"item-title"],[1,"tooltip"],[1,"quick-actions"],[1,"action-btn","primary"],["name","people-outline"],[1,"action-btn","secondary"],["name","map-outline"],[1,"sidebar-footer"],[1,"footer-content"],[1,"stats-summary"],[1,"stat-item"],[1,"stat-number"],[1,"stat-label"]],template:function(o,a){o&1&&(e(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3),p(4,"ion-icon",4),n(),P(5,St,5,0,"div",5),n()(),e(6,"nav",6)(7,"div",7),P(8,zt,2,0,"h4",8),e(9,"div",9),P(10,jt,6,5,"div",10),n()(),P(11,Tt,13,0,"div",11),n(),P(12,Ft,13,2,"div",12),n()),o&2&&(h("collapsed",a.collapsed),g(5),C("ngIf",!a.collapsed),g(3),C("ngIf",!a.collapsed),g(2),C("ngForOf",a.menuItems),g(),C("ngIf",!a.collapsed),g(),C("ngIf",!a.collapsed))},dependencies:[v,z,x,j,D],styles:['.sidebar-container[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column;background:transparent;transition:all .3s ease}.sidebar-container.collapsed[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .logo-text[_ngcontent-%COMP%], .sidebar-container.collapsed[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%], .sidebar-container.collapsed[_ngcontent-%COMP%]   .item-title[_ngcontent-%COMP%], .sidebar-container.collapsed[_ngcontent-%COMP%]   .item-badge[_ngcontent-%COMP%], .sidebar-container.collapsed[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%], .sidebar-container.collapsed[_ngcontent-%COMP%]   .sidebar-footer[_ngcontent-%COMP%]{opacity:0;visibility:hidden}.sidebar-container.collapsed[_ngcontent-%COMP%]   .menu-item[_ngcontent-%COMP%]:hover   .tooltip[_ngcontent-%COMP%]{opacity:1;visibility:visible}.sidebar-header[_ngcontent-%COMP%]{padding:24px 20px;border-bottom:1px solid rgba(255,255,255,.1)}.sidebar-header[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.sidebar-header[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-icon[_ngcontent-%COMP%]{width:48px;height:48px;background:#c8102e;border-radius:12px;display:flex;align-items:center;justify-content:center;box-shadow:0 4px 12px #c8102e}.sidebar-header[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;color:#fff}.sidebar-header[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-text[_ngcontent-%COMP%]{transition:all .3s ease}.sidebar-header[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;font-size:18px;font-weight:700;color:#c8102e;line-height:1.2}.sidebar-header[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:12px;color:#718096;font-weight:500}.sidebar-nav[_ngcontent-%COMP%]{flex:1;padding:20px 0;overflow-y:auto}.sidebar-nav[_ngcontent-%COMP%]   .nav-section[_ngcontent-%COMP%]{margin-bottom:32px}.sidebar-nav[_ngcontent-%COMP%]   .nav-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:11px;font-weight:600;text-transform:uppercase;letter-spacing:1px;color:#c8102e;margin:0 20px 16px;transition:all .3s ease}.sidebar-nav[_ngcontent-%COMP%]   .menu-items[_ngcontent-%COMP%]   .menu-item[_ngcontent-%COMP%]{position:relative;margin:4px 12px;border-radius:12px;cursor:pointer;transition:all .3s ease;overflow:hidden}.sidebar-nav[_ngcontent-%COMP%]   .menu-items[_ngcontent-%COMP%]   .menu-item[_ngcontent-%COMP%]:hover{background:#ea66921a;transform:translate(4px)}.sidebar-nav[_ngcontent-%COMP%]   .menu-items[_ngcontent-%COMP%]   .menu-item[_ngcontent-%COMP%]:hover   .item-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#c8102e;transform:scale(1.1)}.sidebar-nav[_ngcontent-%COMP%]   .menu-items[_ngcontent-%COMP%]   .menu-item.active[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea26,#764ba226);border-left:4px solid #c8102e}.sidebar-nav[_ngcontent-%COMP%]   .menu-items[_ngcontent-%COMP%]   .menu-item.active[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#c8102e}.sidebar-nav[_ngcontent-%COMP%]   .menu-items[_ngcontent-%COMP%]   .menu-item.active[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-title[_ngcontent-%COMP%]{color:#c8102e;font-weight:600}.sidebar-nav[_ngcontent-%COMP%]   .menu-items[_ngcontent-%COMP%]   .menu-item[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]{display:flex;align-items:center;padding:12px 16px;gap:12px}.sidebar-nav[_ngcontent-%COMP%]   .menu-items[_ngcontent-%COMP%]   .menu-item[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-icon[_ngcontent-%COMP%]{width:24px;height:24px;display:flex;align-items:center;justify-content:center}.sidebar-nav[_ngcontent-%COMP%]   .menu-items[_ngcontent-%COMP%]   .menu-item[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px;color:#4a5568;transition:all .3s ease}.sidebar-nav[_ngcontent-%COMP%]   .menu-items[_ngcontent-%COMP%]   .menu-item[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-title[_ngcontent-%COMP%]{flex:1;font-size:14px;font-weight:500;color:#4a5568;transition:all .3s ease}.sidebar-nav[_ngcontent-%COMP%]   .menu-items[_ngcontent-%COMP%]   .menu-item[_ngcontent-%COMP%]   .tooltip[_ngcontent-%COMP%]{position:absolute;left:100%;top:50%;transform:translateY(-50%);background:#c8102e;color:#fff;padding:8px 12px;border-radius:8px;font-size:12px;font-weight:500;white-space:nowrap;opacity:0;visibility:hidden;transition:all .3s ease;z-index:1000;margin-left:8px;box-shadow:0 4px 12px #00000026}.sidebar-nav[_ngcontent-%COMP%]   .menu-items[_ngcontent-%COMP%]   .menu-item[_ngcontent-%COMP%]   .tooltip[_ngcontent-%COMP%]:before{content:"";position:absolute;left:-4px;top:50%;transform:translateY(-50%);border:4px solid transparent;border-right-color:#c8102e}.sidebar-nav[_ngcontent-%COMP%]   .menu-items[_ngcontent-%COMP%]   .menu-item[_ngcontent-%COMP%]   .tooltip[_ngcontent-%COMP%]   .tooltip-badge[_ngcontent-%COMP%]{background:#667eea;color:#fff;font-size:10px;padding:2px 6px;border-radius:8px;margin-left:8px}.sidebar-nav[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]{padding:0 20px}.sidebar-nav[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{width:100%;padding:12px 16px;border:none;border-radius:10px;font-size:13px;font-weight:600;cursor:pointer;transition:all .3s ease;display:flex;align-items:center;gap:8px;margin-bottom:8px}.sidebar-nav[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.sidebar-nav[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .action-btn.primary[_ngcontent-%COMP%]{background:#c8102e;color:#fff;box-shadow:0 4px 12px #c8102e}.sidebar-nav[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .action-btn.primary[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 6px 20px #667eea66}.sidebar-nav[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .action-btn.secondary[_ngcontent-%COMP%]{background:#c8102e;color:#fff;border:1px solid rgba(102,126,234,.2)}.sidebar-nav[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .action-btn.secondary[_ngcontent-%COMP%]:hover{background:#c8102e;transform:translateY(-1px)}.sidebar-footer[_ngcontent-%COMP%]{padding:20px;border-top:1px solid rgba(255,255,255,.1)}.sidebar-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .stats-summary[_ngcontent-%COMP%]{background:#fffc;border-radius:12px;padding:16px;box-shadow:0 2px 8px #0000001a}.sidebar-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .stats-summary[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;margin-bottom:12px}.sidebar-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .stats-summary[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.sidebar-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .stats-summary[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-number[_ngcontent-%COMP%]{font-size:20px;font-weight:700;color:#c8102e;line-height:1}.sidebar-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .stats-summary[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%]{font-size:11px;color:#718096;font-weight:500;text-align:center}.sidebar-nav[_ngcontent-%COMP%]::-webkit-scrollbar{width:4px}.sidebar-nav[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:transparent}.sidebar-nav[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#c8102e;border-radius:2px}.sidebar-nav[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#667eea80}.menu-item[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideInFromLeft .3s ease forwards}@keyframes _ngcontent-%COMP%_slideInFromLeft{0%{opacity:0;transform:translate(-20px)}to{opacity:1;transform:translate(0)}}@media (max-width: 768px){.sidebar-container.collapsed[_ngcontent-%COMP%]{width:100%}}']});let c=i;return c})();var ot=(()=>{let i=class i{constructor(t){this.router=t,this.sidebarCollapsed=!1,this.showUserMenu=!1}ngOnInit(){this.checkScreenSize()}toggleSidebar(){this.sidebarCollapsed=!this.sidebarCollapsed}toggleUserMenu(){this.showUserMenu=!this.showUserMenu}logout(){localStorage.clear(),sessionStorage.clear()}onResize(t){this.checkScreenSize()}onDocumentClick(t){!t.target.closest(".user-profile")&&!t.target.closest(".user-menu")&&(this.showUserMenu=!1)}checkScreenSize(){window.innerWidth<768&&(this.sidebarCollapsed=!0)}};i.\u0275fac=function(o){return new(o||i)(b(W))},i.\u0275cmp=S({type:i,selectors:[["app-layout"]],hostBindings:function(o,a){o&1&&O("resize",function(d){return a.onResize(d)},bt)("click",function(d){return a.onDocumentClick(d)},ft)},decls:23,vars:7,consts:[[1,"admin-layout"],[1,"admin-navbar"],[1,"navbar-content"],[1,"navbar-left"],[1,"menu-toggle",3,"click"],["name","menu-outline"],[1,"app-title"],[1,"navbar-right"],[1,"user-profile",3,"click"],["src","assets/img/administrateur.png ","alt","Admin",1,"avatar"],[1,"username"],["name","chevron-down-outline"],[1,"user-menu"],["href","#",1,"menu-item","logout",3,"click"],["name","log-out-outline"],[1,"admin-sidebar"],[3,"collapsed"],[1,"admin-main"],[1,"content-wrapper"]],template:function(o,a){o&1&&(e(0,"div",0)(1,"header",1)(2,"div",2)(3,"div",3)(4,"button",4),O("click",function(){return a.toggleSidebar()}),p(5,"ion-icon",5),n(),e(6,"h1",6),s(7,"LouageTunise"),n()(),e(8,"div",7)(9,"div",8),O("click",function(){return a.toggleUserMenu()}),p(10,"img",9),e(11,"span",10),s(12,"Admin"),n(),p(13,"ion-icon",11),n(),e(14,"div",12)(15,"a",13),O("click",function(){return a.logout()}),p(16,"ion-icon",14),s(17," D\xE9connexion "),n()()()()(),e(18,"aside",15),p(19,"app-sidebar",16),n(),e(20,"main",17)(21,"div",18),p(22,"ion-router-outlet"),n()()()),o&2&&(g(14),h("show",a.showUserMenu),g(4),h("collapsed",a.sidebarCollapsed),g(),C("collapsed",a.sidebarCollapsed),g(),h("sidebar-collapsed",a.sidebarCollapsed))},dependencies:[x,v,z,ht,et],styles:[".admin-layout[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:100vh;background:135deg,#667eea 0%,#764ba2 100%;overflow:hidden}.admin-navbar[_ngcontent-%COMP%]{position:fixed;top:0;left:0;right:0;height:70px;background:#c8102e;-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border-bottom:1px solid rgba(255,255,255,.2);box-shadow:0 4px 20px #0000001a;z-index:1000;transition:all .3s ease}.admin-navbar[_ngcontent-%COMP%]   .navbar-content[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;height:100%;padding:0 24px;max-width:100%}.admin-navbar[_ngcontent-%COMP%]   .navbar-left[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px}.admin-navbar[_ngcontent-%COMP%]   .navbar-left[_ngcontent-%COMP%]   .menu-toggle[_ngcontent-%COMP%]{background:none;border:none;padding:8px;border-radius:8px;cursor:pointer;transition:all .3s ease;color:#4a5568}.admin-navbar[_ngcontent-%COMP%]   .navbar-left[_ngcontent-%COMP%]   .menu-toggle[_ngcontent-%COMP%]:hover{background:#667eea1a;color:#667eea;transform:scale(1.05)}.admin-navbar[_ngcontent-%COMP%]   .navbar-left[_ngcontent-%COMP%]   .menu-toggle[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px}.admin-navbar[_ngcontent-%COMP%]   .navbar-left[_ngcontent-%COMP%]   .app-title[_ngcontent-%COMP%]{font-size:24px;font-weight:700;background:#fff;-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text;margin:0}.admin-navbar[_ngcontent-%COMP%]   .navbar-right[_ngcontent-%COMP%]{display:flex;align-items:center;gap:20px;position:relative}.admin-navbar[_ngcontent-%COMP%]   .navbar-right[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;cursor:pointer;padding:8px 12px;border-radius:12px;transition:all .3s ease;background:#fffc;border:1px solid #c8102e}.admin-navbar[_ngcontent-%COMP%]   .navbar-right[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]:hover{background:#e5e6e9ec;transform:translateY(-2px);box-shadow:0 4px 12px #ffffffe7}.admin-navbar[_ngcontent-%COMP%]   .navbar-right[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .avatar[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;object-fit:cover;border:2px solid #272931}.admin-navbar[_ngcontent-%COMP%]   .navbar-right[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .username[_ngcontent-%COMP%]{font-weight:600;color:#4a5568}.admin-navbar[_ngcontent-%COMP%]   .navbar-right[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#a0aec0;transition:transform .3s ease}.admin-navbar[_ngcontent-%COMP%]   .navbar-right[_ngcontent-%COMP%]   .user-profile.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{transform:rotate(180deg)}.admin-navbar[_ngcontent-%COMP%]   .navbar-right[_ngcontent-%COMP%]   .user-menu[_ngcontent-%COMP%]{position:absolute;top:100%;right:0;background:#fff;border-radius:12px;box-shadow:0 10px 40px #00000026;border:1px solid rgba(0,0,0,.1);min-width:200px;opacity:0;visibility:hidden;transform:translateY(-10px);transition:all .3s ease;z-index:1001}.admin-navbar[_ngcontent-%COMP%]   .navbar-right[_ngcontent-%COMP%]   .user-menu.show[_ngcontent-%COMP%]{opacity:1;visibility:visible;transform:translateY(0)}.admin-navbar[_ngcontent-%COMP%]   .navbar-right[_ngcontent-%COMP%]   .user-menu[_ngcontent-%COMP%]   .menu-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;padding:12px 16px;text-decoration:none;color:#4a5568;transition:all .3s ease;border-radius:8px;margin:4px}.admin-navbar[_ngcontent-%COMP%]   .navbar-right[_ngcontent-%COMP%]   .user-menu[_ngcontent-%COMP%]   .menu-item[_ngcontent-%COMP%]:hover{background:#667eea1a;color:#667eea}.admin-navbar[_ngcontent-%COMP%]   .navbar-right[_ngcontent-%COMP%]   .user-menu[_ngcontent-%COMP%]   .menu-item.logout[_ngcontent-%COMP%]{color:#e53e3e}.admin-navbar[_ngcontent-%COMP%]   .navbar-right[_ngcontent-%COMP%]   .user-menu[_ngcontent-%COMP%]   .menu-item.logout[_ngcontent-%COMP%]:hover{background:#e53e3e1a;color:#e53e3e}.admin-navbar[_ngcontent-%COMP%]   .navbar-right[_ngcontent-%COMP%]   .user-menu[_ngcontent-%COMP%]   .menu-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.admin-navbar[_ngcontent-%COMP%]   .navbar-right[_ngcontent-%COMP%]   .user-menu[_ngcontent-%COMP%]   .menu-divider[_ngcontent-%COMP%]{margin:8px 0;border:none;border-top:1px solid rgba(0,0,0,.1)}.admin-sidebar[_ngcontent-%COMP%]{position:fixed;top:70px;left:0;width:280px;height:calc(100vh - 70px);background:#fffffff2;-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border-right:1px solid rgba(255,255,255,.2);box-shadow:4px 0 20px #0000001a;transition:all .3s ease;z-index:999;overflow-y:auto}.admin-sidebar.collapsed[_ngcontent-%COMP%]{width:80px}.admin-sidebar[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px}.admin-sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:transparent}.admin-sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#667eea4d;border-radius:3px}.admin-sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#667eea80}.admin-main[_ngcontent-%COMP%]{margin-top:70px;margin-left:280px;min-height:calc(100vh - 70px);transition:all .3s ease;background:transparent}.admin-main.sidebar-collapsed[_ngcontent-%COMP%]{margin-left:80px}.admin-main[_ngcontent-%COMP%]   .content-wrapper[_ngcontent-%COMP%]{padding:24px;min-height:100%;background:#ffffff1a;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border-radius:20px 0 0;margin:0}@keyframes _ngcontent-%COMP%_pulse{0%{transform:scale(1)}50%{transform:scale(1.1)}to{transform:scale(1)}}@keyframes _ngcontent-%COMP%_slideInFromTop{0%{opacity:0;transform:translateY(-20px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_slideInFromLeft{0%{opacity:0;transform:translate(-20px)}to{opacity:1;transform:translate(0)}}@media (max-width: 768px){.admin-navbar[_ngcontent-%COMP%]   .navbar-right[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%], .admin-navbar[_ngcontent-%COMP%]   .navbar-right[_ngcontent-%COMP%]   .username[_ngcontent-%COMP%]{display:none}.admin-sidebar[_ngcontent-%COMP%]{width:280px;transform:translate(-100%)}.admin-sidebar.collapsed[_ngcontent-%COMP%]{transform:translate(0);width:100%}.admin-main[_ngcontent-%COMP%], .admin-main.sidebar-collapsed[_ngcontent-%COMP%]{margin-left:0}}@media (max-width: 480px){.admin-navbar[_ngcontent-%COMP%]   .navbar-content[_ngcontent-%COMP%]{padding:0 16px}.admin-navbar[_ngcontent-%COMP%]   .navbar-left[_ngcontent-%COMP%]   .app-title[_ngcontent-%COMP%]{font-size:18px}.admin-navbar[_ngcontent-%COMP%]   .navbar-right[_ngcontent-%COMP%]{gap:12px}.admin-main[_ngcontent-%COMP%]   .content-wrapper[_ngcontent-%COMP%]{padding:16px;border-radius:16px 0 0}}"]});let c=i;return c})();var wt=(()=>{let i=class i{constructor(t){this.http=t,this.baseUrl="http://localhost:8080/api/v1"}getUsersCount(){return this.http.get(`${this.baseUrl}/utilisateurs/count`)}getCarsCount(){return this.http.get(`${this.baseUrl}/vehicules/count`)}getStationsCount(){return this.http.get(`${this.baseUrl}/stations/count`)}};i.\u0275fac=function(o){return new(o||i)($(Y))},i.\u0275prov=F({token:i,factory:i.\u0275fac,providedIn:"root"});let c=i;return c})();var Dt=["revenueChart"],Yt=["bookingsChart"],Ut=()=>[1,2,3,4,5];function Vt(c,i){if(c&1&&(e(0,"div",18)(1,"div",19)(2,"div",20),p(3,"ion-icon",21),n(),e(4,"div",22)(5,"h3",23),s(6),n(),e(7,"p",24),s(8),n()()()()),c&2){let r=i.$implicit;g(2),lt("background",r.gradient),g(),C("name",r.icon),g(3),_(r.value),g(2),_(r.label)}}function Lt(c,i){if(c&1&&(e(0,"div",25)(1,"div",26),p(2,"ion-icon",21),n(),e(3,"div",27)(4,"p",28),s(5),n(),e(6,"span",29),s(7),n()()()),c&2){let r=i.$implicit;g(),lt("background",r.color),g(),C("name",r.icon),g(3),_(r.text),g(2),_(r.time)}}function Nt(c,i){if(c&1&&p(0,"ion-icon",37),c&2){let r=i.$implicit,t=M().$implicit;h("filled",r<=t.rating)}}function At(c,i){if(c&1&&(e(0,"div",30)(1,"div",31)(2,"div",32)(3,"h4"),s(4),n(),e(5,"p"),s(6),n()()(),e(7,"div",33)(8,"div",34),P(9,Nt,1,2,"ion-icon",35),n(),e(10,"span",36),s(11),n()()()),c&2){let r=i.$implicit;g(4),_(r.name),g(2),k("",r.trips," trajets"),g(3),C("ngForOf",mt(4,Ut)),g(2),_(r.rating)}}var it=(()=>{let i=class i{constructor(t,o,a){this.statsService=t,this.clientService=o,this.chauffeurService=a,this.statistics=[{label:"Total Utilisateur",value:"1,247",icon:"people-outline",gradient:"linear-gradient(135deg, #667eea, #764ba2)",change:"+12.5%",changeType:"positive",changeIcon:"trending-up-outline"},{label:"Total Voitures",value:"89",icon:"car-sport-outline",gradient:"linear-gradient(135deg, #f093fb, #f5576c)",change:"+8.2%",changeType:"positive",changeIcon:"trending-up-outline"},{label:"Total Stations",value:"156",icon:"bus-outline",gradient:"linear-gradient(135deg, #4facfe, #00f2fe)",change:"+15.3%",changeType:"positive",changeIcon:"trending-up-outline"}],this.recentActivities=[{text:"Nouveau client inscrit: Ahmed Ben Ali",time:"Il y a 5 minutes",icon:"person-add-outline",color:"linear-gradient(135deg, #667eea, #764ba2)"},{text:"Trajet termin\xE9: Tunis \u2192 Sousse",time:"Il y a 12 minutes",icon:"checkmark-circle-outline",color:"linear-gradient(135deg, #43e97b, #38f9d7)"},{text:"Nouveau chauffeur approuv\xE9: Mohamed Triki",time:"Il y a 25 minutes",icon:"car-outline",color:"linear-gradient(135deg, #f093fb, #f5576c)"},{text:"Paiement re\xE7u: 45 DT",time:"Il y a 1 heure",icon:"cash-outline",color:"linear-gradient(135deg, #4facfe, #00f2fe)"}],this.topPerformers=[{name:"Karim Mansouri",avatar:"assets/images/driver1.jpg",trips:127,rating:5},{name:"Sami Bouazizi",avatar:"assets/images/driver2.jpg",trips:98,rating:5},{name:"Nabil Cherif",avatar:"assets/images/driver3.jpg",trips:87,rating:4},{name:"Youssef Karray",avatar:"assets/images/driver4.jpg",trips:76,rating:4}]}ngOnInit(){this.loadStatistics()}loadStatistics(){this.statsService.getUsersCount().subscribe({next:t=>{this.statistics[0].value=t.toString()},error:t=>{console.error("Erreur chargement clients actifs",t)}}),this.statsService.getCarsCount().subscribe({next:t=>{this.statistics[1].value=t.toString()},error:t=>{console.error("Erreur chargement chauffeurs",t)}}),this.statsService.getStationsCount().subscribe({next:t=>{this.statistics[2].value=t.toString()},error:t=>{console.error("Erreur chargement trajets",t)}})}};i.\u0275fac=function(o){return new(o||i)(b(wt),b(V),b(L))},i.\u0275cmp=S({type:i,selectors:[["app-dashboard"]],viewQuery:function(o,a){if(o&1&&(rt(Dt,5),rt(Yt,5)),o&2){let l;st(l=gt())&&(a.revenueChart=l.first),st(l=gt())&&(a.bookingsChart=l.first)}},decls:27,vars:3,consts:[["fullscreen",""],[1,"dashboard-container"],[1,"dashboard-header"],[1,"header-content"],[1,"welcome-section"],[1,"dashboard-title"],[1,"stats-grid"],["class","stat-card",4,"ngFor","ngForOf"],[1,"bottom-section"],[1,"bottom-grid"],[1,"activity-card"],[1,"card-header"],["href","#",1,"view-all"],[1,"activity-list"],["class","activity-item",4,"ngFor","ngForOf"],[1,"performers-card"],[1,"performers-list"],["class","performer-item",4,"ngFor","ngForOf"],[1,"stat-card"],[1,"stat-content"],[1,"stat-icon"],[3,"name"],[1,"stat-info"],[1,"stat-value"],[1,"stat-label"],[1,"activity-item"],[1,"activity-icon"],[1,"activity-content"],[1,"activity-text"],[1,"activity-time"],[1,"performer-item"],[1,"performer-info"],[1,"performer-details"],[1,"performer-rating"],[1,"rating-stars"],["name","star",3,"filled",4,"ngFor","ngForOf"],[1,"rating-value"],["name","star"]],template:function(o,a){o&1&&(e(0,"ion-content",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"div",4)(5,"h1",5),s(6,"Tableau de bord"),n()()()(),e(7,"div",6),P(8,Vt,9,5,"div",7),n(),e(9,"div",8)(10,"div",9)(11,"div",10)(12,"div",11)(13,"h3"),s(14,"Activit\xE9s r\xE9centes"),n(),e(15,"a",12),s(16,"Voir tout"),n()(),e(17,"div",13),P(18,Lt,8,5,"div",14),n()(),e(19,"div",15)(20,"div",11)(21,"h3"),s(22,"Meilleurs chauffeurs"),n(),e(23,"a",12),s(24,"Voir tout"),n()(),e(25,"div",16),P(26,At,12,5,"div",17),n()()()()()()),o&2&&(g(8),C("ngForOf",a.statistics),g(10),C("ngForOf",a.recentActivities),g(8),C("ngForOf",a.topPerformers))},dependencies:[v,U,z,x,j],styles:['.dashboard-container[_ngcontent-%COMP%]{padding:0;background:transparent;min-height:100vh}.dashboard-header[_ngcontent-%COMP%]{margin-bottom:32px}.dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;gap:24px}.dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .welcome-section[_ngcontent-%COMP%]   .dashboard-title[_ngcontent-%COMP%]{font-size:32px;font-weight:800;color:#c8102e;margin:0 0 8px;text-shadow:0 2px 4px rgba(0,0,0,.1)}.dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]{display:flex;gap:12px}.dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{padding:12px 20px;border:none;border-radius:12px;font-size:14px;font-weight:600;cursor:pointer;transition:all .3s ease;display:flex;align-items:center;gap:8px}.dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.stats-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(280px,1fr));gap:24px;margin-bottom:32px}.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]{background:#fffffff2;-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border-radius:20px;padding:24px;box-shadow:0 8px 32px #0000001a;border:1px solid rgba(255,255,255,.2);transition:all .3s ease;position:relative;overflow:hidden}.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]:before{content:"";position:absolute;top:0;left:0;right:0;height:4px;background:linear-gradient(90deg,#c8102e)}.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]:hover{transform:translateY(-8px);box-shadow:0 16px 48px #00000026}.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]{display:flex;align-items:center;gap:20px}.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]{width:64px;height:64px;border-radius:16px;display:flex;align-items:center;justify-content:center;box-shadow:0 4px 16px #0000001a}.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:28px;color:#fff}.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]{flex:1}.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%]{font-size:28px;font-weight:800;color:#2d3748;margin:0 0 4px;line-height:1}.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%]{font-size:14px;color:#718096;margin:0 0 8px;font-weight:500}.analytics-section[_ngcontent-%COMP%]{margin-bottom:32px}.analytics-section[_ngcontent-%COMP%]   .analytics-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(400px,1fr));gap:24px}.analytics-section[_ngcontent-%COMP%]   .analytics-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]{background:#fffffff2;-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border-radius:20px;padding:24px;box-shadow:0 8px 32px #0000001a;border:1px solid rgba(255,255,255,.2);transition:all .3s ease}.analytics-section[_ngcontent-%COMP%]   .analytics-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 12px 40px #00000026}.analytics-section[_ngcontent-%COMP%]   .analytics-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:20px}.analytics-section[_ngcontent-%COMP%]   .analytics-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:18px;font-weight:700;color:#2d3748;margin:0}.analytics-section[_ngcontent-%COMP%]   .analytics-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .chart-actions[_ngcontent-%COMP%]   .chart-btn[_ngcontent-%COMP%]{background:none;border:none;padding:8px;border-radius:8px;cursor:pointer;color:#718096;transition:all .3s ease}.analytics-section[_ngcontent-%COMP%]   .analytics-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .chart-actions[_ngcontent-%COMP%]   .chart-btn[_ngcontent-%COMP%]:hover{background:#667eea1a;color:#667eea}.analytics-section[_ngcontent-%COMP%]   .analytics-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .chart-actions[_ngcontent-%COMP%]   .chart-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}.analytics-section[_ngcontent-%COMP%]   .analytics-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]{height:200px;position:relative}.analytics-section[_ngcontent-%COMP%]   .analytics-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   canvas[_ngcontent-%COMP%]{width:100%;height:100%;border-radius:12px}.bottom-section[_ngcontent-%COMP%]   .bottom-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(400px,1fr));gap:24px}.bottom-section[_ngcontent-%COMP%]   .bottom-grid[_ngcontent-%COMP%]   .activity-card[_ngcontent-%COMP%], .bottom-section[_ngcontent-%COMP%]   .bottom-grid[_ngcontent-%COMP%]   .performers-card[_ngcontent-%COMP%]{background:#fffffff2;-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border-radius:20px;padding:24px;box-shadow:0 8px 32px #0000001a;border:1px solid rgba(255,255,255,.2);transition:all .3s ease}.bottom-section[_ngcontent-%COMP%]   .bottom-grid[_ngcontent-%COMP%]   .activity-card[_ngcontent-%COMP%]:hover, .bottom-section[_ngcontent-%COMP%]   .bottom-grid[_ngcontent-%COMP%]   .performers-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 12px 40px #00000026}.bottom-section[_ngcontent-%COMP%]   .bottom-grid[_ngcontent-%COMP%]   .activity-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%], .bottom-section[_ngcontent-%COMP%]   .bottom-grid[_ngcontent-%COMP%]   .performers-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:20px}.bottom-section[_ngcontent-%COMP%]   .bottom-grid[_ngcontent-%COMP%]   .activity-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .bottom-section[_ngcontent-%COMP%]   .bottom-grid[_ngcontent-%COMP%]   .performers-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:18px;font-weight:700;color:#2d3748;margin:0}.bottom-section[_ngcontent-%COMP%]   .bottom-grid[_ngcontent-%COMP%]   .activity-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .view-all[_ngcontent-%COMP%], .bottom-section[_ngcontent-%COMP%]   .bottom-grid[_ngcontent-%COMP%]   .performers-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .view-all[_ngcontent-%COMP%]{color:#667eea;text-decoration:none;font-size:14px;font-weight:600;transition:all .3s ease}.bottom-section[_ngcontent-%COMP%]   .bottom-grid[_ngcontent-%COMP%]   .activity-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .view-all[_ngcontent-%COMP%]:hover, .bottom-section[_ngcontent-%COMP%]   .bottom-grid[_ngcontent-%COMP%]   .performers-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .view-all[_ngcontent-%COMP%]:hover{color:#764ba2}.bottom-section[_ngcontent-%COMP%]   .bottom-grid[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px;padding:16px 0;border-bottom:1px solid rgba(0,0,0,.05)}.bottom-section[_ngcontent-%COMP%]   .bottom-grid[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.bottom-section[_ngcontent-%COMP%]   .bottom-grid[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-icon[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:10px;display:flex;align-items:center;justify-content:center;box-shadow:0 2px 8px #0000001a}.bottom-section[_ngcontent-%COMP%]   .bottom-grid[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px;color:#fff}.bottom-section[_ngcontent-%COMP%]   .bottom-grid[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]{flex:1}.bottom-section[_ngcontent-%COMP%]   .bottom-grid[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .activity-text[_ngcontent-%COMP%]{font-size:14px;color:#4a5568;margin:0 0 4px;font-weight:500}.bottom-section[_ngcontent-%COMP%]   .bottom-grid[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .activity-time[_ngcontent-%COMP%]{font-size:12px;color:#a0aec0;font-weight:400}.bottom-section[_ngcontent-%COMP%]   .bottom-grid[_ngcontent-%COMP%]   .performers-list[_ngcontent-%COMP%]   .performer-item[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:16px 0;border-bottom:1px solid rgba(0,0,0,.05)}.bottom-section[_ngcontent-%COMP%]   .bottom-grid[_ngcontent-%COMP%]   .performers-list[_ngcontent-%COMP%]   .performer-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.bottom-section[_ngcontent-%COMP%]   .bottom-grid[_ngcontent-%COMP%]   .performers-list[_ngcontent-%COMP%]   .performer-item[_ngcontent-%COMP%]   .performer-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.bottom-section[_ngcontent-%COMP%]   .bottom-grid[_ngcontent-%COMP%]   .performers-list[_ngcontent-%COMP%]   .performer-item[_ngcontent-%COMP%]   .performer-info[_ngcontent-%COMP%]   .performer-avatar[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;object-fit:cover;border:2px solid #667eea}.bottom-section[_ngcontent-%COMP%]   .bottom-grid[_ngcontent-%COMP%]   .performers-list[_ngcontent-%COMP%]   .performer-item[_ngcontent-%COMP%]   .performer-info[_ngcontent-%COMP%]   .performer-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:#2d3748;margin:0 0 2px}.bottom-section[_ngcontent-%COMP%]   .bottom-grid[_ngcontent-%COMP%]   .performers-list[_ngcontent-%COMP%]   .performer-item[_ngcontent-%COMP%]   .performer-info[_ngcontent-%COMP%]   .performer-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:12px;color:#718096;margin:0}.bottom-section[_ngcontent-%COMP%]   .bottom-grid[_ngcontent-%COMP%]   .performers-list[_ngcontent-%COMP%]   .performer-item[_ngcontent-%COMP%]   .performer-rating[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.bottom-section[_ngcontent-%COMP%]   .bottom-grid[_ngcontent-%COMP%]   .performers-list[_ngcontent-%COMP%]   .performer-item[_ngcontent-%COMP%]   .performer-rating[_ngcontent-%COMP%]   .rating-stars[_ngcontent-%COMP%]{display:flex;gap:2px}.bottom-section[_ngcontent-%COMP%]   .bottom-grid[_ngcontent-%COMP%]   .performers-list[_ngcontent-%COMP%]   .performer-item[_ngcontent-%COMP%]   .performer-rating[_ngcontent-%COMP%]   .rating-stars[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:14px;color:#e2e8f0}.bottom-section[_ngcontent-%COMP%]   .bottom-grid[_ngcontent-%COMP%]   .performers-list[_ngcontent-%COMP%]   .performer-item[_ngcontent-%COMP%]   .performer-rating[_ngcontent-%COMP%]   .rating-stars[_ngcontent-%COMP%]   ion-icon.filled[_ngcontent-%COMP%]{color:#fbbf24}.bottom-section[_ngcontent-%COMP%]   .bottom-grid[_ngcontent-%COMP%]   .performers-list[_ngcontent-%COMP%]   .performer-item[_ngcontent-%COMP%]   .performer-rating[_ngcontent-%COMP%]   .rating-value[_ngcontent-%COMP%]{font-size:12px;font-weight:600;color:#4a5568}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(30px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_slideInFromRight{0%{opacity:0;transform:translate(30px)}to{opacity:1;transform:translate(0)}}.stat-card[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .6s ease forwards}.chart-card[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideInFromRight .8s ease forwards}.activity-card[_ngcontent-%COMP%], .performers-card[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp 1s ease forwards}@media (max-width: 1200px){.analytics-grid[_ngcontent-%COMP%], .bottom-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}}@media (max-width: 768px){.dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch;gap:16px}.dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .welcome-section[_ngcontent-%COMP%]   .dashboard-title[_ngcontent-%COMP%]{font-size:24px}.dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .welcome-section[_ngcontent-%COMP%]   .dashboard-subtitle[_ngcontent-%COMP%]{font-size:14px}.dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]{justify-content:center}.dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{flex:1;justify-content:center}.stats-grid[_ngcontent-%COMP%], .analytics-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:16px}.analytics-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]{height:150px}.bottom-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:16px}}@media (max-width: 480px){.dashboard-container[_ngcontent-%COMP%]{padding:0}.stat-card[_ngcontent-%COMP%]{padding:16px}.stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]{gap:12px}.stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]{width:48px;height:48px}.stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}.stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%]{font-size:20px}.chart-card[_ngcontent-%COMP%], .activity-card[_ngcontent-%COMP%], .performers-card[_ngcontent-%COMP%]{padding:16px}.header-actions[_ngcontent-%COMP%]{flex-direction:column}.header-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{width:100%}}']});let c=i;return c})();function Rt(c,i){c&1&&p(0,"ion-icon",30)}function Bt(c,i){c&1&&p(0,"ion-icon",31)}function qt(c,i){if(c&1){let r=y();e(0,"tr")(1,"td")(2,"div",32)(3,"div",33)(4,"h4"),s(5),n()()()(),e(6,"td")(7,"div",34)(8,"p"),s(9),n(),e(10,"p"),s(11),n()()(),e(12,"td"),s(13),n(),e(14,"td")(15,"div",35)(16,"button",36),O("click",function(){let o=m(r).$implicit,a=M();return u(a.deleteClient(o))}),p(17,"ion-icon",37),n()()()()}if(c&2){let r=i.$implicit;g(5),R("",r.prenom," ",r.nom),g(4),_(r.email),g(2),_(r.telephone),g(2),_(r.cin)}}function Ht(c,i){if(c&1){let r=y();e(0,"button",44),O("click",function(){let o=m(r).$implicit,a=M(2);return u(a.goToPage(o))}),s(1),n()}if(c&2){let r=i.$implicit,t=M(2);h("active",r===t.currentPage),g(),k(" ",r," ")}}function Wt(c,i){if(c&1){let r=y();e(0,"div",38)(1,"button",39),O("click",function(){m(r);let o=M();return u(o.goToPage(o.currentPage-1))}),p(2,"ion-icon",40),n(),e(3,"div",41),P(4,Ht,2,3,"button",42),n(),e(5,"button",39),O("click",function(){m(r);let o=M();return u(o.goToPage(o.currentPage+1))}),p(6,"ion-icon",43),n()()}if(c&2){let r=M();g(),C("disabled",r.currentPage===1),g(3),C("ngForOf",r.getPageNumbers()),g(),C("disabled",r.currentPage===r.totalPages)}}var at=(()=>{let i=class i{constructor(t,o,a){this.clientService=t,this.alertController=o,this.toastController=a,this.clients=[],this.filteredClients=[],this.paginatedClients=[],this.totalClients=0,this.activeClients=0,this.newClientsThisMonth=0,this.averageRating=0,this.searchQuery="",this.currentPage=1,this.itemsPerPage=10,this.totalPages=1,this.sortField="",this.sortDirection="asc",this.isLoading=!1}ngOnInit(){this.loadClients()}loadClients(){this.isLoading=!0,this.clientService.getClients(this.currentPage,this.itemsPerPage).subscribe({next:t=>{let o=t.content;this.clients=o.map(a=>Mt(Pt({},a),{nombreTrajets:a.historiqueReservation?.length||0,noteGlobale:4.5})),this.filteredClients=[...this.clients],this.totalClients=t.totalElements,this.totalPages=t.totalPages,this.calculateStatistics(),this.updatePagination(),this.isLoading=!1},error:t=>{console.error("Erreur lors du chargement des clients :",t),this.clients=this.clientService.getMockClients(),this.filteredClients=[...this.clients],this.calculateStatistics(),this.updatePagination(),this.isLoading=!1}})}calculateStatistics(){this.totalClients=this.clients.length,this.clientService.getClientsActifCount().subscribe({next:l=>{this.activeClients=l},error:l=>{console.error("Erreur lors de la r\xE9cup\xE9ration du nombre de clients actifs :",l)}});let t=new Date().getMonth(),o=new Date().getFullYear();this.newClientsThisMonth=this.clients.filter(l=>{if(!l.dateInscription)return!1;let d=new Date(l.dateInscription);return d.getMonth()===t&&d.getFullYear()===o}).length;let a=this.clients.filter(l=>l.noteGlobale);if(a.length>0){let l=a.reduce((d,f)=>d+(f.noteGlobale||0),0);this.averageRating=Math.round(l/a.length*10)/10}}onSearch(){this.applyFilters()}applyFilters(){this.filteredClients=this.clients.filter(t=>{let o=this.searchQuery.toLowerCase();return!this.searchQuery||t.nom.toLowerCase().includes(o)||t.prenom.toLowerCase().includes(o)||t.email.toLowerCase().includes(o)||t.telephone.includes(this.searchQuery)}),this.currentPage=1,this.updatePagination()}sortBy(t){this.sortField===t?this.sortDirection=this.sortDirection==="asc"?"desc":"asc":(this.sortField=t,this.sortDirection="asc"),this.filteredClients.sort((o,a)=>{let l=o[t],d=a[t];return t==="dateInscription"&&(l=new Date(l).getTime(),d=new Date(d).getTime()),this.sortDirection==="asc"?l-d:d-l}),this.updatePagination()}updatePagination(){this.totalPages=Math.ceil(this.filteredClients.length/this.itemsPerPage);let t=(this.currentPage-1)*this.itemsPerPage;this.paginatedClients=this.filteredClients.slice(t,t+this.itemsPerPage)}goToPage(t){t>=1&&t<=this.totalPages&&(this.currentPage=t,this.updatePagination())}getPageNumbers(){let t=[],a=Math.max(1,this.currentPage-Math.floor(2.5)),l=Math.min(this.totalPages,a****);l-a<4&&(a=Math.max(1,l-5+1));for(let d=a;d<=l;d++)t.push(d);return t}deleteClient(t){return w(this,null,function*(){yield(yield this.alertController.create({header:"Confirmer la suppression",message:`\xCAtes-vous s\xFBr de vouloir supprimer le client ${t.prenom} ${t.nom} ?`,buttons:[{text:"Annuler",role:"cancel"},{text:"Supprimer",role:"destructive",handler:()=>this.performDeleteClient(t)}]})).present()})}performDeleteClient(t){return w(this,null,function*(){this.clientService.deleteClient(t.id).subscribe({next:()=>w(this,null,function*(){this.clients=this.clients.filter(a=>a.id!==t.id),this.applyFilters(),this.calculateStatistics(),(yield this.toastController.create({message:`Client ${t.prenom} ${t.nom} supprim\xE9 avec succ\xE8s`,duration:2e3,color:"success"})).present()}),error:o=>w(this,null,function*(){console.error("Erreur HTTP lors de la suppression du client :",o),(yield this.toastController.create({message:"Erreur lors de la suppression du client",duration:2e3,color:"danger"})).present()})})})}};i.\u0275fac=function(o){return new(o||i)(b(V),b(Z),b(tt))},i.\u0275cmp=S({type:i,selectors:[["app-clients"]],decls:65,vars:10,consts:[["fullscreen",""],[1,"clients-container"],[1,"page-header"],[1,"header-content"],[1,"title-section"],[1,"page-title"],[1,"stats-row"],[1,"stat-card"],[1,"stat-icon","clients"],["name","people-outline"],[1,"stat-info"],[1,"stat-icon","active"],["name","checkmark-circle-outline"],[1,"stat-icon","new"],["name","person-add-outline"],[1,"stat-icon","rating"],["name","star-outline"],[1,"filters-section"],[1,"search-container"],["name","search-outline"],["type","text","placeholder","Rechercher par nom, email ou t\xE9l\xE9phone...",1,"search-input",3,"ngModelChange","input","ngModel"],[1,"table-container"],[1,"table-header"],[1,"table-wrapper"],[1,"clients-table"],[1,"sortable",3,"click"],["name","chevron-up-outline",4,"ngIf"],["name","chevron-down-outline",4,"ngIf"],[4,"ngFor","ngForOf"],["class","pagination",4,"ngIf"],["name","chevron-up-outline"],["name","chevron-down-outline"],[1,"client-info"],[1,"client-details"],[1,"contact-info"],[1,"action-buttons"],["title","Supprimer",1,"action-btn-small","delete",3,"click"],["name","trash-outline"],[1,"pagination"],[1,"page-btn",3,"click","disabled"],["name","chevron-back-outline"],[1,"page-numbers"],["class","page-number",3,"active","click",4,"ngFor","ngForOf"],["name","chevron-forward-outline"],[1,"page-number",3,"click"]],template:function(o,a){o&1&&(e(0,"ion-content",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"div",4)(5,"h1",5),s(6,"Gestion des Clients"),n()()()(),e(7,"div",6)(8,"div",7)(9,"div",8),p(10,"ion-icon",9),n(),e(11,"div",10)(12,"h3"),s(13),n(),e(14,"p"),s(15,"Total Clients"),n()()(),e(16,"div",7)(17,"div",11),p(18,"ion-icon",12),n(),e(19,"div",10)(20,"h3"),s(21),n(),e(22,"p"),s(23,"Clients Actifs"),n()()(),e(24,"div",7)(25,"div",13),p(26,"ion-icon",14),n(),e(27,"div",10)(28,"h3"),s(29),n(),e(30,"p"),s(31,"Nouveaux ce mois"),n()()(),e(32,"div",7)(33,"div",15),p(34,"ion-icon",16),n(),e(35,"div",10)(36,"h3"),s(37),n(),e(38,"p"),s(39,"Note moyenne"),n()()()(),e(40,"div",17)(41,"div",18),p(42,"ion-icon",19),e(43,"input",20),H("ngModelChange",function(d){return q(a.searchQuery,d)||(a.searchQuery=d),d}),O("input",function(){return a.onSearch()}),n()()(),e(44,"div",21)(45,"div",22)(46,"h3"),s(47),n()(),e(48,"div",23)(49,"table",24)(50,"thead")(51,"tr")(52,"th",25),O("click",function(){return a.sortBy("nom")}),s(53," Nom "),P(54,Rt,1,0,"ion-icon",26)(55,Bt,1,0,"ion-icon",27),n(),e(56,"th"),s(57,"Contact"),n(),e(58,"th"),s(59,"CIN"),n(),e(60,"th"),s(61,"Actions"),n()()(),e(62,"tbody"),P(63,qt,18,5,"tr",28),n()()(),P(64,Wt,7,3,"div",29),n()()()),o&2&&(g(13),_(a.totalClients),g(8),_(a.activeClients),g(8),_(a.newClientsThisMonth),g(8),_(a.averageRating),g(6),B("ngModel",a.searchQuery),g(4),k("Liste des Clients (",a.filteredClients.length,")"),g(7),C("ngIf",a.sortField==="nom"&&a.sortDirection==="asc"),g(),C("ngIf",a.sortField==="nom"&&a.sortDirection==="desc"),g(8),C("ngForOf",a.paginatedClients),g(),C("ngIf",a.totalPages>1))},dependencies:[v,U,z,x,j,D,X,G,K,J],styles:['.clients-container[_ngcontent-%COMP%]{padding:0;background:transparent;min-height:100vh}.page-header[_ngcontent-%COMP%]{margin-bottom:32px}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;gap:24px}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .title-section[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%]{font-size:32px;font-weight:800;color:#c8102e;margin:0 0 8px;text-shadow:0 2px 4px rgba(0,0,0,.1)}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .title-section[_ngcontent-%COMP%]   .page-subtitle[_ngcontent-%COMP%]{font-size:16px;color:#fffc;margin:0;font-weight:400}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]{display:flex;gap:12px}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{padding:12px 20px;border:none;border-radius:12px;font-size:14px;font-weight:600;cursor:pointer;transition:all .3s ease;display:flex;align-items:center;gap:8px}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn.primary[_ngcontent-%COMP%]{background:#fff3;color:#fff;border:1px solid rgba(255,255,255,.3);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn.primary[_ngcontent-%COMP%]:hover{background:#ffffff4d;transform:translateY(-2px);box-shadow:0 8px 25px #00000026}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn.secondary[_ngcontent-%COMP%]{background:#ffffff1a;color:#fff;border:1px solid rgba(255,255,255,.2)}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn.secondary[_ngcontent-%COMP%]:hover{background:#fff3;transform:translateY(-1px)}.stats-row[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:20px;margin-bottom:32px}.stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]{background:#fffffff2;-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border-radius:16px;padding:20px;display:flex;align-items:center;gap:16px;box-shadow:0 8px 32px #0000001a;border:1px solid rgba(255,255,255,.2);transition:all .3s ease}.stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 12px 40px #00000026}.stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]{width:48px;height:48px;border-radius:12px;display:flex;align-items:center;justify-content:center;box-shadow:0 4px 12px #0000001a}.stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;color:#fff}.stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-icon.clients[_ngcontent-%COMP%]{background:linear-gradient(135deg,#c8102e,#a00d26)}.stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-icon.active[_ngcontent-%COMP%]{background:linear-gradient(135deg,#43e97b,#38f9d7)}.stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-icon.new[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f093fb,#f5576c)}.stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-icon.rating[_ngcontent-%COMP%]{background:linear-gradient(135deg,#fbbf24,#f59e0b)}.stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:24px;font-weight:700;color:#2d3748;margin:0 0 4px;line-height:1}.stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:14px;color:#718096;margin:0;font-weight:500}.filters-section[_ngcontent-%COMP%]{background:#fffffff2;-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border-radius:16px;padding:24px;margin-bottom:24px;box-shadow:0 8px 32px #0000001a;border:1px solid rgba(255,255,255,.2)}.filters-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]{display:flex;align-items:center;background:#fffc;border-radius:12px;padding:12px 16px;border:1px solid rgba(200,16,46,.2);margin-bottom:20px;transition:all .3s ease}.filters-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]:focus-within{border-color:#c8102e;box-shadow:0 0 0 3px #c8102e1a}.filters-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#a0aec0;margin-right:12px;font-size:20px}.filters-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%]{border:none;outline:none;background:transparent;flex:1;color:#4a5568;font-size:14px}.filters-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%]::placeholder{color:#a0aec0}.filters-section[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]{display:flex;gap:16px;align-items:center;flex-wrap:wrap}.filters-section[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filter-select[_ngcontent-%COMP%]{padding:10px 16px;border:1px solid rgba(200,16,46,.2);border-radius:8px;background:#fff;color:#4a5568;font-size:14px;cursor:pointer;transition:all .3s ease}.filters-section[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filter-select[_ngcontent-%COMP%]:focus{outline:none;border-color:#c8102e;box-shadow:0 0 0 3px #c8102e1a}.filters-section[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filter-btn[_ngcontent-%COMP%]{padding:10px 16px;background:#c8102e1a;border:1px solid rgba(200,16,46,.2);border-radius:8px;color:#c8102e;font-size:14px;font-weight:600;cursor:pointer;transition:all .3s ease;display:flex;align-items:center;gap:8px}.filters-section[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filter-btn[_ngcontent-%COMP%]:hover{background:#c8102e26;transform:translateY(-1px)}.filters-section[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filter-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.table-container[_ngcontent-%COMP%]{background:#fffffff2;-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border-radius:16px;box-shadow:0 8px 32px #0000001a;border:1px solid rgba(255,255,255,.2);overflow:hidden}.table-container[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:24px;border-bottom:1px solid rgba(0,0,0,.05)}.table-container[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:18px;font-weight:700;color:#2d3748;margin:0}.table-container[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .table-actions[_ngcontent-%COMP%]   .table-btn[_ngcontent-%COMP%]{padding:8px;background:#c8102e1a;border:1px solid rgba(200,16,46,.2);border-radius:8px;color:#c8102e;cursor:pointer;transition:all .3s ease}.table-container[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .table-actions[_ngcontent-%COMP%]   .table-btn[_ngcontent-%COMP%]:hover{background:#c8102e26;transform:translateY(-1px)}.table-container[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .table-actions[_ngcontent-%COMP%]   .table-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]{overflow-x:auto}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]{width:100%;border-collapse:collapse}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]{background:#c8102e0d}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{padding:16px;text-align:left;font-weight:600;color:#4a5568;font-size:14px;border-bottom:1px solid rgba(0,0,0,.05);white-space:nowrap}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%]{cursor:pointer;-webkit-user-select:none;user-select:none;transition:all .3s ease}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%]:hover{background:#c8102e1a}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-left:4px;font-size:12px;opacity:.6}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]{transition:all .3s ease}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover{background:#c8102e05}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr.selected[_ngcontent-%COMP%]{background:#c8102e0d}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{padding:16px;border-bottom:1px solid rgba(0,0,0,.05);vertical-align:middle}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .client-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .client-info[_ngcontent-%COMP%]   .client-avatar[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;object-fit:cover;border:2px solid #c8102e}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .client-info[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:#2d3748;margin:0 0 2px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .client-info[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:12px;color:#718096;margin:0}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .contact-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:13px;color:#4a5568;margin:0 0 2px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .contact-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child{margin:0}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .trips-count[_ngcontent-%COMP%]{background:#c8102e1a;color:#c8102e;padding:4px 8px;border-radius:6px;font-size:12px;font-weight:600}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .rating[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .rating[_ngcontent-%COMP%]   .star[_ngcontent-%COMP%]{font-size:14px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .rating[_ngcontent-%COMP%]   .star.filled[_ngcontent-%COMP%]{color:#fbbf24}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .rating[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:13px;font-weight:600;color:#4a5568}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .no-rating[_ngcontent-%COMP%]{color:#a0aec0;font-style:italic}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%]{padding:4px 12px;border-radius:20px;font-size:12px;font-weight:600;text-transform:capitalize}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .status-badge.actif[_ngcontent-%COMP%]{background:#38b2ac1a;color:#38b2ac}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .status-badge.inactif[_ngcontent-%COMP%]{background:#a0aec01a;color:#a0aec0}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .status-badge.suspendu[_ngcontent-%COMP%]{background:#f565651a;color:#f56565}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]{display:flex;gap:8px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn-small[_ngcontent-%COMP%]{width:32px;height:32px;border:none;border-radius:6px;cursor:pointer;display:flex;align-items:center;justify-content:center;transition:all .3s ease}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn-small[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn-small.view[_ngcontent-%COMP%]{background:#c8102e1a;color:#c8102e}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn-small.view[_ngcontent-%COMP%]:hover{background:#c8102e33;transform:translateY(-1px)}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn-small.edit[_ngcontent-%COMP%]{background:#38b2ac1a;color:#38b2ac}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn-small.edit[_ngcontent-%COMP%]:hover{background:#38b2ac33;transform:translateY(-1px)}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn-small.delete[_ngcontent-%COMP%]{background:#f565651a;color:#f56565}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn-small.delete[_ngcontent-%COMP%]:hover{background:#f5656533;transform:translateY(-1px)}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(300px,1fr));gap:20px;padding:24px}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]{background:#fff;border-radius:16px;overflow:hidden;box-shadow:0 4px 16px #00000014;border:1px solid rgba(0,0,0,.05);transition:all .3s ease}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 8px 32px #0000001f}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]{position:relative;padding:20px;background:linear-gradient(135deg,#c8102e,#a00d26);text-align:center}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .client-photo[_ngcontent-%COMP%]{width:80px;height:80px;border-radius:50%;object-fit:cover;border:4px solid white;box-shadow:0 4px 16px #0000001a}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .client-status[_ngcontent-%COMP%]{position:absolute;top:16px;right:16px}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .client-status[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%]{padding:4px 12px;border-radius:20px;font-size:12px;font-weight:600;text-transform:capitalize;background:#fff3;color:#fff;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]{padding:20px}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:18px;font-weight:700;color:#2d3748;margin:0 0 12px;text-align:center}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-email[_ngcontent-%COMP%], .table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-phone[_ngcontent-%COMP%], .table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-city[_ngcontent-%COMP%]{font-size:14px;color:#718096;margin:0 0 8px;display:flex;align-items:center;justify-content:center;gap:8px}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-email[_ngcontent-%COMP%]:before, .table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-phone[_ngcontent-%COMP%]:before, .table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-city[_ngcontent-%COMP%]:before{content:"";width:4px;height:4px;background:#cbd5e0;border-radius:50%}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-stats[_ngcontent-%COMP%]{display:flex;justify-content:space-around;margin-top:16px;padding-top:16px;border-top:1px solid rgba(0,0,0,.05)}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-stats[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]{text-align:center}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-stats[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%]{display:block;font-size:20px;font-weight:700;color:#c8102e;margin-bottom:4px}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-stats[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%]{font-size:12px;color:#a0aec0;font-weight:500}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]{display:flex;justify-content:center;gap:12px;padding:16px 20px 20px}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-btn[_ngcontent-%COMP%]{width:36px;height:36px;border:none;border-radius:8px;cursor:pointer;display:flex;align-items:center;justify-content:center;transition:all .3s ease}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-btn.view[_ngcontent-%COMP%]{background:#c8102e1a;color:#c8102e}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-btn.view[_ngcontent-%COMP%]:hover{background:#c8102e33;transform:translateY(-2px)}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-btn.edit[_ngcontent-%COMP%]{background:#38b2ac1a;color:#38b2ac}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-btn.edit[_ngcontent-%COMP%]:hover{background:#38b2ac33;transform:translateY(-2px)}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-btn.delete[_ngcontent-%COMP%]{background:#f565651a;color:#f56565}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-btn.delete[_ngcontent-%COMP%]:hover{background:#f5656533;transform:translateY(-2px)}.table-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;gap:8px;padding:24px;border-top:1px solid rgba(0,0,0,.05)}.table-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-btn[_ngcontent-%COMP%]{width:40px;height:40px;border:1px solid rgba(200,16,46,.2);background:#fff;border-radius:8px;cursor:pointer;display:flex;align-items:center;justify-content:center;transition:all .3s ease;color:#c8102e}.table-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-btn[_ngcontent-%COMP%]:hover:not(:disabled){background:#c8102e1a;transform:translateY(-1px)}.table-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-btn[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed}.table-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.table-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-numbers[_ngcontent-%COMP%]{display:flex;gap:4px}.table-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-numbers[_ngcontent-%COMP%]   .page-number[_ngcontent-%COMP%]{width:40px;height:40px;border:1px solid rgba(200,16,46,.2);background:#fff;border-radius:8px;cursor:pointer;display:flex;align-items:center;justify-content:center;transition:all .3s ease;color:#c8102e;font-weight:600;font-size:14px}.table-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-numbers[_ngcontent-%COMP%]   .page-number[_ngcontent-%COMP%]:hover{background:#c8102e1a;transform:translateY(-1px)}.table-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-numbers[_ngcontent-%COMP%]   .page-number.active[_ngcontent-%COMP%]{background:#c8102e;color:#fff;border-color:#c8102e}@media (max-width: 1200px){.stats-row[_ngcontent-%COMP%]{grid-template-columns:repeat(2,1fr)}.grid-view[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fill,minmax(280px,1fr))}}@media (max-width: 768px){.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch;gap:16px}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .title-section[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%]{font-size:24px}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .title-section[_ngcontent-%COMP%]   .page-subtitle[_ngcontent-%COMP%]{font-size:14px}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]{justify-content:center}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{flex:1;justify-content:center}.stats-row[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:16px}.filters-section[_ngcontent-%COMP%]{padding:16px}.filters-section[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch}.filters-section[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filter-select[_ngcontent-%COMP%], .filters-section[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filter-btn[_ngcontent-%COMP%]{width:100%}.table-container[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]{padding:16px;flex-direction:column;gap:12px;align-items:stretch}.table-container[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .table-actions[_ngcontent-%COMP%]{display:flex;justify-content:center}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]{font-size:12px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{padding:8px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .client-info[_ngcontent-%COMP%]   .client-avatar[_ngcontent-%COMP%]{width:32px;height:32px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .client-info[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:12px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .client-info[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:10px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn-small[_ngcontent-%COMP%]{width:28px;height:28px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn-small[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:14px}.grid-view[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:16px;padding:16px}.pagination[_ngcontent-%COMP%]{padding:16px;flex-wrap:wrap}.pagination[_ngcontent-%COMP%]   .page-numbers[_ngcontent-%COMP%]{order:-1;width:100%;justify-content:center;margin-bottom:12px}}@media (max-width: 480px){.clients-container[_ngcontent-%COMP%]{padding:0}.filters-section[_ngcontent-%COMP%]{margin:0 0 16px;border-radius:12px}.table-container[_ngcontent-%COMP%]{border-radius:12px}.client-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]{padding:16px}.client-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .client-photo[_ngcontent-%COMP%]{width:60px;height:60px}.client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]{padding:16px}.client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:16px}.client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-email[_ngcontent-%COMP%], .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-phone[_ngcontent-%COMP%]{font-size:12px}.client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]{padding:12px 16px 16px}.client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-btn[_ngcontent-%COMP%]{width:32px;height:32px}.client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.page-numbers[_ngcontent-%COMP%]   .page-number[_ngcontent-%COMP%]{width:36px;height:36px;font-size:12px}.page-btn[_ngcontent-%COMP%]{width:36px;height:36px}.page-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}.stat-card[_ngcontent-%COMP%], .client-card[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .6s ease forwards}.clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .4s ease forwards}.loading[_ngcontent-%COMP%]{opacity:.6;pointer-events:none}.table-wrapper[_ngcontent-%COMP%]::-webkit-scrollbar{height:8px}.table-wrapper[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#0000000d;border-radius:4px}.table-wrapper[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#c8102e4d;border-radius:4px}.table-wrapper[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#c8102e80}']});let c=i;return c})();function Gt(c,i){c&1&&p(0,"ion-icon",30)}function Kt(c,i){c&1&&p(0,"ion-icon",31)}function Jt(c,i){c&1&&(e(0,"div",40),p(1,"ion-icon",41),e(2,"span"),s(3," note"),n()())}function Xt(c,i){c&1&&(e(0,"span",42),s(1,"-"),n())}function Zt(c,i){if(c&1){let r=y();e(0,"tr")(1,"td")(2,"div",32)(3,"div",33)(4,"h4"),s(5),n()()()(),e(6,"td")(7,"div",34)(8,"p"),s(9),n(),e(10,"p"),s(11),n()()(),e(12,"td"),s(13),n(),e(14,"td"),s(15),n(),e(16,"td"),s(17),n(),e(18,"td"),s(19),n(),e(20,"td"),P(21,Jt,4,0,"div",35)(22,Xt,2,0,"span",36),n(),e(23,"td")(24,"div",37)(25,"button",38),O("click",function(){let o=m(r).$implicit,a=M(2);return u(a.deleteChauffeur(o))}),p(26,"ion-icon",39),n()()()()}if(c&2){let r=i.$implicit;g(5),R("",r.prenom," ",r.nom),g(4),_(r.email),g(2),_(r.telephone),g(2),_(r.cin),g(2),_(r.permis),g(2),_(r.numLicence),g(2),k(" ",r.vehiculeMatricule," "),g(2),C("ngIf",r.noteGlobale),g(),C("ngIf",!r.noteGlobale)}}function tn(c,i){if(c&1){let r=y();e(0,"div",24)(1,"table",25)(2,"thead")(3,"tr")(4,"th",26),O("click",function(){m(r);let o=M();return u(o.sortBy("nom"))}),s(5," Chauffeur "),P(6,Gt,1,0,"ion-icon",27)(7,Kt,1,0,"ion-icon",28),n(),e(8,"th"),s(9,"Contact"),n(),e(10,"th"),s(11,"CIN"),n(),e(12,"th"),s(13,"Permis"),n(),e(14,"th"),s(15,"Licence"),n(),e(16,"th"),s(17,"V\xE9hicule"),n(),e(18,"th"),s(19,"Note"),n(),e(20,"th"),s(21,"Actions"),n()()(),e(22,"tbody"),P(23,Zt,27,10,"tr",29),n()()()}if(c&2){let r=M();g(6),C("ngIf",r.sortField==="nom"&&r.sortDirection==="asc"),g(),C("ngIf",r.sortField==="nom"&&r.sortDirection==="desc"),g(16),C("ngForOf",r.paginatedChauffeurs)}}function nn(c,i){if(c&1){let r=y();e(0,"button",49),O("click",function(){let o=m(r).$implicit,a=M(2);return u(a.goToPage(o))}),s(1),n()}if(c&2){let r=i.$implicit,t=M(2);h("active",r===t.currentPage),g(),k(" ",r," ")}}function en(c,i){if(c&1){let r=y();e(0,"div",43)(1,"button",44),O("click",function(){m(r);let o=M();return u(o.goToPage(o.currentPage-1))}),p(2,"ion-icon",45),n(),e(3,"div",46),P(4,nn,2,3,"button",47),n(),e(5,"button",44),O("click",function(){m(r);let o=M();return u(o.goToPage(o.currentPage+1))}),p(6,"ion-icon",48),n()()}if(c&2){let r=M();g(),C("disabled",r.currentPage===1),g(3),C("ngForOf",r.getPageNumbers()),g(),C("disabled",r.currentPage===r.totalPages)}}var ct=(()=>{let i=class i{constructor(t,o,a,l){this.chauffeurService=t,this.alertController=o,this.toastController=a,this.modalController=l,this.chauffeurs=[],this.filteredChauffeurs=[],this.paginatedChauffeurs=[],this.selectedChauffeurs=[],this.totalChauffeurs=0,this.activeChauffeurs=0,this.pendingValidation=0,this.averageRating=0,this.searchQuery="",this.currentPage=1,this.itemsPerPage=10,this.totalPages=1,this.viewMode="table",this.sortField="",this.sortDirection="asc",this.isLoading=!1}ngOnInit(){this.loadChauffeurs()}loadChauffeurs(){this.isLoading=!0,this.chauffeurService.getChauffeurs().subscribe({next:t=>{this.chauffeurs=t.content,this.filteredChauffeurs=[...this.chauffeurs],this.calculateStatistics(),this.updatePagination(),this.isLoading=!1},error:t=>{console.error("Error loading chauffeurs:",t),this.showToast("Erreur lors du chargement des chauffeurs","danger"),this.filteredChauffeurs=[...this.chauffeurs],this.calculateStatistics(),this.updatePagination(),this.isLoading=!1}})}calculateStatistics(){this.totalChauffeurs=this.chauffeurs.length,this.chauffeurService.getActiveChauffeursCount().subscribe({next:t=>{this.activeChauffeurs=t},error:t=>{console.error("Erreur lors de la r\xE9cup\xE9ration du nombre de chauffeurs actifs :",t)}})}onSearch(){this.applyFilters()}onFilterChange(){this.applyFilters()}applyFilters(){this.filteredChauffeurs=this.chauffeurs.filter(t=>!this.searchQuery||t.nom.toLowerCase().includes(this.searchQuery.toLowerCase())||t.prenom.toLowerCase().includes(this.searchQuery.toLowerCase())||t.email.toLowerCase().includes(this.searchQuery.toLowerCase())||t.telephone.includes(this.searchQuery)),this.currentPage=1,this.updatePagination()}resetFilters(){this.searchQuery="",this.filteredChauffeurs=[...this.chauffeurs],this.currentPage=1,this.updatePagination()}sortBy(t){this.sortField===t?this.sortDirection=this.sortDirection==="asc"?"desc":"asc":(this.sortField=t,this.sortDirection="asc"),this.filteredChauffeurs.sort((o,a)=>{let l=o[t],d=a[t];return t==="dateInscription"&&(l=new Date(l).getTime(),d=new Date(d).getTime()),l<d?this.sortDirection==="asc"?-1:1:l>d?this.sortDirection==="asc"?1:-1:0}),this.updatePagination()}updatePagination(){this.totalPages=Math.ceil(this.filteredChauffeurs.length/this.itemsPerPage);let t=(this.currentPage-1)*this.itemsPerPage,o=t+this.itemsPerPage;this.paginatedChauffeurs=this.filteredChauffeurs.slice(t,o)}goToPage(t){t>=1&&t<=this.totalPages&&(this.currentPage=t,this.updatePagination())}getPageNumbers(){let t=[],a=Math.max(1,this.currentPage-Math.floor(2.5)),l=Math.min(this.totalPages,a****);l-a+1<5&&(a=Math.max(1,l-5+1));for(let d=a;d<=l;d++)t.push(d);return t}deleteChauffeur(t){return w(this,null,function*(){yield(yield this.alertController.create({header:"Confirmer la suppression",message:`\xCAtes-vous s\xFBr de vouloir supprimer le chauffeur ${t.prenom} ${t.nom} ?`,buttons:[{text:"Annuler",role:"cancel"},{text:"Supprimer",role:"destructive",handler:()=>{this.performDeleteChauffeur(t)}}]})).present()})}performDeleteChauffeur(t){return w(this,null,function*(){this.chauffeurService.deleteChauffeur(t.id).subscribe({next:()=>w(this,null,function*(){this.chauffeurs=this.chauffeurs.filter(a=>a.id!==t.id),this.applyFilters(),this.calculateStatistics(),(yield this.toastController.create({message:`Chauffeur ${t.prenom} ${t.nom} supprim\xE9 avec succ\xE8s`,duration:2e3,color:"success"})).present()}),error:()=>w(this,null,function*(){(yield this.toastController.create({message:"Erreur lors de la suppression du chauffeur",duration:2e3,color:"danger"})).present()})})})}showToast(t,o="primary"){return w(this,null,function*(){(yield this.toastController.create({message:t,duration:2e3,color:o})).present()})}};i.\u0275fac=function(o){return new(o||i)(b(L),b(Z),b(tt),b(xt))},i.\u0275cmp=S({type:i,selectors:[["app-chauffeurs"]],decls:44,vars:7,consts:[["fullscreen",""],[1,"chauffeurs-container"],[1,"page-header"],[1,"header-content"],[1,"title-section"],[1,"page-title"],[1,"page-subtitle"],[1,"stats-row"],[1,"stat-card"],[1,"stat-icon","chauffeurs"],["name","car-outline"],[1,"stat-info"],[1,"stat-icon","active"],["name","checkmark-circle-outline"],[1,"stat-icon","rating"],["name","star-outline"],[1,"filters-section"],[1,"search-container"],["name","search-outline"],["type","text","placeholder","Rechercher par nom, email ou t\xE9l\xE9phone...",1,"search-input",3,"ngModelChange","input","ngModel"],[1,"table-container"],[1,"table-header"],["class","table-wrapper",4,"ngIf"],["class","pagination",4,"ngIf"],[1,"table-wrapper"],[1,"clients-table"],[1,"sortable",3,"click"],["name","chevron-up-outline",4,"ngIf"],["name","chevron-down-outline",4,"ngIf"],[4,"ngFor","ngForOf"],["name","chevron-up-outline"],["name","chevron-down-outline"],[1,"chauffeur-info"],[1,"chauffeur-details"],[1,"contact-info"],["class","rating",4,"ngIf"],["class","no-rating",4,"ngIf"],[1,"action-buttons"],["title","Supprimer",1,"action-btn-small","delete",3,"click"],["name","trash-outline"],[1,"rating"],["name","star",1,"star","filled"],[1,"no-rating"],[1,"pagination"],[1,"page-btn",3,"click","disabled"],["name","chevron-back-outline"],[1,"page-numbers"],["class","page-number",3,"active","click",4,"ngFor","ngForOf"],["name","chevron-forward-outline"],[1,"page-number",3,"click"]],template:function(o,a){o&1&&(e(0,"ion-content",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"div",4)(5,"h1",5),s(6,"Gestion des Chauffeurs"),n(),e(7,"p",6),s(8,"G\xE9rez et suivez tous vos chauffeurs"),n()()()(),e(9,"div",7)(10,"div",8)(11,"div",9),p(12,"ion-icon",10),n(),e(13,"div",11)(14,"h3"),s(15),n(),e(16,"p"),s(17,"Total Chauffeurs"),n()()(),e(18,"div",8)(19,"div",12),p(20,"ion-icon",13),n(),e(21,"div",11)(22,"h3"),s(23),n(),e(24,"p"),s(25,"Chauffeurs Actifs"),n()()(),e(26,"div",8)(27,"div",14),p(28,"ion-icon",15),n(),e(29,"div",11)(30,"h3"),s(31),n(),e(32,"p"),s(33,"Note moyenne"),n()()()(),e(34,"div",16)(35,"div",17),p(36,"ion-icon",18),e(37,"input",19),H("ngModelChange",function(d){return q(a.searchQuery,d)||(a.searchQuery=d),d}),O("input",function(){return a.onSearch()}),n()()(),e(38,"div",20)(39,"div",21)(40,"h3"),s(41),n()(),P(42,tn,24,3,"div",22)(43,en,7,3,"div",23),n()()()),o&2&&(g(15),_(a.totalChauffeurs),g(8),_(a.activeChauffeurs),g(8),_(a.averageRating),g(6),B("ngModel",a.searchQuery),g(4),k("Liste des Chauffeurs (",a.filteredChauffeurs.length,")"),g(),C("ngIf",a.viewMode==="table"),g(),C("ngIf",a.totalPages>1))},dependencies:[v,U,z,x,j,D,X,G,K,J],styles:['.clients-container[_ngcontent-%COMP%], .chauffeurs-container[_ngcontent-%COMP%]{padding:0;background:transparent;min-height:100vh}.page-header[_ngcontent-%COMP%]{margin-bottom:32px}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;gap:24px}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .title-section[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%]{font-size:32px;font-weight:800;color:#c8102e;margin:0 0 8px;text-shadow:0 2px 4px rgba(0,0,0,.1)}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .title-section[_ngcontent-%COMP%]   .page-subtitle[_ngcontent-%COMP%]{font-size:16px;color:#fffc;margin:0;font-weight:400}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]{display:flex;gap:12px}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{padding:12px 20px;border:none;border-radius:12px;font-size:14px;font-weight:600;cursor:pointer;transition:all .3s ease;display:flex;align-items:center;gap:8px}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn.primary[_ngcontent-%COMP%]{background:#fff3;color:#fff;border:1px solid rgba(255,255,255,.3);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn.primary[_ngcontent-%COMP%]:hover{background:#ffffff4d;transform:translateY(-2px);box-shadow:0 8px 25px #00000026}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn.secondary[_ngcontent-%COMP%]{background:#ffffff1a;color:#fff;border:1px solid rgba(255,255,255,.2)}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn.secondary[_ngcontent-%COMP%]:hover{background:#fff3;transform:translateY(-1px)}.stats-row[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:20px;margin-bottom:32px}.stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]{background:#fffffff2;-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border-radius:16px;padding:20px;display:flex;align-items:center;gap:16px;box-shadow:0 8px 32px #0000001a;border:1px solid rgba(255,255,255,.2);transition:all .3s ease}.stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 12px 40px #00000026}.stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]{width:48px;height:48px;border-radius:12px;display:flex;align-items:center;justify-content:center;box-shadow:0 4px 12px #0000001a}.stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;color:#fff}.stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-icon.clients[_ngcontent-%COMP%]{background:linear-gradient(135deg,#c8102e,#a00d26)}.stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-icon.active[_ngcontent-%COMP%]{background:linear-gradient(135deg,#43e97b,#38f9d7)}.stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-icon.new[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f093fb,#f5576c)}.stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-icon.rating[_ngcontent-%COMP%]{background:linear-gradient(135deg,#fbbf24,#f59e0b)}.stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:24px;font-weight:700;color:#2d3748;margin:0 0 4px;line-height:1}.stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:14px;color:#718096;margin:0;font-weight:500}.filters-section[_ngcontent-%COMP%]{background:#fffffff2;-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border-radius:16px;padding:24px;margin-bottom:24px;box-shadow:0 8px 32px #0000001a;border:1px solid rgba(255,255,255,.2)}.filters-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]{display:flex;align-items:center;background:#fffc;border-radius:12px;padding:12px 16px;border:1px solid rgba(200,16,46,.2);margin-bottom:20px;transition:all .3s ease}.filters-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]:focus-within{border-color:#c8102e;box-shadow:0 0 0 3px #c8102e1a}.filters-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#a0aec0;margin-right:12px;font-size:20px}.filters-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%]{border:none;outline:none;background:transparent;flex:1;color:#4a5568;font-size:14px}.filters-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%]::placeholder{color:#a0aec0}.filters-section[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]{display:flex;gap:16px;align-items:center;flex-wrap:wrap}.filters-section[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filter-select[_ngcontent-%COMP%]{padding:10px 16px;border:1px solid rgba(200,16,46,.2);border-radius:8px;background:#fff;color:#4a5568;font-size:14px;cursor:pointer;transition:all .3s ease}.filters-section[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filter-select[_ngcontent-%COMP%]:focus{outline:none;border-color:#c8102e;box-shadow:0 0 0 3px #c8102e1a}.filters-section[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filter-btn[_ngcontent-%COMP%]{padding:10px 16px;background:#c8102e1a;border:1px solid rgba(200,16,46,.2);border-radius:8px;color:#c8102e;font-size:14px;font-weight:600;cursor:pointer;transition:all .3s ease;display:flex;align-items:center;gap:8px}.filters-section[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filter-btn[_ngcontent-%COMP%]:hover{background:#c8102e26;transform:translateY(-1px)}.filters-section[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filter-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.table-container[_ngcontent-%COMP%]{background:#fffffff2;-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border-radius:16px;box-shadow:0 8px 32px #0000001a;border:1px solid rgba(255,255,255,.2);overflow:hidden}.table-container[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:24px;border-bottom:1px solid rgba(0,0,0,.05)}.table-container[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:18px;font-weight:700;color:#2d3748;margin:0}.table-container[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .table-actions[_ngcontent-%COMP%]   .table-btn[_ngcontent-%COMP%]{padding:8px;background:#c8102e1a;border:1px solid rgba(200,16,46,.2);border-radius:8px;color:#c8102e;cursor:pointer;transition:all .3s ease}.table-container[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .table-actions[_ngcontent-%COMP%]   .table-btn[_ngcontent-%COMP%]:hover{background:#c8102e26;transform:translateY(-1px)}.table-container[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .table-actions[_ngcontent-%COMP%]   .table-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]{overflow-x:auto}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]{width:100%;border-collapse:collapse}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]{background:#c8102e0d}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{padding:16px;text-align:left;font-weight:600;color:#4a5568;font-size:14px;border-bottom:1px solid rgba(0,0,0,.05);white-space:nowrap}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%]{cursor:pointer;-webkit-user-select:none;user-select:none;transition:all .3s ease}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%]:hover{background:#c8102e1a}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-left:4px;font-size:12px;opacity:.6}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]{transition:all .3s ease}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover{background:#c8102e05}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr.selected[_ngcontent-%COMP%]{background:#c8102e0d}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{padding:16px;border-bottom:1px solid rgba(0,0,0,.05);vertical-align:middle}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .client-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .client-info[_ngcontent-%COMP%]   .client-avatar[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;object-fit:cover;border:2px solid #c8102e}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .client-info[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:#2d3748;margin:0 0 2px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .client-info[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:12px;color:#718096;margin:0}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .contact-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:13px;color:#4a5568;margin:0 0 2px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .contact-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child{margin:0}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .trips-count[_ngcontent-%COMP%]{background:#c8102e1a;color:#c8102e;padding:4px 8px;border-radius:6px;font-size:12px;font-weight:600}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .rating[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .rating[_ngcontent-%COMP%]   .star[_ngcontent-%COMP%]{font-size:14px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .rating[_ngcontent-%COMP%]   .star.filled[_ngcontent-%COMP%]{color:#fbbf24}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .rating[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:13px;font-weight:600;color:#4a5568}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .no-rating[_ngcontent-%COMP%]{color:#a0aec0;font-style:italic}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%]{padding:4px 12px;border-radius:20px;font-size:12px;font-weight:600;text-transform:capitalize}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .status-badge.actif[_ngcontent-%COMP%]{background:#38b2ac1a;color:#38b2ac}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .status-badge.inactif[_ngcontent-%COMP%]{background:#a0aec01a;color:#a0aec0}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .status-badge.suspendu[_ngcontent-%COMP%]{background:#f565651a;color:#f56565}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]{display:flex;gap:8px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn-small[_ngcontent-%COMP%]{width:32px;height:32px;border:none;border-radius:6px;cursor:pointer;display:flex;align-items:center;justify-content:center;transition:all .3s ease}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn-small[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn-small.view[_ngcontent-%COMP%]{background:#c8102e1a;color:#c8102e}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn-small.view[_ngcontent-%COMP%]:hover{background:#c8102e33;transform:translateY(-1px)}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn-small.edit[_ngcontent-%COMP%]{background:#38b2ac1a;color:#38b2ac}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn-small.edit[_ngcontent-%COMP%]:hover{background:#38b2ac33;transform:translateY(-1px)}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn-small.delete[_ngcontent-%COMP%]{background:#f565651a;color:#f56565}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn-small.delete[_ngcontent-%COMP%]:hover{background:#f5656533;transform:translateY(-1px)}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(300px,1fr));gap:20px;padding:24px}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]{background:#fff;border-radius:16px;overflow:hidden;box-shadow:0 4px 16px #00000014;border:1px solid rgba(0,0,0,.05);transition:all .3s ease}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 8px 32px #0000001f}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]{position:relative;padding:20px;background:linear-gradient(135deg,#c8102e,#a00d26);text-align:center}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .client-photo[_ngcontent-%COMP%]{width:80px;height:80px;border-radius:50%;object-fit:cover;border:4px solid white;box-shadow:0 4px 16px #0000001a}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .client-status[_ngcontent-%COMP%]{position:absolute;top:16px;right:16px}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .client-status[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%]{padding:4px 12px;border-radius:20px;font-size:12px;font-weight:600;text-transform:capitalize;background:#fff3;color:#fff;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]{padding:20px}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:18px;font-weight:700;color:#2d3748;margin:0 0 12px;text-align:center}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-email[_ngcontent-%COMP%], .table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-phone[_ngcontent-%COMP%], .table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-city[_ngcontent-%COMP%]{font-size:14px;color:#718096;margin:0 0 8px;display:flex;align-items:center;justify-content:center;gap:8px}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-email[_ngcontent-%COMP%]:before, .table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-phone[_ngcontent-%COMP%]:before, .table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-city[_ngcontent-%COMP%]:before{content:"";width:4px;height:4px;background:#cbd5e0;border-radius:50%}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-stats[_ngcontent-%COMP%]{display:flex;justify-content:space-around;margin-top:16px;padding-top:16px;border-top:1px solid rgba(0,0,0,.05)}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-stats[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]{text-align:center}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-stats[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%]{display:block;font-size:20px;font-weight:700;color:#c8102e;margin-bottom:4px}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-stats[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%]{font-size:12px;color:#a0aec0;font-weight:500}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]{display:flex;justify-content:center;gap:12px;padding:16px 20px 20px}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-btn[_ngcontent-%COMP%]{width:36px;height:36px;border:none;border-radius:8px;cursor:pointer;display:flex;align-items:center;justify-content:center;transition:all .3s ease}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-btn.view[_ngcontent-%COMP%]{background:#c8102e1a;color:#c8102e}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-btn.view[_ngcontent-%COMP%]:hover{background:#c8102e33;transform:translateY(-2px)}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-btn.edit[_ngcontent-%COMP%]{background:#38b2ac1a;color:#38b2ac}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-btn.edit[_ngcontent-%COMP%]:hover{background:#38b2ac33;transform:translateY(-2px)}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-btn.delete[_ngcontent-%COMP%]{background:#f565651a;color:#f56565}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-btn.delete[_ngcontent-%COMP%]:hover{background:#f5656533;transform:translateY(-2px)}.table-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;gap:8px;padding:24px;border-top:1px solid rgba(0,0,0,.05)}.table-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-btn[_ngcontent-%COMP%]{width:40px;height:40px;border:1px solid rgba(200,16,46,.2);background:#fff;border-radius:8px;cursor:pointer;display:flex;align-items:center;justify-content:center;transition:all .3s ease;color:#c8102e}.table-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-btn[_ngcontent-%COMP%]:hover:not(:disabled){background:#c8102e1a;transform:translateY(-1px)}.table-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-btn[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed}.table-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.table-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-numbers[_ngcontent-%COMP%]{display:flex;gap:4px}.table-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-numbers[_ngcontent-%COMP%]   .page-number[_ngcontent-%COMP%]{width:40px;height:40px;border:1px solid rgba(200,16,46,.2);background:#fff;border-radius:8px;cursor:pointer;display:flex;align-items:center;justify-content:center;transition:all .3s ease;color:#c8102e;font-weight:600;font-size:14px}.table-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-numbers[_ngcontent-%COMP%]   .page-number[_ngcontent-%COMP%]:hover{background:#c8102e1a;transform:translateY(-1px)}.table-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-numbers[_ngcontent-%COMP%]   .page-number.active[_ngcontent-%COMP%]{background:#c8102e;color:#fff;border-color:#c8102e}@media (max-width: 1200px){.stats-row[_ngcontent-%COMP%]{grid-template-columns:repeat(2,1fr)}.grid-view[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fill,minmax(280px,1fr))}}@media (max-width: 768px){.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch;gap:16px}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .title-section[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%]{font-size:24px}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .title-section[_ngcontent-%COMP%]   .page-subtitle[_ngcontent-%COMP%]{font-size:14px}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]{justify-content:center}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{flex:1;justify-content:center}.stats-row[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:16px}.filters-section[_ngcontent-%COMP%]{padding:16px}.filters-section[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch}.filters-section[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filter-select[_ngcontent-%COMP%], .filters-section[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filter-btn[_ngcontent-%COMP%]{width:100%}.table-container[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]{padding:16px;flex-direction:column;gap:12px;align-items:stretch}.table-container[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .table-actions[_ngcontent-%COMP%]{display:flex;justify-content:center}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]{font-size:12px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{padding:8px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .client-info[_ngcontent-%COMP%]   .client-avatar[_ngcontent-%COMP%]{width:32px;height:32px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .client-info[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:12px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .client-info[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:10px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn-small[_ngcontent-%COMP%]{width:28px;height:28px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn-small[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:14px}.grid-view[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:16px;padding:16px}.pagination[_ngcontent-%COMP%]{padding:16px;flex-wrap:wrap}.pagination[_ngcontent-%COMP%]   .page-numbers[_ngcontent-%COMP%]{order:-1;width:100%;justify-content:center;margin-bottom:12px}}@media (max-width: 480px){.clients-container[_ngcontent-%COMP%], .chauffeurs-container[_ngcontent-%COMP%]{padding:0}.filters-section[_ngcontent-%COMP%]{margin:0 0 16px;border-radius:12px}.table-container[_ngcontent-%COMP%]{border-radius:12px}.client-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]{padding:16px}.client-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .client-photo[_ngcontent-%COMP%]{width:60px;height:60px}.client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]{padding:16px}.client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:16px}.client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-email[_ngcontent-%COMP%], .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-phone[_ngcontent-%COMP%]{font-size:12px}.client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]{padding:12px 16px 16px}.client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-btn[_ngcontent-%COMP%]{width:32px;height:32px}.client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.page-numbers[_ngcontent-%COMP%]   .page-number[_ngcontent-%COMP%]{width:36px;height:36px;font-size:12px}.page-btn[_ngcontent-%COMP%]{width:36px;height:36px}.page-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}.stat-card[_ngcontent-%COMP%], .client-card[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .6s ease forwards}.clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .4s ease forwards}.loading[_ngcontent-%COMP%]{opacity:.6;pointer-events:none}.table-wrapper[_ngcontent-%COMP%]::-webkit-scrollbar{height:8px}.table-wrapper[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#0000000d;border-radius:4px}.table-wrapper[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#c8102e4d;border-radius:4px}.table-wrapper[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#c8102e80}.stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-icon.chauffeurs[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2)}.stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-icon.pending[_ngcontent-%COMP%]{background:linear-gradient(135deg,#fbbf24,#f59e0b)}.vehicle-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:#2d3748;margin:0 0 4px}.vehicle-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:12px;color:#718096;margin:0 0 2px}.vehicle-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-of-type{margin-bottom:8px}.vehicle-info[_ngcontent-%COMP%]   .capacity-info[_ngcontent-%COMP%]{font-size:11px;color:var(--ion-color-primary);background:var(--ion-color-primary-tint);padding:2px 6px;border-radius:8px;font-weight:500;display:inline-block}.vehicle-info[_ngcontent-%COMP%]   .no-vehicle[_ngcontent-%COMP%]{font-size:11px;color:var(--ion-color-warning);font-style:italic;display:block;margin-top:4px}.vehicle-info[_ngcontent-%COMP%]   .places-badge[_ngcontent-%COMP%]{background:#667eea1a;color:#667eea;padding:2px 8px;border-radius:6px;font-size:11px;font-weight:600}.document-badge[_ngcontent-%COMP%]{padding:4px 12px;border-radius:20px;font-size:12px;font-weight:600;text-transform:capitalize}.document-badge.verified[_ngcontent-%COMP%]{background:#38b2ac1a;color:#38b2ac}.document-badge.pending[_ngcontent-%COMP%]{background:#f565651a;color:#f56565}.chauffeur-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.chauffeur-info[_ngcontent-%COMP%]   .chauffeur-avatar[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;object-fit:cover;border:2px solid #667eea}.chauffeur-info[_ngcontent-%COMP%]   .chauffeur-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:#2d3748;margin:0 0 2px}.chauffeur-info[_ngcontent-%COMP%]   .chauffeur-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:11px;color:#718096;margin:0 0 1px}.chauffeur-info[_ngcontent-%COMP%]   .chauffeur-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child{margin:0}.action-btn-small.validate[_ngcontent-%COMP%]{background:#38b2ac1a;color:#38b2ac}.action-btn-small.validate[_ngcontent-%COMP%]:hover{background:#38b2ac33;transform:translateY(-1px)}.card-btn.validate[_ngcontent-%COMP%]{background:#38b2ac1a;color:#38b2ac}.card-btn.validate[_ngcontent-%COMP%]:hover{background:#38b2ac33;transform:translateY(-2px)}.chauffeur-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .chauffeur-photo[_ngcontent-%COMP%]{width:80px;height:80px;border-radius:50%;object-fit:cover;border:4px solid white;box-shadow:0 4px 16px #0000001a}.chauffeur-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .chauffeur-status[_ngcontent-%COMP%]{position:absolute;top:16px;right:16px;display:flex;flex-direction:column;gap:8px}.chauffeur-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .chauffeur-status[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%], .chauffeur-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .chauffeur-status[_ngcontent-%COMP%]   .document-badge[_ngcontent-%COMP%]{padding:4px 12px;border-radius:20px;font-size:12px;font-weight:600;text-transform:capitalize;background:#fff3;color:#fff;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);text-align:center}.chauffeur-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .vehicle-summary[_ngcontent-%COMP%]{background:#667eea0d;border-radius:8px;padding:12px;margin:16px 0}.chauffeur-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .vehicle-summary[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:#667eea;margin:0 0 4px}.chauffeur-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .vehicle-summary[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:12px;color:#718096;margin:0 0 8px}.chauffeur-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .vehicle-summary[_ngcontent-%COMP%]   .places-info[_ngcontent-%COMP%]{background:#667eea1a;color:#667eea;padding:4px 8px;border-radius:6px;font-size:11px;font-weight:600}.chauffeur-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .chauffeur-email[_ngcontent-%COMP%], .chauffeur-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .chauffeur-phone[_ngcontent-%COMP%], .chauffeur-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .chauffeur-city[_ngcontent-%COMP%]{font-size:14px;color:#718096;margin:0 0 8px;display:flex;align-items:center;justify-content:center;gap:8px}.chauffeur-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .chauffeur-email[_ngcontent-%COMP%]:before, .chauffeur-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .chauffeur-phone[_ngcontent-%COMP%]:before, .chauffeur-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .chauffeur-city[_ngcontent-%COMP%]:before{content:"";width:4px;height:4px;background:#cbd5e0;border-radius:50%}.chauffeur-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .chauffeur-stats[_ngcontent-%COMP%]{display:flex;justify-content:space-around;margin-top:16px;padding-top:16px;border-top:1px solid rgba(0,0,0,.05)}.chauffeur-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .chauffeur-stats[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]{text-align:center}.chauffeur-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .chauffeur-stats[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%]{display:block;font-size:20px;font-weight:700;color:#667eea;margin-bottom:4px}.chauffeur-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .chauffeur-stats[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%]{font-size:12px;color:#a0aec0;font-weight:500}.status-badge.en_attente_validation[_ngcontent-%COMP%]{background:#fbbf241a;color:#f59e0b}.chauffeurs-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(4){min-width:180px}.chauffeurs-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(9){min-width:120px}.chauffeurs-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(11){min-width:140px}']});let c=i;return c})();var on=[{path:"",component:ot,children:[{path:"",redirectTo:"dashboard",pathMatch:"full"},{path:"dashboard",component:it},{path:"clients",component:at},{path:"chauffeurs",component:ct},{path:"stations",loadComponent:()=>import("./chunk-J7O5ELN5.js").then(c=>c.StationsComponent)}]}],yt=(()=>{let i=class i{};i.\u0275fac=function(o){return new(o||i)},i.\u0275mod=Q({type:i}),i.\u0275inj=A({imports:[dt.forChild(on),dt]});let c=i;return c})();var Hn=(()=>{let i=class i{};i.\u0275fac=function(o){return new(o||i)},i.\u0275mod=Q({type:i}),i.\u0275inj=A({imports:[ot,et,it,at,ct,x,v,yt]});let c=i;return c})();export{Hn as AdminModule};
