<ion-content fullscreen>
<div class="page-header">
    <div class="header-content">
      <div class="title-section">
        <h1 class="page-title">Gestion des Stations</h1>
        <p class="page-subtitle"><PERSON><PERSON><PERSON> et suivez tous vos Stations</p>
      </div>
      
    </div>
  </div>
   <div class="filters-section">
    <div class="search-container">
      <ion-icon name="search-outline"></ion-icon>
      <input
        type="text"
        placeholder="Rechercher par nom, ville ou Adresse."
        class="search-input"
        [(ngModel)]="searchQuery"
        (input)="onSearch()"
      >
    </div>

    
  </div>
  <div class="table-container">
    <div class="table-header">
      <h3>Liste des Stations ({{ filteredStations.length }})</h3>
    </div>
    <div class="table-wrapper">
      <table class="clients-table">
        <thead>
          <tr>
            <th>Nom</th>
            <th>Ville</th>
            <th>Adresse</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let station of filteredStations">
            <td>{{ station.nom }}</td>
            <td>{{ station.ville }}</td>
            <td>{{ station.adresse }}</td>
            <td>
              <div class="action-buttons">
                <button class="action-btn-small delete" (click)="deleteStation(station)" title="Supprimer">
                  <ion-icon name="trash-outline"></ion-icon>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</ion-content>
