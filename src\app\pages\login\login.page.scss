/* Root content styling for full-screen fit */
ion-content.login-page {
  --background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  width: 100vw;
  padding: 0;
  margin: 0;
  overflow-x: hidden;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* Main container that fits screen and adapts */
#contenu-back {
  display: flex;
  flex-direction: row;
  width: 100%;
  max-width: 1200px;
  min-height: 100vh;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: clamp(0px, 2vw, 20px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin: clamp(0px, 2vh, 20px);
}

/* Left side: Form section */
.contenu-write {
  flex: 1;
  min-width: 0;
  padding: clamp(20px, 5vh, 40px) clamp(20px, 5vw, 40px);
  display: flex;
  justify-content: center;
  align-items: center;
  background: transparent;
  min-height: 100vh;
  position: relative;
}

.write-element {
  width: 100%;
  max-width: 400px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: clamp(16px, 4vw, 20px);
  padding: clamp(24px, 6vw, 32px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: slideInUp 0.6s ease forwards;
}

.write-element h1 {
  color: #c8102e;
  font-size: clamp(1.5rem, 5vw, 2rem);
  font-weight: 800;
  margin-bottom: clamp(8px, 2vw, 16px);
  text-align: center;
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.soustitre {
  color: #666;
  font-size: clamp(0.875rem, 3vw, 1rem);
  text-align: center;
  margin-bottom: clamp(24px, 6vw, 32px);
  line-height: 1.4;
  font-weight: 500;
}

/* Input styles */
ion-input.email,
ion-input.motDePasse {
  --background: rgba(255, 255, 255, 0.9);
  --color: #333;
  --placeholder-color: #999;
  --placeholder-opacity: 0.7;
  --border-radius: clamp(8px, 2vw, 12px);
  --padding-start: clamp(12px, 3vw, 16px);
  --padding-end: clamp(12px, 3vw, 16px);
  --padding-top: clamp(12px, 3vw, 16px);
  --padding-bottom: clamp(12px, 3vw, 16px);
  --border-width: 1px;
  --border-color: rgba(200, 16, 46, 0.2);
  --border-style: solid;
  margin-bottom: clamp(16px, 4vw, 24px);
  width: 100%;
  font-size: clamp(14px, 3.5vw, 16px);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  min-height: 48px; /* Minimum touch target */

  &:focus-within {
    --border-color: #c8102e;
    --border-width: 2px;
    box-shadow: 0 4px 12px rgba(200, 16, 46, 0.15);
    transform: translateY(-2px);
  }

  &:hover {
    --border-color: rgba(200, 16, 46, 0.4);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

/* Checkbox and links */
.input-links {
  display: flex;
  justify-content: space-between;
  color: #333;
  align-items: center;
  margin-bottom: 1.5rem;
  font-size: 0.875rem;
  flex-wrap: wrap;
}

.input-links div {
  display: flex;
  align-items: center;
  gap: 8px;
}

.input-links a {
  color: #c8102e;
  font-weight: 500;
  text-decoration: none;
}
.input-links a:hover {
  text-decoration: underline;
}

/* HR Divider */
hr {
  border: none;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(200, 16, 46, 0.3), transparent);
  margin: clamp(20px, 5vw, 24px) 0;
}

/* Error messages */
ion-text[color="danger"] {
  display: block;
  font-size: clamp(12px, 3vw, 14px);
  margin-bottom: clamp(16px, 4vw, 20px);
  text-align: center;
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  padding: clamp(8px, 2vw, 12px);
  border-radius: clamp(6px, 1.5vw, 8px);
  border: 1px solid rgba(239, 68, 68, 0.2);
  font-weight: 500;
}

/* Buttons */
ion-button {
  --background: #c8102e;
  --background-activated: #a50d25;
  --background-hover: #b00e28;
  --color: #ffffff;
  --border-radius: clamp(8px, 2vw, 12px);
  --padding-top: clamp(12px, 3vw, 16px);
  --padding-bottom: clamp(12px, 3vw, 16px);
  margin-bottom: clamp(12px, 3vw, 16px);
  font-weight: 600;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  width: 100%;
  font-size: clamp(14px, 3.5vw, 16px);
  min-height: 48px; /* Minimum touch target */
  box-shadow: 0 4px 12px rgba(200, 16, 46, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(200, 16, 46, 0.4);
  }

  &:active {
    transform: translateY(0) scale(0.98);
  }
}

ion-button[fill="clear"] {
  --color: #333;
  --background: rgba(255, 255, 255, 0.8);
  --background-activated: rgba(200, 16, 46, 0.1);
  --background-hover: rgba(200, 16, 46, 0.05);
  --border-color: rgba(200, 16, 46, 0.3);
  --border-width: 1px;
  --border-style: solid;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  &:hover {
    --color: #c8102e;
    --border-color: #c8102e;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: translateY(0) scale(0.98);
  }
}

/* Register section */
.goregister {
  text-align: center;
  font-size: 0.9rem;
  color: #333;
}
.goregister a {
  color: #c8102e;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
}
.goregister a:hover {
  text-decoration: underline;
}

/* Logo section (right side) */
.contenu-logo {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 5vh 5vw;
  background-color: #ffffff;
  height: 100%;
  text-align: center;
}

.logo0001 img {
  max-width: 150px;
  height: auto;
  margin-bottom: 1.5rem;
}

.title0001 h1 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #c8102e;
  margin-bottom: 1rem;
}

.reseaux {
  display: flex;
  gap: 1rem;
  justify-content: center;
}
.reseaux a img {
  width: 30px;
  height: 30px;
  filter: brightness(2) invert(1);
  transition: transform 0.3s ease;
}
.reseaux a img:hover {
  transform: scale(1.1);
}

/* Responsive design for small devices */
@media (max-width: 768px) {
  #contenu-back {
    flex-direction: column;
    height: 100vh;
  }

  .contenu-write,
  .contenu-logo {
    width: 100%;
    height: auto;
    padding: 4vh 6vw;
  }

  .contenu-logo {
    order: -1;
  }

  .write-element h1 {
    font-size: 1.6rem;
  }
}
