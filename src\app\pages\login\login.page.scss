/* Root content styling for full-screen fit */
ion-content.login-page {
  --background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  width: 100vw;
  padding: 0;
  margin: 0;
  overflow-x: hidden;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* Main container that fits screen and adapts */
#contenu-back {
  display: flex;
  flex-direction: row;
  width: 100%;
  max-width: 1200px;
  min-height: 100vh;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: clamp(0px, 2vw, 20px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin: clamp(0px, 2vh, 20px);
}

/* Left side: Form section */
.contenu-write {
  flex: 1;
  min-width: 0;
  padding: clamp(20px, 5vh, 40px) clamp(20px, 5vw, 40px);
  display: flex;
  justify-content: center;
  align-items: center;
  background: transparent;
  min-height: 100vh;
  position: relative;
}

.write-element {
  width: 100%;
  max-width: 400px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: clamp(16px, 4vw, 20px);
  padding: clamp(24px, 6vw, 32px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: slideInUp 0.6s ease forwards;
}

.write-element h1 {
  color: #c8102e;
  font-size: clamp(1.5rem, 5vw, 2rem);
  font-weight: 800;
  margin-bottom: clamp(8px, 2vw, 16px);
  text-align: center;
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.soustitre {
  color: #666;
  font-size: clamp(0.875rem, 3vw, 1rem);
  text-align: center;
  margin-bottom: clamp(24px, 6vw, 32px);
  line-height: 1.4;
  font-weight: 500;
}

/* Input styles */
ion-input.email,
ion-input.motDePasse {
  --background: rgba(255, 255, 255, 0.9);
  --color: #333;
  --placeholder-color: #999;
  --placeholder-opacity: 0.7;
  --border-radius: clamp(8px, 2vw, 12px);
  --padding-start: clamp(12px, 3vw, 16px);
  --padding-end: clamp(12px, 3vw, 16px);
  --padding-top: clamp(12px, 3vw, 16px);
  --padding-bottom: clamp(12px, 3vw, 16px);
  --border-width: 1px;
  --border-color: rgba(200, 16, 46, 0.2);
  --border-style: solid;
  margin-bottom: clamp(16px, 4vw, 24px);
  width: 100%;
  font-size: clamp(14px, 3.5vw, 16px);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  min-height: 48px; /* Minimum touch target */

  &:focus-within {
    --border-color: #c8102e;
    --border-width: 2px;
    box-shadow: 0 4px 12px rgba(200, 16, 46, 0.15);
    transform: translateY(-2px);
  }

  &:hover {
    --border-color: rgba(200, 16, 46, 0.4);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

/* Checkbox and links */
.input-links {
  display: flex;
  justify-content: space-between;
  color: #333;
  align-items: center;
  margin-bottom: 1.5rem;
  font-size: 0.875rem;
  flex-wrap: wrap;
}

.input-links div {
  display: flex;
  align-items: center;
  gap: 8px;
}

.input-links a {
  color: #c8102e;
  font-weight: 500;
  text-decoration: none;
}
.input-links a:hover {
  text-decoration: underline;
}

/* HR Divider */
hr {
  border: none;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(200, 16, 46, 0.3), transparent);
  margin: clamp(20px, 5vw, 24px) 0;
}

/* Error messages */
ion-text[color="danger"] {
  display: block;
  font-size: clamp(12px, 3vw, 14px);
  margin-bottom: clamp(16px, 4vw, 20px);
  text-align: center;
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  padding: clamp(8px, 2vw, 12px);
  border-radius: clamp(6px, 1.5vw, 8px);
  border: 1px solid rgba(239, 68, 68, 0.2);
  font-weight: 500;
}

/* Buttons */
ion-button {
  --background: #c8102e;
  --background-activated: #a50d25;
  --background-hover: #b00e28;
  --color: #ffffff;
  --border-radius: clamp(8px, 2vw, 12px);
  --padding-top: clamp(12px, 3vw, 16px);
  --padding-bottom: clamp(12px, 3vw, 16px);
  margin-bottom: clamp(12px, 3vw, 16px);
  font-weight: 600;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  width: 100%;
  font-size: clamp(14px, 3.5vw, 16px);
  min-height: 48px; /* Minimum touch target */
  box-shadow: 0 4px 12px rgba(200, 16, 46, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(200, 16, 46, 0.4);
  }

  &:active {
    transform: translateY(0) scale(0.98);
  }
}

ion-button[fill="clear"] {
  --color: #333;
  --background: rgba(255, 255, 255, 0.8);
  --background-activated: rgba(200, 16, 46, 0.1);
  --background-hover: rgba(200, 16, 46, 0.05);
  --border-color: rgba(200, 16, 46, 0.3);
  --border-width: 1px;
  --border-style: solid;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  &:hover {
    --color: #c8102e;
    --border-color: #c8102e;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: translateY(0) scale(0.98);
  }
}

/* Register section */
.goregister {
  text-align: center;
  font-size: clamp(13px, 3vw, 14px);
  color: #666;
  margin-top: clamp(16px, 4vw, 20px);
  line-height: 1.5;
  cursor: pointer;
  padding: clamp(8px, 2vw, 12px);
  border-radius: clamp(6px, 1.5vw, 8px);
  transition: all 0.3s ease;

  &:hover {
    background: rgba(200, 16, 46, 0.05);
  }

  a {
    color: #c8102e;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;

    &:hover {
      text-decoration: underline;
      color: #a50d25;
    }
  }
}

/* Logo section (right side) */
.contenu-logo {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: clamp(20px, 5vh, 40px) clamp(20px, 5vw, 40px);
  background: linear-gradient(135deg, rgba(200, 16, 46, 0.05), rgba(200, 16, 46, 0.1));
  min-height: 100vh;
  text-align: center;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23c8102e" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="%23c8102e" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
    pointer-events: none;
  }
}

.logo0001 {
  position: relative;
  z-index: 1;
  margin-bottom: clamp(20px, 5vw, 24px);

  img {
    max-width: clamp(120px, 30vw, 180px);
    height: auto;
    border-radius: clamp(12px, 3vw, 16px);
    box-shadow: 0 10px 30px rgba(200, 16, 46, 0.2);
    transition: all 0.3s ease;
    animation: float 3s ease-in-out infinite;

    &:hover {
      transform: scale(1.05);
      box-shadow: 0 15px 40px rgba(200, 16, 46, 0.3);
    }
  }
}

.title0001 {
  position: relative;
  z-index: 1;
  margin-bottom: clamp(20px, 5vw, 24px);

  h1 {
    font-size: clamp(1.25rem, 4vw, 1.75rem);
    font-weight: 800;
    color: #c8102e;
    margin: 0;
    line-height: 1.2;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    animation: slideInRight 0.8s ease forwards;
  }
}

.reseaux {
  display: flex;
  gap: clamp(12px, 3vw, 16px);
  justify-content: center;
  position: relative;
  z-index: 1;

  a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: clamp(40px, 10vw, 48px);
    height: clamp(40px, 10vw, 48px);
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    touch-action: manipulation;

    &:hover {
      transform: translateY(-4px) scale(1.1);
      box-shadow: 0 8px 20px rgba(200, 16, 46, 0.2);
      background: rgba(200, 16, 46, 0.1);
    }

    &:active {
      transform: translateY(-2px) scale(1.05);
    }

    img {
      width: clamp(20px, 5vw, 24px);
      height: clamp(20px, 5vw, 24px);
      filter: brightness(0) saturate(100%) invert(20%) sepia(100%) saturate(2000%) hue-rotate(345deg);
      transition: all 0.3s ease;
    }

    &:hover img {
      filter: brightness(0) saturate(100%) invert(100%);
    }
  }
}

/* Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Responsive design - Mobile First Approach */

/* Large tablets and small desktops */
@media (max-width: 1024px) {
  #contenu-back {
    margin: clamp(0px, 1vh, 10px);
    border-radius: clamp(0px, 1vw, 16px);
  }

  .contenu-write,
  .contenu-logo {
    padding: clamp(20px, 4vh, 32px) clamp(20px, 4vw, 32px);
  }
}

/* Tablets */
@media (max-width: 768px) {
  ion-content.login-page {
    align-items: stretch;
    padding: clamp(10px, 2vh, 20px);
  }

  #contenu-back {
    flex-direction: column;
    min-height: auto;
    margin: 0;
    border-radius: clamp(12px, 3vw, 16px);
  }

  .contenu-logo {
    order: -1;
    min-height: 40vh;
    padding: clamp(20px, 4vh, 32px) clamp(20px, 4vw, 32px);
  }

  .contenu-write {
    min-height: 60vh;
    padding: clamp(20px, 4vh, 32px) clamp(20px, 4vw, 32px);
  }

  .write-element {
    max-width: 100%;
    padding: clamp(20px, 5vw, 28px);
  }

  .logo0001 img {
    max-width: clamp(100px, 25vw, 140px);
  }

  .title0001 h1 {
    font-size: clamp(1.125rem, 3.5vw, 1.5rem);
  }
}

/* Mobile devices */
@media (max-width: 480px) {
  ion-content.login-page {
    padding: clamp(5px, 1vh, 10px);
  }

  #contenu-back {
    border-radius: clamp(8px, 2vw, 12px);
  }

  .contenu-logo {
    min-height: 35vh;
    padding: clamp(16px, 3vh, 24px) clamp(16px, 3vw, 24px);
  }

  .contenu-write {
    min-height: 65vh;
    padding: clamp(16px, 3vh, 24px) clamp(16px, 3vw, 24px);
  }

  .write-element {
    padding: clamp(16px, 4vw, 24px);

    h1 {
      font-size: clamp(1.25rem, 4vw, 1.5rem);
      margin-bottom: clamp(6px, 1.5vw, 12px);
    }
  }

  .soustitre {
    font-size: clamp(12px, 3vw, 14px);
    margin-bottom: clamp(20px, 5vw, 28px);
  }

  ion-input.email,
  ion-input.motDePasse {
    margin-bottom: clamp(12px, 3vw, 20px);
    font-size: clamp(14px, 3.5vw, 16px);
  }

  ion-button {
    font-size: clamp(13px, 3vw, 15px);
    margin-bottom: clamp(10px, 2.5vw, 14px);
  }

  .logo0001 img {
    max-width: clamp(80px, 20vw, 120px);
  }

  .title0001 h1 {
    font-size: clamp(1rem, 3vw, 1.25rem);
  }

  .reseaux {
    gap: clamp(8px, 2vw, 12px);

    a {
      width: clamp(36px, 9vw, 44px);
      height: clamp(36px, 9vw, 44px);

      img {
        width: clamp(18px, 4.5vw, 22px);
        height: clamp(18px, 4.5vw, 22px);
      }
    }
  }
}

/* Extra small devices */
@media (max-width: 360px) {
  .contenu-logo {
    min-height: 30vh;
  }

  .contenu-write {
    min-height: 70vh;
  }

  .write-element {
    padding: clamp(12px, 3vw, 20px);

    h1 {
      font-size: clamp(1.125rem, 3.5vw, 1.375rem);
    }
  }

  .logo0001 img {
    max-width: clamp(70px, 18vw, 100px);
  }
}

/* Landscape orientation on mobile */
@media (max-height: 500px) and (orientation: landscape) {
  #contenu-back {
    flex-direction: row;
  }

  .contenu-logo {
    order: 0;
    min-height: 100vh;
    flex: 0.4;
  }

  .contenu-write {
    min-height: 100vh;
    flex: 0.6;
  }

  .logo0001 img {
    max-width: clamp(60px, 15vw, 100px);
  }

  .title0001 h1 {
    font-size: clamp(0.875rem, 2.5vw, 1.125rem);
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  ion-input.email,
  ion-input.motDePasse {
    &:hover {
      transform: none;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }
  }

  ion-button {
    &:hover {
      transform: none;
      box-shadow: 0 4px 12px rgba(200, 16, 46, 0.3);
    }

    &:active {
      transform: scale(0.98);
    }
  }

  ion-button[fill="clear"] {
    &:hover {
      transform: none;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    &:active {
      transform: scale(0.98);
    }
  }

  .reseaux a {
    &:hover {
      transform: none;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    &:active {
      transform: scale(0.95);
    }
  }

  .logo0001 img {
    &:hover {
      transform: none;
    }
  }
}
