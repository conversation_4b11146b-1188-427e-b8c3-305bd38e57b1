/* Root content styling for full-screen fit */
ion-content.login-page {
  --background: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100vw;
  padding: 0;
  margin: 0;
  overflow: hidden;
}

/* Main container that fits screen and adapts */
#contenu-back {
  display: flex;
  flex-direction: row;
  width: 100%;
  max-width: 100vw;
  height: 100%;
  max-height: 100vh;
  background: #ffffff;
  border-radius: 0;
  overflow: auto;
}

/* Left side: Form section */
.contenu-write {
  flex: 1;
  min-width: 0;
  padding: 5vh 5vw;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ffffff;
  height: 100%;
}

.write-element {
  width: 100%;
  max-width: 400px;
}

.write-element h1 {
  color: #c8102e;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-align: center;
}

.soustitre {
  color: #666;
  font-size: 1rem;
  text-align: center;
  margin-bottom: 2rem;
}

/* Input styles */
ion-input.email,
ion-input.password {
  --background: #ffffff;
  --color: #333;
  --placeholder-color: #999;
  --placeholder-opacity: 0.8;
  --border-radius: 8px;
  --padding-start: 16px;
  --padding-end: 16px;
  margin-bottom: 1.5rem;
  width: 100%;
}

/* Checkbox and links */
.input-links {
  display: flex;
  justify-content: space-between;
  color: #333;
  align-items: center;
  margin-bottom: 1.5rem;
  font-size: 0.875rem;
  flex-wrap: wrap;
}

.input-links div {
  display: flex;
  align-items: center;
  gap: 8px;
}

.input-links a {
  color: #c8102e;
  font-weight: 500;
  text-decoration: none;
}
.input-links a:hover {
  text-decoration: underline;
}

/* Error messages */
ion-text[color="danger"] {
  display: block;
  font-size: 0.875rem;
  margin-bottom: 1.5rem;
  text-align: center;
}

/* Buttons */
ion-button {
  --background: #c8102e;
  --background-activated: #a50d25;
  --color: #ffffff;
  --border-radius: 8px;
  margin-bottom: 1rem;
  font-weight: 600;
  letter-spacing: 1px;
  text-transform: uppercase;
  width: 100%;
}

ion-button[fill="clear"] {
  --color: #333;
  --border-color: #c8102e;
  --border-width: 1px;
  --border-style: solid;
  --background-activated: #f5f5f5;
}

/* Register section */
.goregister {
  text-align: center;
  font-size: 0.9rem;
  color: #333;
}
.goregister a {
  color: #c8102e;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
}
.goregister a:hover {
  text-decoration: underline;
}

/* Logo section (right side) */
.contenu-logo {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 5vh 5vw;
  background-color: #ffffff;
  height: 100%;
  text-align: center;
}

.logo0001 img {
  max-width: 150px;
  height: auto;
  margin-bottom: 1.5rem;
}

.title0001 h1 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #c8102e;
  margin-bottom: 1rem;
}

.reseaux {
  display: flex;
  gap: 1rem;
  justify-content: center;
}
.reseaux a img {
  width: 30px;
  height: 30px;
  filter: brightness(2) invert(1);
  transition: transform 0.3s ease;
}
.reseaux a img:hover {
  transform: scale(1.1);
}

/* Responsive design for small devices */
@media (max-width: 768px) {
  #contenu-back {
    flex-direction: column;
    height: 100vh;
  }

  .contenu-write,
  .contenu-logo {
    width: 100%;
    height: auto;
    padding: 4vh 6vw;
  }

  .contenu-logo {
    order: -1;
  }

  .write-element h1 {
    font-size: 1.6rem;
  }
}
