import{a as S,b as I,c as F,e as V,i as q,j as T}from"./chunk-X3KNIMLA.js";import"./chunk-5ANC4TAO.js";import"./chunk-QUJFQN2Y.js";import"./chunk-7HYD6CC7.js";import"./chunk-5VDVK6VQ.js";import"./chunk-423VJ54E.js";import"./chunk-7FY2OE2O.js";import"./chunk-RC522WYB.js";import"./chunk-LMF7SRCC.js";import"./chunk-AWU72VOH.js";import"./chunk-W5XPJHSR.js";import"./chunk-3AW3VJFF.js";import"./chunk-MTHZ7MWU.js";import"./chunk-WI5MSH4N.js";import"./chunk-UT7HCVIF.js";import"./chunk-CKP3SGE2.js";import"./chunk-I4XHNRTT.js";import{A as C,E as _,I as d,J as o,K as r,L as l,Q as f,_ as a,ab as i,bb as b,cb as y,eb as k,fb as v,gb as w,jb as E,ka as P,lb as N,ma as h,pa as M,sa as O,wa as x,x as m,y as p}from"./chunk-XPS4RP6N.js";import"./chunk-YJ7VB3TT.js";import"./chunk-QDU6VP7L.js";import"./chunk-ICSGBKZQ.js";import"./chunk-YSN7XVTD.js";import"./chunk-R5HL6L5F.js";import"./chunk-M2X7KQLB.js";import"./chunk-REYR55MP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-42C7ZIID.js";import{e as u}from"./chunk-JHI3MBHO.js";function z(s,g){s&1&&(o(0,"div"),l(1,"ion-input",27)(2,"ion-input",28)(3,"ion-input",29),r())}var K=(()=>{let g=class g{constructor(e,t,n,c){this.fb=e,this.http=t,this.toastController=n,this.router=c,this.isChauffeur=!1,this.registerForm=this.fb.group({nom:["",i.required],prenom:["",i.required],telephone:["",[i.required,i.pattern("^[0-9]{8}$")]],email:["",[i.required,i.email]],password:["",[i.required,i.minLength(6)]],cin:["",[i.required,i.pattern("^[0-9]{8}$")]],numPermis:[""],plaque:[""],numLicence:[""]})}toggleChauffeur(e){this.isChauffeur=e.detail?.checked??e.target.checked;let t=this.registerForm.get("numPermis"),n=this.registerForm.get("plaque"),c=this.registerForm.get("numLicence");this.isChauffeur?(t?.setValidators([i.required]),n?.setValidators([i.required]),c?.setValidators([i.required])):(t?.clearValidators(),n?.clearValidators(),c?.clearValidators()),t?.updateValueAndValidity(),n?.updateValueAndValidity(),c?.updateValueAndValidity()}onSubmit(){return u(this,null,function*(){if(this.registerForm.invalid){this.showToast("Veuillez remplir tous les champs obligatoires");return}let e=this.registerForm.value,t={nom:e.nom,prenom:e.prenom,email:e.email,motDePasse:e.password,role:this.isChauffeur?"CHAUFFEUR":"CLIENT",telephone:parseInt(e.telephone),cin:parseInt(e.cin)};this.isChauffeur&&(t.permis=e.numPermis,t.matricule=e.plaque,t.numLicence=parseInt(e.numLicence)),this.http.post("http://localhost:8080/api/auth/register",t).subscribe({next:()=>u(this,null,function*(){this.registerForm.reset(),this.isChauffeur=!1,this.router.navigate(["/login"])}),error:n=>u(this,null,function*(){let c=n.error?.message||"Erreur lors de l\u2019inscription"})}),this.router.navigate(["/login"]),console.log("Payload envoy\xE9:",t),console.log("Formulaire valide:",this.registerForm.valid)})}showToast(e){return u(this,null,function*(){yield(yield this.toastController.create({message:e,duration:2500,position:"bottom",color:"primary"})).present()})}goToLogin(){this.router.navigate(["/login"])}};g.\u0275fac=function(t){return new(t||g)(p(E),p(M),p(q),p(x))},g.\u0275cmp=C({type:g,selectors:[["app-register"]],decls:39,vars:3,consts:[[1,"ion-padding","register-page"],["id","contenu-back"],[1,"contenu-write"],[1,"write-element"],[1,"soustitre"],[3,"ngSubmit","formGroup"],["formControlName","nom","placeholder","Nom","type","text","fill","outline"],["formControlName","prenom","placeholder","Pr\xE9nom","type","text","fill","outline"],["formControlName","telephone","placeholder","T\xE9l\xE9phone","type","tel","fill","outline"],["formControlName","email","placeholder","Email","type","email","fill","outline"],["formControlName","password","placeholder","Mot de passe","type","password","fill","outline"],["formControlName","cin","placeholder","CIN","type","text","fill","outline"],[1,"role-checkbox-group"],["type","checkbox",3,"change","checked"],[4,"ngIf"],["expand","block","type","submit"],[1,"goregister"],[3,"click"],[1,"contenu-logo"],[1,"logo0001"],["src","assets/img/stock-vector-logo-for-transport-company-car-icon-you-can-use-it-for-your-design-and-ideas-*********.jpg"],[1,"title0001"],[1,"reseaux"],["href","#"],["src","assets/img/facebook.png"],["src","assets/img/instagram.png"],["src","assets/img/e-mail.png"],["formControlName","numPermis","placeholder","Num\xE9ro de permis","type","text","fill","outline"],["formControlName","plaque","placeholder","Plaque d'immatriculation","type","text","fill","outline"],["formControlName","numLicence","placeholder","Votre Num\xE9ro de Licence","type","text","fill","outline"]],template:function(t,n){t&1&&(o(0,"ion-content",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"h1"),a(5,"INSCRIPTION"),r(),o(6,"p",4),a(7,"Cr\xE9er votre compte personnel"),r(),o(8,"form",5),f("ngSubmit",function(){return n.onSubmit()}),l(9,"ion-input",6)(10,"ion-input",7)(11,"ion-input",8)(12,"ion-input",9)(13,"ion-input",10)(14,"ion-input",11),o(15,"div",12)(16,"label")(17,"input",13),f("change",function(j){return n.toggleChauffeur(j)}),r(),a(18," Je suis un chauffeur "),r()(),_(19,z,4,0,"div",14),o(20,"ion-button",15),a(21,"S'inscrire"),r(),o(22,"p",16),a(23," D\xE9j\xE0 un compte ? "),o(24,"a",17),f("click",function(){return n.goToLogin()}),a(25,"Connectez-vous"),r()()()()(),o(26,"div",18)(27,"div",19),l(28,"img",20),r(),o(29,"div",21)(30,"h1"),a(31,"Rejoignez-nous !"),r()(),o(32,"div",22)(33,"a",23),l(34,"img",24),r(),o(35,"a",23),l(36,"img",25),r(),o(37,"a",23),l(38,"img",26),r()()()()()),t&2&&(m(8),d("formGroup",n.registerForm),m(9),d("checked",n.isChauffeur),m(2),d("ngIf",n.isChauffeur))},dependencies:[T,I,F,V,S,h,P,N,k,b,y,v,w,O],styles:['@charset "UTF-8";.register-page[_ngcontent-%COMP%]{--ion-background-color: #ffffff;display:flex;justify-content:center;align-items:center;padding:20px}.register-page[_ngcontent-%COMP%]   #contenu-back[_ngcontent-%COMP%]{display:flex;flex-direction:row;justify-content:right;align-items:stretch;max-width:2000px;width:100%;background-color:#fff;border-radius:15px;box-shadow:0 4px 20px #0000001a;overflow:hidden}.register-page[_ngcontent-%COMP%]   #contenu-back[_ngcontent-%COMP%]   .contenu-write[_ngcontent-%COMP%]{flex:2;padding:40px 30px;display:flex;flex-direction:column;justify-content:center}.register-page[_ngcontent-%COMP%]   #contenu-back[_ngcontent-%COMP%]   .contenu-write[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{color:#c8102e;text-align:center;font-size:2rem;font-weight:700;margin-bottom:1px}.register-page[_ngcontent-%COMP%]   #contenu-back[_ngcontent-%COMP%]   .contenu-write[_ngcontent-%COMP%]   .soustitre[_ngcontent-%COMP%]{color:#444;font-size:14px;text-align:center;margin-bottom:30px}.register-page[_ngcontent-%COMP%]   #contenu-back[_ngcontent-%COMP%]   .contenu-write[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]{display:flex;flex-direction:column}.register-page[_ngcontent-%COMP%]   #contenu-back[_ngcontent-%COMP%]   .contenu-write[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{--background: #fff;--padding-start: 14px;--border-radius: 8px;color:#000;font-size:14px;margin-bottom:10px!important}.register-page[_ngcontent-%COMP%]   #contenu-back[_ngcontent-%COMP%]   .contenu-write[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]   [_ngcontent-%COMP%]::placeholder{color:#888}.register-page[_ngcontent-%COMP%]   #contenu-back[_ngcontent-%COMP%]   .contenu-write[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .role-checkbox-group[_ngcontent-%COMP%]{display:flex;align-items:center;font-size:14px;color:#000;margin-top:10px}.register-page[_ngcontent-%COMP%]   #contenu-back[_ngcontent-%COMP%]   .contenu-write[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .role-checkbox-group[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%]{margin-right:10px;accent-color:#c8102e}.register-page[_ngcontent-%COMP%]   #contenu-back[_ngcontent-%COMP%]   .contenu-write[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--background: #c8102e;--color: #fff;--border-radius: 6px;font-weight:700;font-size:16px}.register-page[_ngcontent-%COMP%]   #contenu-back[_ngcontent-%COMP%]   .contenu-write[_ngcontent-%COMP%]   .goregister[_ngcontent-%COMP%]{margin-top:20px;text-align:center;font-size:14px;color:#444}.register-page[_ngcontent-%COMP%]   #contenu-back[_ngcontent-%COMP%]   .contenu-write[_ngcontent-%COMP%]   .goregister[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#c8102e;font-weight:600;margin-left:4px;cursor:pointer}.register-page[_ngcontent-%COMP%]   #contenu-back[_ngcontent-%COMP%]   .contenu-logo[_ngcontent-%COMP%]{flex:1;order:2;background-color:#fff;padding:30px;display:flex;flex-direction:column;align-items:center;justify-content:center;text-align:center;border-left:1px solid #eee}.register-page[_ngcontent-%COMP%]   #contenu-back[_ngcontent-%COMP%]   .contenu-logo[_ngcontent-%COMP%]   .logo0001[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:150px;height:auto;margin-bottom:1.5rem}.register-page[_ngcontent-%COMP%]   #contenu-back[_ngcontent-%COMP%]   .contenu-logo[_ngcontent-%COMP%]   .title0001[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{color:#d50000;font-size:20px;margin-bottom:15px}.register-page[_ngcontent-%COMP%]   #contenu-back[_ngcontent-%COMP%]   .contenu-logo[_ngcontent-%COMP%]   .reseaux[_ngcontent-%COMP%]{display:flex;gap:15px}.register-page[_ngcontent-%COMP%]   #contenu-back[_ngcontent-%COMP%]   .contenu-logo[_ngcontent-%COMP%]   .reseaux[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:28px;height:28px}@media (max-width: 768px){.register-page[_ngcontent-%COMP%]   #contenu-back[_ngcontent-%COMP%]{flex-direction:column-reverse}.register-page[_ngcontent-%COMP%]   #contenu-back[_ngcontent-%COMP%]   .contenu-logo[_ngcontent-%COMP%]{border-right:none;border-bottom:1px solid #eee}.register-page[_ngcontent-%COMP%]   #contenu-back[_ngcontent-%COMP%]   .contenu-write[_ngcontent-%COMP%]{padding:25px 20px}}.register-page[_ngcontent-%COMP%]   .reseaux[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{filter:brightness(0) invert(.2) grayscale(.2);transition:filter .2s}.register-page[_ngcontent-%COMP%]   .reseaux[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover{filter:brightness(0) invert(.5) grayscale(.1)}']});let s=g;return s})();export{K as RegisterPage};
