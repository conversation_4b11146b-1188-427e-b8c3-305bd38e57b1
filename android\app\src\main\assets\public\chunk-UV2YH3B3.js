import{a as T}from"./chunk-P2ZJ4VL5.js";import{c as z,d as F,f as N,j}from"./chunk-TCELLFCD.js";import{$ as x,A as f,E as h,I as u,J as e,K as n,L as r,Q as m,R as _,_ as o,ab as p,bb as P,cb as w,eb as O,fb as k,gb as y,jb as S,ka as C,kb as E,lb as I,ma as b,pa as v,wa as M,x as d,y as s}from"./chunk-XPS4RP6N.js";import"./chunk-QUM6RZVN.js";import"./chunk-YJ7VB3TT.js";import"./chunk-QDU6VP7L.js";import"./chunk-ICSGBKZQ.js";import"./chunk-YSN7XVTD.js";import"./chunk-R5HL6L5F.js";import"./chunk-4WFVMWDK.js";import"./chunk-M2X7KQLB.js";import"./chunk-2YSZFPCQ.js";import"./chunk-57YRIO75.js";import"./chunk-REYR55MP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-42C7ZIID.js";import"./chunk-JHI3MBHO.js";function R(l,a){if(l&1&&(e(0,"ion-text",24),o(1),n()),l&2){let L=_();d(),x(L.errorMessage)}}var K=(()=>{let a=class a{constructor(c,t,i,g){this.fb=c,this.http=t,this.router=i,this.authService=g,this.loginForm=this.fb.group({email:["",[p.required,p.email]],motDePasse:["",p.required]}),this.errorMessage=""}ngOnInit(){}onLogin(){if(this.loginForm.valid){let c=this.loginForm.value;this.http.post("http://localhost:8080/api/auth/login",c).subscribe({next:t=>{console.log("R\xE9ponse login :",t),this.authService.saveSession(t.token,t.role),t.role==="ADMIN"?this.router.navigate(["/admin/dashboard"]):this.router.navigate(["/home"])},error:t=>{console.error("Erreur de connexion",t),this.errorMessage="Email ou mot de passe invalide."}})}}loginWithGoogle(){window.location.href="http://localhost:8080/oauth2/authorization/google"}loginWithFacebook(){window.location.href="http://localhost:8080/oauth2/authorization/facebook"}goToRegister(){this.router.navigate(["/register"])}};a.\u0275fac=function(t){return new(t||a)(s(S),s(v),s(M),s(T))},a.\u0275cmp=f({type:a,selectors:[["app-login"]],decls:38,vars:2,consts:[[1,"ion-padding","login-page"],["id","contenu-back"],[1,"contenu-write"],[1,"write-element"],[1,"soustitre"],[3,"ngSubmit","formGroup"],["formControlName","email","placeholder","email","type","email","fill","outline",1,"email"],["formControlName","motDePasse","placeholder","Mot de passe","type","password","fill","outline",1,"motDePasse"],["color","danger",4,"ngIf"],["expand","block","type","submit"],["expand","block","fill","clear","color","black",3,"click"],["src","assets/img/google.png","width","20",2,"margin-right","8px"],["src","assets/img/facebookb.png","width","20",2,"margin-right","8px"],[1,"goregister",3,"click"],["routerLink","/register"],[1,"contenu-logo"],[1,"logo0001"],["src","assets/img/stock-vector-logo-for-transport-company-car-icon-you-can-use-it-for-your-design-and-ideas-*********.jpg"],[1,"title0001"],[1,"reseaux"],["href","#"],["src","assets/img/facebook.png"],["src","assets/img/instagram.png"],["src","assets/img/e-mail.png"],["color","danger"]],template:function(t,i){t&1&&(e(0,"ion-content",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"h1"),o(5,"SE CONNECTER"),n(),e(6,"p",4),o(7,"Acc\xE9dez \xE0 Votre Compte "),n(),e(8,"form",5),m("ngSubmit",function(){return i.onLogin()}),r(9,"ion-input",6)(10,"ion-input",7)(11,"hr"),h(12,R,2,1,"ion-text",8),e(13,"ion-button",9),o(14,"CONNECTER"),n(),e(15,"ion-button",10),m("click",function(){return i.loginWithGoogle()}),r(16,"img",11),o(17," Google "),n(),e(18,"ion-button",10),m("click",function(){return i.loginWithFacebook()}),r(19,"img",12),o(20," Facebook "),n()(),e(21,"p",13),m("click",function(){return i.goToRegister()}),o(22," Vous n'avez pas de compte ? "),e(23,"a",14),o(24,"Inscrivez-vous !"),n()()()(),e(25,"div",15)(26,"div",16),r(27,"img",17),n(),e(28,"div",18)(29,"h1"),o(30,"Rejoignez-nous !"),n()(),e(31,"div",19)(32,"a",20),r(33,"img",21),n(),e(34,"a",20),r(35,"img",22),n(),e(36,"a",20),r(37,"img",23),n()()()()()),t&2&&(d(8),u("formGroup",i.loginForm),d(4),u("ngIf",i.errorMessage))},dependencies:[b,C,E,O,P,w,I,k,y,F,j,z,N],styles:["ion-content.login-page[_ngcontent-%COMP%]{--background: #ffffff;display:flex;justify-content:center;align-items:center;height:100vh;width:100vw;padding:0;margin:0;overflow:hidden}#contenu-back[_ngcontent-%COMP%]{display:flex;flex-direction:row;width:100%;max-width:100vw;height:100%;max-height:100vh;background:#fff;border-radius:0;overflow:auto}.contenu-write[_ngcontent-%COMP%]{flex:1;min-width:0;padding:5vh 5vw;display:flex;justify-content:center;align-items:center;background:#fff;height:100%}.write-element[_ngcontent-%COMP%]{width:100%;max-width:400px}.write-element[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{color:#c8102e;font-size:2rem;font-weight:700;margin-bottom:1rem;text-align:center}.soustitre[_ngcontent-%COMP%]{color:#666;font-size:1rem;text-align:center;margin-bottom:2rem}ion-input.email[_ngcontent-%COMP%], ion-input.password[_ngcontent-%COMP%]{--background: #ffffff;--color: #333;--placeholder-color: #999;--placeholder-opacity: .8;--border-radius: 8px;--padding-start: 16px;--padding-end: 16px;margin-bottom:1.5rem;width:100%}.input-links[_ngcontent-%COMP%]{display:flex;justify-content:space-between;color:#333;align-items:center;margin-bottom:1.5rem;font-size:.875rem;flex-wrap:wrap}.input-links[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.input-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#c8102e;font-weight:500;text-decoration:none}.input-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{text-decoration:underline}ion-text[color=danger][_ngcontent-%COMP%]{display:block;font-size:.875rem;margin-bottom:1.5rem;text-align:center}ion-button[_ngcontent-%COMP%]{--background: #c8102e;--background-activated: #a50d25;--color: #ffffff;--border-radius: 8px;margin-bottom:1rem;font-weight:600;letter-spacing:1px;text-transform:uppercase;width:100%}ion-button[fill=clear][_ngcontent-%COMP%]{--color: #333;--border-color: #c8102e;--border-width: 1px;--border-style: solid;--background-activated: #f5f5f5}.goregister[_ngcontent-%COMP%]{text-align:center;font-size:.9rem;color:#333}.goregister[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#c8102e;font-weight:500;text-decoration:none;cursor:pointer}.goregister[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{text-decoration:underline}.contenu-logo[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;justify-content:center;align-items:center;padding:5vh 5vw;background-color:#fff;height:100%;text-align:center}.logo0001[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:150px;height:auto;margin-bottom:1.5rem}.title0001[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:700;color:#c8102e;margin-bottom:1rem}.reseaux[_ngcontent-%COMP%]{display:flex;gap:1rem;justify-content:center}.reseaux[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:30px;height:30px;filter:brightness(2) invert(1);transition:transform .3s ease}.reseaux[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover{transform:scale(1.1)}@media (max-width: 768px){#contenu-back[_ngcontent-%COMP%]{flex-direction:column;height:100vh}.contenu-write[_ngcontent-%COMP%], .contenu-logo[_ngcontent-%COMP%]{width:100%;height:auto;padding:4vh 6vw}.contenu-logo[_ngcontent-%COMP%]{order:-1}.write-element[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:1.6rem}}"]});let l=a;return l})();export{K as LoginPage};
