import{b as Pc,c as Fc,d as Lc,e as jc,f as Vc}from"./chunk-ICSGBKZQ.js";import{h as oh,i as ih}from"./chunk-YSN7XVTD.js";import{a as hh,d as ph}from"./chunk-R5HL6L5F.js";import{a as Bc}from"./chunk-M2X7KQLB.js";import{a as Lt,e as lh,f as dh,g as fh,j as kc}from"./chunk-REYR55MP.js";import{a as Re,b as sh,c as ah,d as ch,e as Ri,f as uh}from"./chunk-C5RQ2IC2.js";import{b as Ft}from"./chunk-42C7ZIID.js";import{a as g,b as A,d as Oc,e as X}from"./chunk-JHI3MBHO.js";var Uc;function Hc(){return Uc}function wt(e){let n=Uc;return Uc=e,n}var gE=Symbol("NotFound"),Ni=class extends Error{name="\u0275NotFound";constructor(n){super(n)}};function Qn(e){return e===gE||e?.name==="\u0275NotFound"}function Fi(e,n){return Object.is(e,n)}var re=null,xi=!1,$c=1,mE=null,he=Symbol("SIGNAL");function R(e){let n=re;return re=e,n}function Li(){return re}var dn={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function Kn(e){if(xi)throw new Error("");if(re===null)return;re.consumerOnSignalRead(e);let n=re.nextProducerIndex++;if(Ui(re),n<re.producerNode.length&&re.producerNode[n]!==e&&ro(re)){let t=re.producerNode[n];Bi(t,re.producerIndexOfThis[n])}re.producerNode[n]!==e&&(re.producerNode[n]=e,re.producerIndexOfThis[n]=ro(re)?mh(e,re,n):0),re.producerLastReadVersion[n]=e.version}function gh(){$c++}function ji(e){if(!(ro(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===$c)){if(!e.producerMustRecompute(e)&&!oo(e)){Pi(e);return}e.producerRecomputeValue(e),Pi(e)}}function zc(e){if(e.liveConsumerNode===void 0)return;let n=xi;xi=!0;try{for(let t of e.liveConsumerNode)t.dirty||vE(t)}finally{xi=n}}function Gc(){return re?.consumerAllowSignalWrites!==!1}function vE(e){e.dirty=!0,zc(e),e.consumerMarkedDirty?.(e)}function Pi(e){e.dirty=!1,e.lastCleanEpoch=$c}function fn(e){return e&&(e.nextProducerIndex=0),R(e)}function Jn(e,n){if(R(n),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(ro(e))for(let t=e.nextProducerIndex;t<e.producerNode.length;t++)Bi(e.producerNode[t],e.producerIndexOfThis[t]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function oo(e){Ui(e);for(let n=0;n<e.producerNode.length;n++){let t=e.producerNode[n],r=e.producerLastReadVersion[n];if(r!==t.version||(ji(t),r!==t.version))return!0}return!1}function Vi(e){if(Ui(e),ro(e))for(let n=0;n<e.producerNode.length;n++)Bi(e.producerNode[n],e.producerIndexOfThis[n]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function mh(e,n,t){if(vh(e),e.liveConsumerNode.length===0&&yh(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=mh(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(t),e.liveConsumerNode.push(n)-1}function Bi(e,n){if(vh(e),e.liveConsumerNode.length===1&&yh(e))for(let r=0;r<e.producerNode.length;r++)Bi(e.producerNode[r],e.producerIndexOfThis[r]);let t=e.liveConsumerNode.length-1;if(e.liveConsumerNode[n]=e.liveConsumerNode[t],e.liveConsumerIndexOfThis[n]=e.liveConsumerIndexOfThis[t],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,n<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[n],o=e.liveConsumerNode[n];Ui(o),o.producerIndexOfThis[r]=n}}function ro(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function Ui(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function vh(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function yh(e){return e.producerNode!==void 0}function Hi(e){mE?.(e)}function $i(e,n){let t=Object.create(yE);t.computation=e,n!==void 0&&(t.equal=n);let r=()=>{if(ji(t),Kn(t),t.value===no)throw t.error;return t.value};return r[he]=t,Hi(t),r}var Oi=Symbol("UNSET"),ki=Symbol("COMPUTING"),no=Symbol("ERRORED"),yE=A(g({},dn),{value:Oi,dirty:!0,error:null,equal:Fi,kind:"computed",producerMustRecompute(e){return e.value===Oi||e.value===ki},producerRecomputeValue(e){if(e.value===ki)throw new Error("");let n=e.value;e.value=ki;let t=fn(e),r,o=!1;try{r=e.computation(),R(null),o=n!==Oi&&n!==no&&r!==no&&e.equal(n,r)}catch(i){r=no,e.error=i}finally{Jn(e,t)}if(o){e.value=n;return}e.value=r,e.version++}});function DE(){throw new Error}var Dh=DE;function Eh(e){Dh(e)}function Wc(e){Dh=e}var EE=null;function qc(e,n){let t=Object.create(zi);t.value=e,n!==void 0&&(t.equal=n);let r=()=>Ch(t);return r[he]=t,Hi(t),[r,s=>Xn(t,s),s=>Zc(t,s)]}function Ch(e){return Kn(e),e.value}function Xn(e,n){Gc()||Eh(e),e.equal(e.value,n)||(e.value=n,CE(e))}function Zc(e,n){Gc()||Eh(e),Xn(e,n(e.value))}var zi=A(g({},dn),{equal:Fi,value:void 0,kind:"signal"});function CE(e){e.version++,gh(),zc(e),EE?.(e)}function _(e){return typeof e=="function"}function er(e){let t=e(r=>{Error.call(r),r.stack=new Error().stack});return t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,t}var Gi=er(e=>function(t){e(this),this.message=t?`${t.length} errors occurred during unsubscription:
${t.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=t});function io(e,n){if(e){let t=e.indexOf(n);0<=t&&e.splice(t,1)}}var ee=class e{constructor(n){this.initialTeardown=n,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let n;if(!this.closed){this.closed=!0;let{_parentage:t}=this;if(t)if(this._parentage=null,Array.isArray(t))for(let i of t)i.remove(this);else t.remove(this);let{initialTeardown:r}=this;if(_(r))try{r()}catch(i){n=i instanceof Gi?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{wh(i)}catch(s){n=n??[],s instanceof Gi?n=[...n,...s.errors]:n.push(s)}}if(n)throw new Gi(n)}}add(n){var t;if(n&&n!==this)if(this.closed)wh(n);else{if(n instanceof e){if(n.closed||n._hasParent(this))return;n._addParent(this)}(this._finalizers=(t=this._finalizers)!==null&&t!==void 0?t:[]).push(n)}}_hasParent(n){let{_parentage:t}=this;return t===n||Array.isArray(t)&&t.includes(n)}_addParent(n){let{_parentage:t}=this;this._parentage=Array.isArray(t)?(t.push(n),t):t?[t,n]:n}_removeParent(n){let{_parentage:t}=this;t===n?this._parentage=null:Array.isArray(t)&&io(t,n)}remove(n){let{_finalizers:t}=this;t&&io(t,n),n instanceof e&&n._removeParent(this)}};ee.EMPTY=(()=>{let e=new ee;return e.closed=!0,e})();var Yc=ee.EMPTY;function Wi(e){return e instanceof ee||e&&"closed"in e&&_(e.remove)&&_(e.add)&&_(e.unsubscribe)}function wh(e){_(e)?e():e.unsubscribe()}var qe={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var tr={setTimeout(e,n,...t){let{delegate:r}=tr;return r?.setTimeout?r.setTimeout(e,n,...t):setTimeout(e,n,...t)},clearTimeout(e){let{delegate:n}=tr;return(n?.clearTimeout||clearTimeout)(e)},delegate:void 0};function qi(e){tr.setTimeout(()=>{let{onUnhandledError:n}=qe;if(n)n(e);else throw e})}function so(){}var Ih=Qc("C",void 0,void 0);function bh(e){return Qc("E",void 0,e)}function _h(e){return Qc("N",e,void 0)}function Qc(e,n,t){return{kind:e,value:n,error:t}}var hn=null;function nr(e){if(qe.useDeprecatedSynchronousErrorHandling){let n=!hn;if(n&&(hn={errorThrown:!1,error:null}),e(),n){let{errorThrown:t,error:r}=hn;if(hn=null,t)throw r}}else e()}function Th(e){qe.useDeprecatedSynchronousErrorHandling&&hn&&(hn.errorThrown=!0,hn.error=e)}var pn=class extends ee{constructor(n){super(),this.isStopped=!1,n?(this.destination=n,Wi(n)&&n.add(this)):this.destination=bE}static create(n,t,r){return new rr(n,t,r)}next(n){this.isStopped?Jc(_h(n),this):this._next(n)}error(n){this.isStopped?Jc(bh(n),this):(this.isStopped=!0,this._error(n))}complete(){this.isStopped?Jc(Ih,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(n){this.destination.next(n)}_error(n){try{this.destination.error(n)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},wE=Function.prototype.bind;function Kc(e,n){return wE.call(e,n)}var Xc=class{constructor(n){this.partialObserver=n}next(n){let{partialObserver:t}=this;if(t.next)try{t.next(n)}catch(r){Zi(r)}}error(n){let{partialObserver:t}=this;if(t.error)try{t.error(n)}catch(r){Zi(r)}else Zi(n)}complete(){let{partialObserver:n}=this;if(n.complete)try{n.complete()}catch(t){Zi(t)}}},rr=class extends pn{constructor(n,t,r){super();let o;if(_(n)||!n)o={next:n??void 0,error:t??void 0,complete:r??void 0};else{let i;this&&qe.useDeprecatedNextContext?(i=Object.create(n),i.unsubscribe=()=>this.unsubscribe(),o={next:n.next&&Kc(n.next,i),error:n.error&&Kc(n.error,i),complete:n.complete&&Kc(n.complete,i)}):o=n}this.destination=new Xc(o)}};function Zi(e){qe.useDeprecatedSynchronousErrorHandling?Th(e):qi(e)}function IE(e){throw e}function Jc(e,n){let{onStoppedNotification:t}=qe;t&&tr.setTimeout(()=>t(e,n))}var bE={closed:!0,next:so,error:IE,complete:so};var or=typeof Symbol=="function"&&Symbol.observable||"@@observable";function me(e){return e}function eu(...e){return tu(e)}function tu(e){return e.length===0?me:e.length===1?e[0]:function(t){return e.reduce((r,o)=>o(r),t)}}var O=(()=>{class e{constructor(t){t&&(this._subscribe=t)}lift(t){let r=new e;return r.source=this,r.operator=t,r}subscribe(t,r,o){let i=TE(t)?t:new rr(t,r,o);return nr(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(t){try{return this._subscribe(t)}catch(r){t.error(r)}}forEach(t,r){return r=Sh(r),new r((o,i)=>{let s=new rr({next:a=>{try{t(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(t){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(t)}[or](){return this}pipe(...t){return tu(t)(this)}toPromise(t){return t=Sh(t),new t((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=n=>new e(n),e})();function Sh(e){var n;return(n=e??qe.Promise)!==null&&n!==void 0?n:Promise}function _E(e){return e&&_(e.next)&&_(e.error)&&_(e.complete)}function TE(e){return e&&e instanceof pn||_E(e)&&Wi(e)}function nu(e){return _(e?.lift)}function F(e){return n=>{if(nu(n))return n.lift(function(t){try{return e(t,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function P(e,n,t,r,o){return new ru(e,n,t,r,o)}var ru=class extends pn{constructor(n,t,r,o,i,s){super(n),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=t?function(a){try{t(a)}catch(c){n.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){n.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){n.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var n;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:t}=this;super.unsubscribe(),!t&&((n=this.onFinalize)===null||n===void 0||n.call(this))}}};function ir(){return F((e,n)=>{let t=null;e._refCount++;let r=P(n,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){t=null;return}let o=e._connection,i=t;t=null,o&&(!i||o===i)&&o.unsubscribe(),n.unsubscribe()});e.subscribe(r),r.closed||(t=e.connect())})}var sr=class extends O{constructor(n,t){super(),this.source=n,this.subjectFactory=t,this._subject=null,this._refCount=0,this._connection=null,nu(n)&&(this.lift=n.lift)}_subscribe(n){return this.getSubject().subscribe(n)}getSubject(){let n=this._subject;return(!n||n.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:n}=this;this._subject=this._connection=null,n?.unsubscribe()}connect(){let n=this._connection;if(!n){n=this._connection=new ee;let t=this.getSubject();n.add(this.source.subscribe(P(t,void 0,()=>{this._teardown(),t.complete()},r=>{this._teardown(),t.error(r)},()=>this._teardown()))),n.closed&&(this._connection=null,n=ee.EMPTY)}return n}refCount(){return ir()(this)}};var Mh=er(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var H=(()=>{class e extends O{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(t){let r=new Yi(this,this);return r.operator=t,r}_throwIfClosed(){if(this.closed)throw new Mh}next(t){nr(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(t)}})}error(t){nr(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=t;let{observers:r}=this;for(;r.length;)r.shift().error(t)}})}complete(){nr(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:t}=this;for(;t.length;)t.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var t;return((t=this.observers)===null||t===void 0?void 0:t.length)>0}_trySubscribe(t){return this._throwIfClosed(),super._trySubscribe(t)}_subscribe(t){return this._throwIfClosed(),this._checkFinalizedStatuses(t),this._innerSubscribe(t)}_innerSubscribe(t){let{hasError:r,isStopped:o,observers:i}=this;return r||o?Yc:(this.currentObservers=null,i.push(t),new ee(()=>{this.currentObservers=null,io(i,t)}))}_checkFinalizedStatuses(t){let{hasError:r,thrownError:o,isStopped:i}=this;r?t.error(o):i&&t.complete()}asObservable(){let t=new O;return t.source=this,t}}return e.create=(n,t)=>new Yi(n,t),e})(),Yi=class extends H{constructor(n,t){super(),this.destination=n,this.source=t}next(n){var t,r;(r=(t=this.destination)===null||t===void 0?void 0:t.next)===null||r===void 0||r.call(t,n)}error(n){var t,r;(r=(t=this.destination)===null||t===void 0?void 0:t.error)===null||r===void 0||r.call(t,n)}complete(){var n,t;(t=(n=this.destination)===null||n===void 0?void 0:n.complete)===null||t===void 0||t.call(n)}_subscribe(n){var t,r;return(r=(t=this.source)===null||t===void 0?void 0:t.subscribe(n))!==null&&r!==void 0?r:Yc}};var te=class extends H{constructor(n){super(),this._value=n}get value(){return this.getValue()}_subscribe(n){let t=super._subscribe(n);return!t.closed&&n.next(this._value),t}getValue(){let{hasError:n,thrownError:t,_value:r}=this;if(n)throw t;return this._throwIfClosed(),r}next(n){super.next(this._value=n)}};var we=new O(e=>e.complete());function Ah(e){return e&&_(e.schedule)}function Rh(e){return e[e.length-1]}function Qi(e){return _(Rh(e))?e.pop():void 0}function jt(e){return Ah(Rh(e))?e.pop():void 0}function ao(e,n,t,r){var o=arguments.length,i=o<3?n:r===null?r=Object.getOwnPropertyDescriptor(n,t):r,s;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")i=Reflect.decorate(e,n,t,r);else for(var a=e.length-1;a>=0;a--)(s=e[a])&&(i=(o<3?s(i):o>3?s(n,t,i):s(n,t))||i);return o>3&&i&&Object.defineProperty(n,t,i),i}function xh(e,n,t,r){function o(i){return i instanceof t?i:new t(function(s){s(i)})}return new(t||(t=Promise))(function(i,s){function a(l){try{u(r.next(l))}catch(d){s(d)}}function c(l){try{u(r.throw(l))}catch(d){s(d)}}function u(l){l.done?i(l.value):o(l.value).then(a,c)}u((r=r.apply(e,n||[])).next())})}function Nh(e){var n=typeof Symbol=="function"&&Symbol.iterator,t=n&&e[n],r=0;if(t)return t.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(n?"Object is not iterable.":"Symbol.iterator is not defined.")}function gn(e){return this instanceof gn?(this.v=e,this):new gn(e)}function Oh(e,n,t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=t.apply(e,n||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(f){return function(m){return Promise.resolve(m).then(f,d)}}function a(f,m){r[f]&&(o[f]=function(y){return new Promise(function(w,T){i.push([f,y,w,T])>1||c(f,y)})},m&&(o[f]=m(o[f])))}function c(f,m){try{u(r[f](m))}catch(y){h(i[0][3],y)}}function u(f){f.value instanceof gn?Promise.resolve(f.value.v).then(l,d):h(i[0][2],f)}function l(f){c("next",f)}function d(f){c("throw",f)}function h(f,m){f(m),i.shift(),i.length&&c(i[0][0],i[0][1])}}function kh(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n=e[Symbol.asyncIterator],t;return n?n.call(e):(e=typeof Nh=="function"?Nh(e):e[Symbol.iterator](),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(i){t[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(u){i({value:u,done:a})},s)}}var ar=e=>e&&typeof e.length=="number"&&typeof e!="function";function Ki(e){return _(e?.then)}function Ji(e){return _(e[or])}function Xi(e){return Symbol.asyncIterator&&_(e?.[Symbol.asyncIterator])}function es(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function SE(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var ts=SE();function ns(e){return _(e?.[ts])}function rs(e){return Oh(this,arguments,function*(){let t=e.getReader();try{for(;;){let{value:r,done:o}=yield gn(t.read());if(o)return yield gn(void 0);yield yield gn(r)}}finally{t.releaseLock()}})}function os(e){return _(e?.getReader)}function J(e){if(e instanceof O)return e;if(e!=null){if(Ji(e))return ME(e);if(ar(e))return AE(e);if(Ki(e))return RE(e);if(Xi(e))return Ph(e);if(ns(e))return NE(e);if(os(e))return xE(e)}throw es(e)}function ME(e){return new O(n=>{let t=e[or]();if(_(t.subscribe))return t.subscribe(n);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function AE(e){return new O(n=>{for(let t=0;t<e.length&&!n.closed;t++)n.next(e[t]);n.complete()})}function RE(e){return new O(n=>{e.then(t=>{n.closed||(n.next(t),n.complete())},t=>n.error(t)).then(null,qi)})}function NE(e){return new O(n=>{for(let t of e)if(n.next(t),n.closed)return;n.complete()})}function Ph(e){return new O(n=>{OE(e,n).catch(t=>n.error(t))})}function xE(e){return Ph(rs(e))}function OE(e,n){var t,r,o,i;return xh(this,void 0,void 0,function*(){try{for(t=kh(e);r=yield t.next(),!r.done;){let s=r.value;if(n.next(s),n.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=t.return)&&(yield i.call(t))}finally{if(o)throw o.error}}n.complete()})}function Ie(e,n,t,r=0,o=!1){let i=n.schedule(function(){t(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function is(e,n=0){return F((t,r)=>{t.subscribe(P(r,o=>Ie(r,e,()=>r.next(o),n),()=>Ie(r,e,()=>r.complete(),n),o=>Ie(r,e,()=>r.error(o),n)))})}function ss(e,n=0){return F((t,r)=>{r.add(e.schedule(()=>t.subscribe(r),n))})}function Fh(e,n){return J(e).pipe(ss(n),is(n))}function Lh(e,n){return J(e).pipe(ss(n),is(n))}function jh(e,n){return new O(t=>{let r=0;return n.schedule(function(){r===e.length?t.complete():(t.next(e[r++]),t.closed||this.schedule())})})}function Vh(e,n){return new O(t=>{let r;return Ie(t,n,()=>{r=e[ts](),Ie(t,n,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){t.error(s);return}i?t.complete():t.next(o)},0,!0)}),()=>_(r?.return)&&r.return()})}function as(e,n){if(!e)throw new Error("Iterable cannot be null");return new O(t=>{Ie(t,n,()=>{let r=e[Symbol.asyncIterator]();Ie(t,n,()=>{r.next().then(o=>{o.done?t.complete():t.next(o.value)})},0,!0)})})}function Bh(e,n){return as(rs(e),n)}function Uh(e,n){if(e!=null){if(Ji(e))return Fh(e,n);if(ar(e))return jh(e,n);if(Ki(e))return Lh(e,n);if(Xi(e))return as(e,n);if(ns(e))return Vh(e,n);if(os(e))return Bh(e,n)}throw es(e)}function G(e,n){return n?Uh(e,n):J(e)}function b(...e){let n=jt(e);return G(e,n)}function cr(e,n){let t=_(e)?e:()=>e,r=o=>o.error(t());return new O(n?o=>n.schedule(r,0,o):r)}function ou(e){return!!e&&(e instanceof O||_(e.lift)&&_(e.subscribe))}var It=er(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function N(e,n){return F((t,r)=>{let o=0;t.subscribe(P(r,i=>{r.next(e.call(n,i,o++))}))})}var{isArray:kE}=Array;function PE(e,n){return kE(n)?e(...n):e(n)}function ur(e){return N(n=>PE(e,n))}var{isArray:FE}=Array,{getPrototypeOf:LE,prototype:jE,keys:VE}=Object;function cs(e){if(e.length===1){let n=e[0];if(FE(n))return{args:n,keys:null};if(BE(n)){let t=VE(n);return{args:t.map(r=>n[r]),keys:t}}}return{args:e,keys:null}}function BE(e){return e&&typeof e=="object"&&LE(e)===jE}function us(e,n){return e.reduce((t,r,o)=>(t[r]=n[o],t),{})}function mn(...e){let n=jt(e),t=Qi(e),{args:r,keys:o}=cs(e);if(r.length===0)return G([],n);let i=new O(UE(r,n,o?s=>us(o,s):me));return t?i.pipe(ur(t)):i}function UE(e,n,t=me){return r=>{Hh(n,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)Hh(n,()=>{let u=G(e[c],n),l=!1;u.subscribe(P(r,d=>{i[c]=d,l||(l=!0,a--),a||r.next(t(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function Hh(e,n,t){e?Ie(t,e,n):n()}function $h(e,n,t,r,o,i,s,a){let c=[],u=0,l=0,d=!1,h=()=>{d&&!c.length&&!u&&n.complete()},f=y=>u<r?m(y):c.push(y),m=y=>{i&&n.next(y),u++;let w=!1;J(t(y,l++)).subscribe(P(n,T=>{o?.(T),i?f(T):n.next(T)},()=>{w=!0},void 0,()=>{if(w)try{for(u--;c.length&&u<r;){let T=c.shift();s?Ie(n,s,()=>m(T)):m(T)}h()}catch(T){n.error(T)}}))};return e.subscribe(P(n,f,()=>{d=!0,h()})),()=>{a?.()}}function Z(e,n,t=1/0){return _(n)?Z((r,o)=>N((i,s)=>n(r,i,o,s))(J(e(r,o))),t):(typeof n=="number"&&(t=n),F((r,o)=>$h(r,o,e,t)))}function lr(e=1/0){return Z(me,e)}function zh(){return lr(1)}function dr(...e){return zh()(G(e,jt(e)))}function co(e){return new O(n=>{J(e()).subscribe(n)})}function iu(...e){let n=Qi(e),{args:t,keys:r}=cs(e),o=new O(i=>{let{length:s}=t;if(!s){i.complete();return}let a=new Array(s),c=s,u=s;for(let l=0;l<s;l++){let d=!1;J(t[l]).subscribe(P(i,h=>{d||(d=!0,u--),a[l]=h},()=>c--,void 0,()=>{(!c||!d)&&(u||i.next(r?us(r,a):a),i.complete())}))}});return n?o.pipe(ur(n)):o}var HE=["addListener","removeListener"],$E=["addEventListener","removeEventListener"],zE=["on","off"];function uo(e,n,t,r){if(_(t)&&(r=t,t=void 0),r)return uo(e,n,t).pipe(ur(r));let[o,i]=qE(e)?$E.map(s=>a=>e[s](n,a,t)):GE(e)?HE.map(Gh(e,n)):WE(e)?zE.map(Gh(e,n)):[];if(!o&&ar(e))return Z(s=>uo(s,n,t))(J(e));if(!o)throw new TypeError("Invalid event target");return new O(s=>{let a=(...c)=>s.next(1<c.length?c:c[0]);return o(a),()=>i(a)})}function Gh(e,n){return t=>r=>e[t](n,r)}function GE(e){return _(e.addListener)&&_(e.removeListener)}function WE(e){return _(e.on)&&_(e.off)}function qE(e){return _(e.addEventListener)&&_(e.removeEventListener)}function oe(e,n){return F((t,r)=>{let o=0;t.subscribe(P(r,i=>e.call(n,i,o++)&&r.next(i)))})}function it(e){return F((n,t)=>{let r=null,o=!1,i;r=n.subscribe(P(t,void 0,void 0,s=>{i=J(e(s,it(e)(n))),r?(r.unsubscribe(),r=null,i.subscribe(t)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(t))})}function Wh(e,n,t,r,o){return(i,s)=>{let a=t,c=n,u=0;i.subscribe(P(s,l=>{let d=u++;c=a?e(c,l,d):(a=!0,l),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function st(e,n){return _(n)?Z(e,n,1):Z(e,1)}function Vt(e){return F((n,t)=>{let r=!1;n.subscribe(P(t,o=>{r=!0,t.next(o)},()=>{r||t.next(e),t.complete()}))})}function bt(e){return e<=0?()=>we:F((n,t)=>{let r=0;n.subscribe(P(t,o=>{++r<=e&&(t.next(o),e<=r&&t.complete())}))})}function su(e,n=me){return e=e??ZE,F((t,r)=>{let o,i=!0;t.subscribe(P(r,s=>{let a=n(s);(i||!e(o,a))&&(i=!1,o=a,r.next(s))}))})}function ZE(e,n){return e===n}function ls(e=YE){return F((n,t)=>{let r=!1;n.subscribe(P(t,o=>{r=!0,t.next(o)},()=>r?t.complete():t.error(e())))})}function YE(){return new It}function Bt(e){return F((n,t)=>{try{n.subscribe(t)}finally{t.add(e)}})}function _t(e,n){let t=arguments.length>=2;return r=>r.pipe(e?oe((o,i)=>e(o,i,r)):me,bt(1),t?Vt(n):ls(()=>new It))}function fr(e){return e<=0?()=>we:F((n,t)=>{let r=[];n.subscribe(P(t,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)t.next(o);t.complete()},void 0,()=>{r=null}))})}function au(e,n){let t=arguments.length>=2;return r=>r.pipe(e?oe((o,i)=>e(o,i,r)):me,fr(1),t?Vt(n):ls(()=>new It))}function cu(e,n){return F(Wh(e,n,arguments.length>=2,!0))}function uu(...e){let n=jt(e);return F((t,r)=>{(n?dr(e,t,n):dr(e,t)).subscribe(r)})}function ie(e,n){return F((t,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();t.subscribe(P(r,c=>{o?.unsubscribe();let u=0,l=i++;J(e(c,l)).subscribe(o=P(r,d=>r.next(n?n(c,d,l,u++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function ds(e){return F((n,t)=>{J(e).subscribe(P(t,()=>t.complete(),so)),!t.closed&&n.subscribe(t)})}function ce(e,n,t){let r=_(e)||n||t?{next:e,error:n,complete:t}:e;return r?F((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(P(i,c=>{var u;(u=r.next)===null||u===void 0||u.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var u;a=!1,(u=r.error)===null||u===void 0||u.call(r,c),i.error(c)},()=>{var c,u;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(u=r.finalize)===null||u===void 0||u.call(r)}))}):me}function qh(e){let n=R(null);try{return e()}finally{R(n)}}var ms="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",C=class extends Error{code;constructor(n,t){super(pr(n,t)),this.code=n}};function QE(e){return`NG0${Math.abs(e)}`}function pr(e,n){return`${QE(e)}${n?": "+n:""}`}var po=globalThis;function W(e){for(let n in e)if(e[n]===W)return n;throw Error("")}function Kh(e,n){for(let t in n)n.hasOwnProperty(t)&&!e.hasOwnProperty(t)&&(e[t]=n[t])}function be(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(be).join(", ")}]`;if(e==null)return""+e;let n=e.overriddenName||e.name;if(n)return`${n}`;let t=e.toString();if(t==null)return""+t;let r=t.indexOf(`
`);return r>=0?t.slice(0,r):t}function wu(e,n){return e?n?`${e} ${n}`:e:n||""}var KE=W({__forward_ref__:W});function Ye(e){return e.__forward_ref__=Ye,e.toString=function(){return be(this())},e}function de(e){return Iu(e)?e():e}function Iu(e){return typeof e=="function"&&e.hasOwnProperty(KE)&&e.__forward_ref__===Ye}function Jh(e,n,t,r){throw new Error(`ASSERTION ERROR: ${e}`+(r==null?"":` [Expected=> ${t} ${r} ${n} <=Actual]`))}function E(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function _e(e){return{providers:e.providers||[],imports:e.imports||[]}}function go(e){return JE(e,vs)}function bu(e){return go(e)!==null}function JE(e,n){return e.hasOwnProperty(n)&&e[n]||null}function XE(e){let n=e?.[vs]??null;return n||null}function du(e){return e&&e.hasOwnProperty(hs)?e[hs]:null}var vs=W({\u0275prov:W}),hs=W({\u0275inj:W}),D=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(n,t){this._desc=n,this.\u0275prov=void 0,typeof t=="number"?this.__NG_ELEMENT_ID__=t:t!==void 0&&(this.\u0275prov=E({token:this,providedIn:t.providedIn||"root",factory:t.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function _u(e){return e&&!!e.\u0275providers}var Tu=W({\u0275cmp:W}),Su=W({\u0275dir:W}),Mu=W({\u0275pipe:W}),Au=W({\u0275mod:W}),fo=W({\u0275fac:W}),wn=W({__NG_ELEMENT_ID__:W}),Zh=W({__NG_ENV_ID__:W});function In(e){return typeof e=="string"?e:e==null?"":String(e)}function Xh(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():In(e)}function Ru(e,n){throw new C(-200,e)}function ys(e,n){throw new C(-201,!1)}var fu;function ep(){return fu}function Ne(e){let n=fu;return fu=e,n}function Nu(e,n,t){let r=go(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(t&8)return null;if(n!==void 0)return n;ys(e,"Injector")}var eC={},vn=eC,hu="__NG_DI_FLAG__",pu=class{injector;constructor(n){this.injector=n}retrieve(n,t){let r=yn(t)||0;try{return this.injector.get(n,r&8?null:vn,r)}catch(o){if(Qn(o))return o;throw o}}},ps="ngTempTokenPath",tC="ngTokenPath",nC=/\n/gm,rC="\u0275",Yh="__source";function oC(e,n=0){let t=Hc();if(t===void 0)throw new C(-203,!1);if(t===null)return Nu(e,void 0,n);{let r=iC(n),o=t.retrieve(e,r);if(Qn(o)){if(r.optional)return null;throw o}return o}}function I(e,n=0){return(ep()||oC)(de(e),n)}function p(e,n){return I(e,yn(n))}function yn(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function iC(e){return{optional:!!(e&8),host:!!(e&1),self:!!(e&2),skipSelf:!!(e&4)}}function gu(e){let n=[];for(let t=0;t<e.length;t++){let r=de(e[t]);if(Array.isArray(r)){if(r.length===0)throw new C(900,!1);let o,i=0;for(let s=0;s<r.length;s++){let a=r[s],c=sC(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}n.push(I(o,i))}else n.push(I(r))}return n}function xu(e,n){return e[hu]=n,e.prototype[hu]=n,e}function sC(e){return e[hu]}function aC(e,n,t,r){let o=e[ps];throw n[Yh]&&o.unshift(n[Yh]),e.message=cC(`
`+e.message,o,t,r),e[tC]=o,e[ps]=null,e}function cC(e,n,t,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==rC?e.slice(2):e;let o=be(n);if(Array.isArray(n))o=n.map(be).join(" -> ");else if(typeof n=="object"){let i=[];for(let s in n)if(n.hasOwnProperty(s)){let a=n[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):be(a)))}o=`{${i.join(", ")}}`}return`${t}${r?"("+r+")":""}[${o}]: ${e.replace(nC,`
  `)}`}function Dn(e,n){let t=e.hasOwnProperty(fo);return t?e[fo]:null}function tp(e,n,t){if(e.length!==n.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=n[r];if(t&&(o=t(o),i=t(i)),i!==o)return!1}return!0}function np(e){return e.flat(Number.POSITIVE_INFINITY)}function Ds(e,n){e.forEach(t=>Array.isArray(t)?Ds(t,n):n(t))}function Ou(e,n,t){n>=e.length?e.push(t):e.splice(n,0,t)}function mo(e,n){return n>=e.length-1?e.pop():e.splice(n,1)[0]}function rp(e,n){let t=[];for(let r=0;r<e;r++)t.push(n);return t}function op(e,n,t,r){let o=e.length;if(o==n)e.push(t,r);else if(o===1)e.push(r,e[0]),e[0]=t;else{for(o--,e.push(e[o-1],e[o]);o>n;){let i=o-2;e[o]=e[i],o--}e[n]=t,e[n+1]=r}}function ip(e,n,t){let r=gr(e,n);return r>=0?e[r|1]=t:(r=~r,op(e,r,n,t)),r}function Es(e,n){let t=gr(e,n);if(t>=0)return e[t|1]}function gr(e,n){return uC(e,n,1)}function uC(e,n,t){let r=0,o=e.length>>t;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<t];if(n===s)return i<<t;s>n?o=i:r=i+1}return~(o<<t)}var Ht={},xe=[],$t=new D(""),ku=new D("",-1),Pu=new D(""),ho=class{get(n,t=vn){if(t===vn)throw new Ni(`NullInjectorError: No provider for ${be(n)}!`);return t}};function Fu(e){return e[Au]||null}function at(e){return e[Tu]||null}function Lu(e){return e[Su]||null}function sp(e){return e[Mu]||null}function bn(e){return{\u0275providers:e}}function ap(...e){return{\u0275providers:ju(!0,e),\u0275fromNgModule:!0}}function ju(e,...n){let t=[],r=new Set,o,i=s=>{t.push(s)};return Ds(n,s=>{let a=s;gs(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&cp(o,i),t}function cp(e,n){for(let t=0;t<e.length;t++){let{ngModule:r,providers:o}=e[t];Vu(o,i=>{n(i,r)})}}function gs(e,n,t,r){if(e=de(e),!e)return!1;let o=null,i=du(e),s=!i&&at(e);if(!i&&!s){let c=e.ngModule;if(i=du(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let u of c)gs(u,n,t,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let u;try{Ds(i.imports,l=>{gs(l,n,t,r)&&(u||=[],u.push(l))})}finally{}u!==void 0&&cp(u,n)}if(!a){let u=Dn(o)||(()=>new o);n({provide:o,useFactory:u,deps:xe},o),n({provide:Pu,useValue:o,multi:!0},o),n({provide:$t,useValue:()=>I(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let u=e;Vu(c,l=>{n(l,u)})}}else return!1;return o!==e&&e.providers!==void 0}function Vu(e,n){for(let t of e)_u(t)&&(t=t.\u0275providers),Array.isArray(t)?Vu(t,n):n(t)}var lC=W({provide:String,useValue:W});function up(e){return e!==null&&typeof e=="object"&&lC in e}function dC(e){return!!(e&&e.useExisting)}function fC(e){return!!(e&&e.useFactory)}function En(e){return typeof e=="function"}function lp(e){return!!e.useClass}var vo=new D(""),fs={},Qh={},lu;function mr(){return lu===void 0&&(lu=new ho),lu}var z=class{},Cn=class extends z{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(n,t,r,o){super(),this.parent=t,this.source=r,this.scopes=o,vu(n,s=>this.processProvider(s)),this.records.set(ku,hr(void 0,this)),o.has("environment")&&this.records.set(z,hr(void 0,this));let i=this.records.get(vo);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(Pu,xe,{self:!0}))}retrieve(n,t){let r=yn(t)||0;try{return this.get(n,vn,r)}catch(o){if(Qn(o))return o;throw o}}destroy(){lo(this),this._destroyed=!0;let n=R(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let t=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of t)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),R(n)}}onDestroy(n){return lo(this),this._onDestroyHooks.push(n),()=>this.removeOnDestroy(n)}runInContext(n){lo(this);let t=wt(this),r=Ne(void 0),o;try{return n()}finally{wt(t),Ne(r)}}get(n,t=vn,r){if(lo(this),n.hasOwnProperty(Zh))return n[Zh](this);let o=yn(r),i,s=wt(this),a=Ne(void 0);try{if(!(o&4)){let u=this.records.get(n);if(u===void 0){let l=vC(n)&&go(n);l&&this.injectableDefInScope(l)?u=hr(mu(n),fs):u=null,this.records.set(n,u)}if(u!=null)return this.hydrate(n,u)}let c=o&2?mr():this.parent;return t=o&8&&t===vn?null:t,c.get(n,t)}catch(c){if(Qn(c)){if((c[ps]=c[ps]||[]).unshift(be(n)),s)throw c;return aC(c,n,"R3InjectorError",this.source)}else throw c}finally{Ne(a),wt(s)}}resolveInjectorInitializers(){let n=R(null),t=wt(this),r=Ne(void 0),o;try{let i=this.get($t,xe,{self:!0});for(let s of i)s()}finally{wt(t),Ne(r),R(n)}}toString(){let n=[],t=this.records;for(let r of t.keys())n.push(be(r));return`R3Injector[${n.join(", ")}]`}processProvider(n){n=de(n);let t=En(n)?n:de(n&&n.provide),r=pC(n);if(!En(n)&&n.multi===!0){let o=this.records.get(t);o||(o=hr(void 0,fs,!0),o.factory=()=>gu(o.multi),this.records.set(t,o)),t=n,o.multi.push(n)}this.records.set(t,r)}hydrate(n,t){let r=R(null);try{return t.value===Qh?Ru(be(n)):t.value===fs&&(t.value=Qh,t.value=t.factory()),typeof t.value=="object"&&t.value&&mC(t.value)&&this._ngOnDestroyHooks.add(t.value),t.value}finally{R(r)}}injectableDefInScope(n){if(!n.providedIn)return!1;let t=de(n.providedIn);return typeof t=="string"?t==="any"||this.scopes.has(t):this.injectorDefTypes.has(t)}removeOnDestroy(n){let t=this._onDestroyHooks.indexOf(n);t!==-1&&this._onDestroyHooks.splice(t,1)}};function mu(e){let n=go(e),t=n!==null?n.factory:Dn(e);if(t!==null)return t;if(e instanceof D)throw new C(204,!1);if(e instanceof Function)return hC(e);throw new C(204,!1)}function hC(e){if(e.length>0)throw new C(204,!1);let t=XE(e);return t!==null?()=>t.factory(e):()=>new e}function pC(e){if(up(e))return hr(void 0,e.useValue);{let n=Bu(e);return hr(n,fs)}}function Bu(e,n,t){let r;if(En(e)){let o=de(e);return Dn(o)||mu(o)}else if(up(e))r=()=>de(e.useValue);else if(fC(e))r=()=>e.useFactory(...gu(e.deps||[]));else if(dC(e))r=()=>I(de(e.useExisting));else{let o=de(e&&(e.useClass||e.provide));if(gC(e))r=()=>new o(...gu(e.deps));else return Dn(o)||mu(o)}return r}function lo(e){if(e.destroyed)throw new C(205,!1)}function hr(e,n,t=!1){return{factory:e,value:n,multi:t?[]:void 0}}function gC(e){return!!e.deps}function mC(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function vC(e){return typeof e=="function"||typeof e=="object"&&e.ngMetadataName==="InjectionToken"}function vu(e,n){for(let t of e)Array.isArray(t)?vu(t,n):t&&_u(t)?vu(t.\u0275providers,n):n(t)}function pe(e,n){let t;e instanceof Cn?(lo(e),t=e):t=new pu(e);let r,o=wt(t),i=Ne(void 0);try{return n()}finally{wt(o),Ne(i)}}function dp(){return ep()!==void 0||Hc()!=null}var Qe=0,x=1,S=2,ue=3,je=4,ve=5,vr=6,yr=7,ge=8,_n=9,ct=10,Y=11,Dr=12,Uu=13,Tn=14,Te=15,zt=16,Sn=17,ut=18,yo=19,Hu=20,Tt=21,Cs=22,Do=23,Oe=24,Mn=25,Se=26,fp=1;var Gt=7,Eo=8,An=9,ye=10;function lt(e){return Array.isArray(e)&&typeof e[fp]=="object"}function Ke(e){return Array.isArray(e)&&e[fp]===!0}function ws(e){return(e.flags&4)!==0}function Wt(e){return e.componentOffset>-1}function Co(e){return(e.flags&1)===1}function dt(e){return!!e.template}function Er(e){return(e[S]&512)!==0}function Rn(e){return(e[S]&256)===256}var hp="svg",pp="math";function Ve(e){for(;Array.isArray(e);)e=e[Qe];return e}function $u(e,n){return Ve(n[e])}function Je(e,n){return Ve(n[e.index])}function Is(e,n){return e.data[n]}function Be(e,n){let t=n[e];return lt(t)?t:t[Qe]}function gp(e){return(e[S]&4)===4}function bs(e){return(e[S]&128)===128}function mp(e){return Ke(e[ue])}function Nn(e,n){return n==null?null:e[n]}function zu(e){e[Sn]=0}function Gu(e){e[S]&1024||(e[S]|=1024,bs(e)&&Cr(e))}function vp(e,n){for(;e>0;)n=n[Tn],e--;return n}function wo(e){return!!(e[S]&9216||e[Oe]?.dirty)}function _s(e){e[ct].changeDetectionScheduler?.notify(8),e[S]&64&&(e[S]|=1024),wo(e)&&Cr(e)}function Cr(e){e[ct].changeDetectionScheduler?.notify(0);let n=Ut(e);for(;n!==null&&!(n[S]&8192||(n[S]|=8192,!bs(n)));)n=Ut(n)}function Wu(e,n){if(Rn(e))throw new C(911,!1);e[Tt]===null&&(e[Tt]=[]),e[Tt].push(n)}function yp(e,n){if(e[Tt]===null)return;let t=e[Tt].indexOf(n);t!==-1&&e[Tt].splice(t,1)}function Ut(e){let n=e[ue];return Ke(n)?n[ue]:n}function qu(e){return e[yr]??=[]}function Zu(e){return e.cleanup??=[]}function Dp(e,n,t,r){let o=qu(n);o.push(t),e.firstCreatePass&&Zu(e).push(r,o.length-1)}var k={lFrame:Fp(null),bindingsEnabled:!0,skipHydrationRootTNode:null},Io=function(e){return e[e.Off=0]="Off",e[e.Exhaustive=1]="Exhaustive",e[e.OnlyDirtyViews=2]="OnlyDirtyViews",e}(Io||{}),yC=0,yu=!1;function Ep(){return k.lFrame.elementDepthCount}function Cp(){k.lFrame.elementDepthCount++}function wp(){k.lFrame.elementDepthCount--}function Ts(){return k.bindingsEnabled}function Yu(){return k.skipHydrationRootTNode!==null}function Ip(e){return k.skipHydrationRootTNode===e}function bp(){k.skipHydrationRootTNode=null}function L(){return k.lFrame.lView}function ne(){return k.lFrame.tView}function _p(e){return k.lFrame.contextLView=e,e[ge]}function Tp(e){return k.lFrame.contextLView=null,e}function le(){let e=Qu();for(;e!==null&&e.type===64;)e=e.parent;return e}function Qu(){return k.lFrame.currentTNode}function Sp(){let e=k.lFrame,n=e.currentTNode;return e.isParent?n:n.parent}function qt(e,n){let t=k.lFrame;t.currentTNode=e,t.isParent=n}function Ss(){return k.lFrame.isParent}function Ms(){k.lFrame.isParent=!1}function Ku(e){Jh("Must never be called in production mode"),yC=e}function Ju(){return yu}function Xu(e){let n=yu;return yu=e,n}function Mp(){let e=k.lFrame,n=e.bindingRootIndex;return n===-1&&(n=e.bindingRootIndex=e.tView.bindingStartIndex),n}function Ap(){return k.lFrame.bindingIndex}function Rp(e){return k.lFrame.bindingIndex=e}function bo(){return k.lFrame.bindingIndex++}function el(e){let n=k.lFrame,t=n.bindingIndex;return n.bindingIndex=n.bindingIndex+e,t}function Np(){return k.lFrame.inI18n}function xp(e,n){let t=k.lFrame;t.bindingIndex=t.bindingRootIndex=e,As(n)}function Op(){return k.lFrame.currentDirectiveIndex}function As(e){k.lFrame.currentDirectiveIndex=e}function kp(e){let n=k.lFrame.currentDirectiveIndex;return n===-1?null:e[n]}function tl(){return k.lFrame.currentQueryIndex}function Rs(e){k.lFrame.currentQueryIndex=e}function DC(e){let n=e[x];return n.type===2?n.declTNode:n.type===1?e[ve]:null}function nl(e,n,t){if(t&4){let o=n,i=e;for(;o=o.parent,o===null&&!(t&1);)if(o=DC(i),o===null||(i=i[Tn],o.type&10))break;if(o===null)return!1;n=o,e=i}let r=k.lFrame=Pp();return r.currentTNode=n,r.lView=e,!0}function Ns(e){let n=Pp(),t=e[x];k.lFrame=n,n.currentTNode=t.firstChild,n.lView=e,n.tView=t,n.contextLView=e,n.bindingIndex=t.bindingStartIndex,n.inI18n=!1}function Pp(){let e=k.lFrame,n=e===null?null:e.child;return n===null?Fp(e):n}function Fp(e){let n={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=n),n}function Lp(){let e=k.lFrame;return k.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var rl=Lp;function xs(){let e=Lp();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function jp(e){return(k.lFrame.contextLView=vp(e,k.lFrame.contextLView))[ge]}function Zt(){return k.lFrame.selectedIndex}function Yt(e){k.lFrame.selectedIndex=e}function Os(){let e=k.lFrame;return Is(e.tView,e.selectedIndex)}function Vp(){return k.lFrame.currentNamespace}var Bp=!0;function _o(){return Bp}function To(e){Bp=e}function Du(e,n=null,t=null,r){let o=ol(e,n,t,r);return o.resolveInjectorInitializers(),o}function ol(e,n=null,t=null,r,o=new Set){let i=[t||xe,ap(e)];return r=r||(typeof e=="object"?void 0:be(e)),new Cn(i,n||mr(),r||null,o)}var se=class e{static THROW_IF_NOT_FOUND=vn;static NULL=new ho;static create(n,t){if(Array.isArray(n))return Du({name:""},t,n,"");{let r=n.name??"";return Du({name:r},n.parent,n.providers,r)}}static \u0275prov=E({token:e,providedIn:"any",factory:()=>I(ku)});static __NG_ELEMENT_ID__=-1},q=new D(""),Qt=(()=>{class e{static __NG_ELEMENT_ID__=EC;static __NG_ENV_ID__=t=>t}return e})(),Eu=class extends Qt{_lView;constructor(n){super(),this._lView=n}get destroyed(){return Rn(this._lView)}onDestroy(n){let t=this._lView;return Wu(t,n),()=>yp(t,n)}};function EC(){return new Eu(L())}var Ze=class{_console=console;handleError(n){this._console.error("ERROR",n)}},Me=new D("",{providedIn:"root",factory:()=>{let e=p(z),n;return t=>{e.destroyed&&!n?setTimeout(()=>{throw t}):(n??=e.get(Ze),n.handleError(t))}}}),Up={provide:$t,useValue:()=>void p(Ze),multi:!0};function il(e){return typeof e=="function"&&e[he]!==void 0}function Mt(e,n){let[t,r,o]=qc(e,n?.equal),i=t,s=i[he];return i.set=r,i.update=o,i.asReadonly=Hp.bind(i),i}function Hp(){let e=this[he];if(e.readonlyFn===void 0){let n=()=>this();n[he]=e,e.readonlyFn=n}return e.readonlyFn}function sl(e){return il(e)&&typeof e.set=="function"}var St=class{},ks=new D("",{providedIn:"root",factory:()=>!1});var al=new D(""),cl=new D("");var Ps=(()=>{class e{view;node;constructor(t,r){this.view=t,this.node=r}static __NG_ELEMENT_ID__=CC}return e})();function CC(){return new Ps(L(),le())}var At=(()=>{class e{taskId=0;pendingTasks=new Set;destroyed=!1;pendingTask=new te(!1);get hasPendingTasks(){return this.destroyed?!1:this.pendingTask.value}get hasPendingTasksObservable(){return this.destroyed?new O(t=>{t.next(!1),t.complete()}):this.pendingTask}add(){!this.hasPendingTasks&&!this.destroyed&&this.pendingTask.next(!0);let t=this.taskId++;return this.pendingTasks.add(t),t}has(t){return this.pendingTasks.has(t)}remove(t){this.pendingTasks.delete(t),this.pendingTasks.size===0&&this.hasPendingTasks&&this.pendingTask.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this.hasPendingTasks&&this.pendingTask.next(!1),this.destroyed=!0,this.pendingTask.unsubscribe()}static \u0275prov=E({token:e,providedIn:"root",factory:()=>new e})}return e})(),So=(()=>{class e{internalPendingTasks=p(At);scheduler=p(St);errorHandler=p(Me);add(){let t=this.internalPendingTasks.add();return()=>{this.internalPendingTasks.has(t)&&(this.scheduler.notify(11),this.internalPendingTasks.remove(t))}}run(t){let r=this.add();t().catch(this.errorHandler).finally(r)}static \u0275prov=E({token:e,providedIn:"root",factory:()=>new e})}return e})();function Mo(...e){}var ul=(()=>{class e{static \u0275prov=E({token:e,providedIn:"root",factory:()=>new Cu})}return e})(),Cu=class{dirtyEffectCount=0;queues=new Map;add(n){this.enqueue(n),this.schedule(n)}schedule(n){n.dirty&&this.dirtyEffectCount++}remove(n){let t=n.zone,r=this.queues.get(t);r.has(n)&&(r.delete(n),n.dirty&&this.dirtyEffectCount--)}enqueue(n){let t=n.zone;this.queues.has(t)||this.queues.set(t,new Set);let r=this.queues.get(t);r.has(n)||r.add(n)}flush(){for(;this.dirtyEffectCount>0;){let n=!1;for(let[t,r]of this.queues)t===null?n||=this.flushQueue(r):n||=t.run(()=>this.flushQueue(r));n||(this.dirtyEffectCount=0)}}flushQueue(n){let t=!1;for(let r of n)r.dirty&&(this.dirtyEffectCount--,t=!0,r.run());return t}};function Tr(e){return{toString:e}.toString()}var Fs="__parameters__";function RC(e){return function(...t){if(e){let r=e(...t);for(let o in r)this[o]=r[o]}}}function vg(e,n,t){return Tr(()=>{let r=RC(n);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(c,u,l){let d=c.hasOwnProperty(Fs)?c[Fs]:Object.defineProperty(c,Fs,{value:[]})[Fs];for(;d.length<=l;)d.push(null);return(d[l]=d[l]||[]).push(s),c}}return o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var yg=xu(vg("Optional"),8);var Dg=xu(vg("SkipSelf"),4);function NC(e){return typeof e=="function"}var $s=class{previousValue;currentValue;firstChange;constructor(n,t,r){this.previousValue=n,this.currentValue=t,this.firstChange=r}isFirstChange(){return this.firstChange}};function Eg(e,n,t,r){n!==null?n.applyValueToInputSignal(n,r):e[t]=r}var De=(()=>{let e=()=>Cg;return e.ngInherit=!0,e})();function Cg(e){return e.type.prototype.ngOnChanges&&(e.setInput=OC),xC}function xC(){let e=Ig(this),n=e?.current;if(n){let t=e.previous;if(t===Ht)e.previous=n;else for(let r in n)t[r]=n[r];e.current=null,this.ngOnChanges(n)}}function OC(e,n,t,r,o){let i=this.declaredInputs[r],s=Ig(e)||kC(e,{previous:Ht,current:null}),a=s.current||(s.current={}),c=s.previous,u=c[i];a[i]=new $s(u&&u.currentValue,t,c===Ht),Eg(e,n,o,t)}var wg="__ngSimpleChanges__";function Ig(e){return e[wg]||null}function kC(e,n){return e[wg]=n}var $p=[];var U=function(e,n=null,t){for(let r=0;r<$p.length;r++){let o=$p[r];o(e,n,t)}};function PC(e,n,t){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=n.type.prototype;if(r){let s=Cg(n);(t.preOrderHooks??=[]).push(e,s),(t.preOrderCheckHooks??=[]).push(e,s)}o&&(t.preOrderHooks??=[]).push(0-e,o),i&&((t.preOrderHooks??=[]).push(e,i),(t.preOrderCheckHooks??=[]).push(e,i))}function ql(e,n){for(let t=n.directiveStart,r=n.directiveEnd;t<r;t++){let i=e.data[t].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:u,ngOnDestroy:l}=i;s&&(e.contentHooks??=[]).push(-t,s),a&&((e.contentHooks??=[]).push(t,a),(e.contentCheckHooks??=[]).push(t,a)),c&&(e.viewHooks??=[]).push(-t,c),u&&((e.viewHooks??=[]).push(t,u),(e.viewCheckHooks??=[]).push(t,u)),l!=null&&(e.destroyHooks??=[]).push(t,l)}}function Vs(e,n,t){bg(e,n,3,t)}function Bs(e,n,t,r){(e[S]&3)===t&&bg(e,n,t,r)}function ll(e,n){let t=e[S];(t&3)===n&&(t&=16383,t+=1,e[S]=t)}function bg(e,n,t,r){let o=r!==void 0?e[Sn]&65535:0,i=r??-1,s=n.length-1,a=0;for(let c=o;c<s;c++)if(typeof n[c+1]=="number"){if(a=n[c],r!=null&&a>=r)break}else n[c]<0&&(e[Sn]+=65536),(a<i||i==-1)&&(FC(e,t,n,c),e[Sn]=(e[Sn]&**********)+c+2),c++}function zp(e,n){U(4,e,n);let t=R(null);try{n.call(e)}finally{R(t),U(5,e,n)}}function FC(e,n,t,r){let o=t[r]<0,i=t[r+1],s=o?-t[r]:t[r],a=e[s];o?e[S]>>14<e[Sn]>>16&&(e[S]&3)===n&&(e[S]+=16384,zp(a,i)):zp(a,i)}var Ir=-1,On=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(n,t,r){this.factory=n,this.canSeeViewProviders=t,this.injectImpl=r}};function LC(e){return(e.flags&8)!==0}function jC(e){return(e.flags&16)!==0}function VC(e,n,t){let r=0;for(;r<t.length;){let o=t[r];if(typeof o=="number"){if(o!==0)break;r++;let i=t[r++],s=t[r++],a=t[r++];e.setAttribute(n,s,a,i)}else{let i=o,s=t[++r];BC(i)?e.setProperty(n,i,s):e.setAttribute(n,i,s),r++}}return r}function _g(e){return e===3||e===4||e===6}function BC(e){return e.charCodeAt(0)===64}function br(e,n){if(!(n===null||n.length===0))if(e===null||e.length===0)e=n.slice();else{let t=-1;for(let r=0;r<n.length;r++){let o=n[r];typeof o=="number"?t=o:t===0||(t===-1||t===2?Gp(e,t,o,null,n[++r]):Gp(e,t,o,null,null))}}return e}function Gp(e,n,t,r,o){let i=0,s=e.length;if(n===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===n){s=-1;break}else if(a>n){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===t){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,n),i=s+1),e.splice(i++,0,t),o!==null&&e.splice(i++,0,o)}function Tg(e){return e!==Ir}function zs(e){return e&32767}function UC(e){return e>>16}function Gs(e,n){let t=UC(e),r=n;for(;t>0;)r=r[Tn],t--;return r}var El=!0;function Wp(e){let n=El;return El=e,n}var HC=256,Sg=HC-1,Mg=5,$C=0,ft={};function zC(e,n,t){let r;typeof t=="string"?r=t.charCodeAt(0)||0:t.hasOwnProperty(wn)&&(r=t[wn]),r==null&&(r=t[wn]=$C++);let o=r&Sg,i=1<<o;n.data[e+(o>>Mg)]|=i}function Ws(e,n){let t=Ag(e,n);if(t!==-1)return t;let r=n[x];r.firstCreatePass&&(e.injectorIndex=n.length,dl(r.data,e),dl(n,null),dl(r.blueprint,null));let o=Zl(e,n),i=e.injectorIndex;if(Tg(o)){let s=zs(o),a=Gs(o,n),c=a[x].data;for(let u=0;u<8;u++)n[i+u]=a[s+u]|c[s+u]}return n[i+8]=o,i}function dl(e,n){e.push(0,0,0,0,0,0,0,0,n)}function Ag(e,n){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||n[e.injectorIndex+8]===null?-1:e.injectorIndex}function Zl(e,n){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let t=0,r=null,o=n;for(;o!==null;){if(r=kg(o),r===null)return Ir;if(t++,o=o[Tn],r.injectorIndex!==-1)return r.injectorIndex|t<<16}return Ir}function Cl(e,n,t){zC(e,n,t)}function GC(e,n){if(n==="class")return e.classes;if(n==="style")return e.styles;let t=e.attrs;if(t){let r=t.length,o=0;for(;o<r;){let i=t[o];if(_g(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof t[o]=="string";)o++;else{if(i===n)return t[o+1];o=o+2}}}return null}function Rg(e,n,t){if(t&8||e!==void 0)return e;ys(n,"NodeInjector")}function Ng(e,n,t,r){if(t&8&&r===void 0&&(r=null),(t&3)===0){let o=e[_n],i=Ne(void 0);try{return o?o.get(n,r,t&8):Nu(n,r,t&8)}finally{Ne(i)}}return Rg(r,n,t)}function xg(e,n,t,r=0,o){if(e!==null){if(n[S]&2048&&!(r&2)){let s=YC(e,n,t,r,ft);if(s!==ft)return s}let i=Og(e,n,t,r,ft);if(i!==ft)return i}return Ng(n,t,r,o)}function Og(e,n,t,r,o){let i=qC(t);if(typeof i=="function"){if(!nl(n,e,r))return r&1?Rg(o,t,r):Ng(n,t,r,o);try{let s;if(s=i(r),s==null&&!(r&8))ys(t);else return s}finally{rl()}}else if(typeof i=="number"){let s=null,a=Ag(e,n),c=Ir,u=r&1?n[Te][ve]:null;for((a===-1||r&4)&&(c=a===-1?Zl(e,n):n[a+8],c===Ir||!Zp(r,!1)?a=-1:(s=n[x],a=zs(c),n=Gs(c,n)));a!==-1;){let l=n[x];if(qp(i,a,l.data)){let d=WC(a,n,t,s,r,u);if(d!==ft)return d}c=n[a+8],c!==Ir&&Zp(r,n[x].data[a+8]===u)&&qp(i,a,n)?(s=l,a=zs(c),n=Gs(c,n)):a=-1}}return o}function WC(e,n,t,r,o,i){let s=n[x],a=s.data[e+8],c=r==null?Wt(a)&&El:r!=s&&(a.type&3)!==0,u=o&1&&i===a,l=Us(a,s,t,c,u);return l!==null?No(n,s,l,a):ft}function Us(e,n,t,r,o){let i=e.providerIndexes,s=n.data,a=i&1048575,c=e.directiveStart,u=e.directiveEnd,l=i>>20,d=r?a:a+l,h=o?a+l:u;for(let f=d;f<h;f++){let m=s[f];if(f<c&&t===m||f>=c&&m.type===t)return f}if(o){let f=s[c];if(f&&dt(f)&&f.type===t)return c}return null}function No(e,n,t,r){let o=e[t],i=n.data;if(o instanceof On){let s=o;s.resolving&&Ru(Xh(i[t]));let a=Wp(s.canSeeViewProviders);s.resolving=!0;let c=i[t].type||i[t],u,l=s.injectImpl?Ne(s.injectImpl):null,d=nl(e,r,0);try{o=e[t]=s.factory(void 0,i,e,r),n.firstCreatePass&&t>=r.directiveStart&&PC(t,i[t],n)}finally{l!==null&&Ne(l),Wp(a),s.resolving=!1,rl()}}return o}function qC(e){if(typeof e=="string")return e.charCodeAt(0)||0;let n=e.hasOwnProperty(wn)?e[wn]:void 0;return typeof n=="number"?n>=0?n&Sg:ZC:n}function qp(e,n,t){let r=1<<e;return!!(t[n+(e>>Mg)]&r)}function Zp(e,n){return!(e&2)&&!(e&1&&n)}var xn=class{_tNode;_lView;constructor(n,t){this._tNode=n,this._lView=t}get(n,t,r){return xg(this._tNode,this._lView,n,yn(r),t)}};function ZC(){return new xn(le(),L())}function Xt(e){return Tr(()=>{let n=e.prototype.constructor,t=n[fo]||wl(n),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[fo]||wl(o);if(i&&i!==t)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function wl(e){return Iu(e)?()=>{let n=wl(de(e));return n&&n()}:Dn(e)}function YC(e,n,t,r,o){let i=e,s=n;for(;i!==null&&s!==null&&s[S]&2048&&!Er(s);){let a=Og(i,s,t,r|2,ft);if(a!==ft)return a;let c=i.parent;if(!c){let u=s[Hu];if(u){let l=u.get(t,ft,r);if(l!==ft)return l}c=kg(s),s=s[Tn]}i=c}return o}function kg(e){let n=e[x],t=n.type;return t===2?n.declTNode:t===1?e[ve]:null}function en(e){return GC(le(),e)}function QC(){return Sr(le(),L())}function Sr(e,n){return new Q(Je(e,n))}var Q=(()=>{class e{nativeElement;constructor(t){this.nativeElement=t}static __NG_ELEMENT_ID__=QC}return e})();function KC(e){return e instanceof Q?e.nativeElement:e}function JC(){return this._results[Symbol.iterator]()}var qs=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new H}constructor(n=!1){this._emitDistinctChangesOnly=n}get(n){return this._results[n]}map(n){return this._results.map(n)}filter(n){return this._results.filter(n)}find(n){return this._results.find(n)}reduce(n,t){return this._results.reduce(n,t)}forEach(n){this._results.forEach(n)}some(n){return this._results.some(n)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(n,t){this.dirty=!1;let r=np(n);(this._changesDetected=!tp(this._results,r,t))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(n){this._onDirty=n}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=JC};function Pg(e){return(e.flags&128)===128}var Yl=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Yl||{}),Fg=new Map,XC=0;function ew(){return XC++}function tw(e){Fg.set(e[yo],e)}function Il(e){Fg.delete(e[yo])}var Yp="__ngContext__";function Mr(e,n){lt(n)?(e[Yp]=n[yo],tw(n)):e[Yp]=n}function Lg(e){return Vg(e[Dr])}function jg(e){return Vg(e[je])}function Vg(e){for(;e!==null&&!Ke(e);)e=e[je];return e}var bl;function Ql(e){bl=e}function Bg(){if(bl!==void 0)return bl;if(typeof document<"u")return document;throw new C(210,!1)}var sa=new D("",{providedIn:"root",factory:()=>nw}),nw="ng",aa=new D(""),Ar=new D("",{providedIn:"platform",factory:()=>"unknown"});var ca=new D("",{providedIn:"root",factory:()=>Bg().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var rw="h",ow="b";var Ug=!1,Hg=new D("",{providedIn:"root",factory:()=>Ug});var iw=(e,n,t,r)=>{};function sw(e,n,t,r){iw(e,n,t,r)}var aw=()=>null;function $g(e,n,t=!1){return aw(e,n,t)}function zg(e,n){let t=e.contentQueries;if(t!==null){let r=R(null);try{for(let o=0;o<t.length;o+=2){let i=t[o],s=t[o+1];if(s!==-1){let a=e.data[s];Rs(i),a.contentQueries(2,n[s],s)}}}finally{R(r)}}}function _l(e,n,t){Rs(0);let r=R(null);try{n(e,t)}finally{R(r)}}function Kl(e,n,t){if(ws(n)){let r=R(null);try{let o=n.directiveStart,i=n.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=t[s];a.contentQueries(1,c,s)}}}finally{R(r)}}}var Rt=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(Rt||{});var Ls;function cw(){if(Ls===void 0&&(Ls=null,po.trustedTypes))try{Ls=po.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return Ls}function Qp(e){return cw()?.createScriptURL(e)||e}var Zs=class{changingThisBreaksApplicationSecurity;constructor(n){this.changingThisBreaksApplicationSecurity=n}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${ms})`}};function Po(e){return e instanceof Zs?e.changingThisBreaksApplicationSecurity:e}function Jl(e,n){let t=Gg(e);if(t!=null&&t!==n){if(t==="ResourceURL"&&n==="URL")return!0;throw new Error(`Required a safe ${n}, got a ${t} (see ${ms})`)}return t===n}function Gg(e){return e instanceof Zs&&e.getTypeName()||null}var uw=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function Wg(e){return e=String(e),e.match(uw)?e:"unsafe:"+e}var ua=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(ua||{});function qg(e){let n=Yg();return n?n.sanitize(ua.URL,e)||"":Jl(e,"URL")?Po(e):Wg(In(e))}function Zg(e){let n=Yg();if(n)return Qp(n.sanitize(ua.RESOURCE_URL,e)||"");if(Jl(e,"ResourceURL"))return Qp(Po(e));throw new C(904,!1)}function lw(e,n){return n==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||n==="href"&&(e==="base"||e==="link")?Zg:qg}function Xl(e,n,t){return lw(n,t)(e)}function Yg(){let e=L();return e&&e[ct].sanitizer}var dw=/^>|^->|<!--|-->|--!>|<!-$/g,fw=/(<|>)/g,hw="\u200B$1\u200B";function pw(e){return e.replace(dw,n=>n.replace(fw,hw))}function gw(e){return e.ownerDocument.defaultView}function mw(e){return e.ownerDocument}function Qg(e){return e instanceof Function?e():e}function vw(e,n,t){let r=e.length;for(;;){let o=e.indexOf(n,t);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=n.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}t=o+1}}var Kg="ng-template";function yw(e,n,t,r){let o=0;if(r){for(;o<n.length&&typeof n[o]=="string";o+=2)if(n[o]==="class"&&vw(n[o+1].toLowerCase(),t,0)!==-1)return!0}else if(ed(e))return!1;if(o=n.indexOf(1,o),o>-1){let i;for(;++o<n.length&&typeof(i=n[o])=="string";)if(i.toLowerCase()===t)return!0}return!1}function ed(e){return e.type===4&&e.value!==Kg}function Dw(e,n,t){let r=e.type===4&&!t?Kg:e.value;return n===r}function Ew(e,n,t){let r=4,o=e.attrs,i=o!==null?Iw(o):0,s=!1;for(let a=0;a<n.length;a++){let c=n[a];if(typeof c=="number"){if(!s&&!Xe(r)&&!Xe(c))return!1;if(s&&Xe(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!Dw(e,c,t)||c===""&&n.length===1){if(Xe(r))return!1;s=!0}}else if(r&8){if(o===null||!yw(e,o,c,t)){if(Xe(r))return!1;s=!0}}else{let u=n[++a],l=Cw(c,o,ed(e),t);if(l===-1){if(Xe(r))return!1;s=!0;continue}if(u!==""){let d;if(l>i?d="":d=o[l+1].toLowerCase(),r&2&&u!==d){if(Xe(r))return!1;s=!0}}}}return Xe(r)||s}function Xe(e){return(e&1)===0}function Cw(e,n,t,r){if(n===null)return-1;let o=0;if(r||!t){let i=!1;for(;o<n.length;){let s=n[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=n[++o];for(;typeof a=="string";)a=n[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return bw(n,e)}function Jg(e,n,t=!1){for(let r=0;r<n.length;r++)if(Ew(e,n[r],t))return!0;return!1}function ww(e){let n=e.attrs;if(n!=null){let t=n.indexOf(5);if((t&1)===0)return n[t+1]}return null}function Iw(e){for(let n=0;n<e.length;n++){let t=e[n];if(_g(t))return n}return e.length}function bw(e,n){let t=e.indexOf(4);if(t>-1)for(t++;t<e.length;){let r=e[t];if(typeof r=="number")return-1;if(r===n)return t;t++}return-1}function _w(e,n){e:for(let t=0;t<n.length;t++){let r=n[t];if(e.length===r.length){for(let o=0;o<e.length;o++)if(e[o]!==r[o])continue e;return!0}}return!1}function Kp(e,n){return e?":not("+n.trim()+")":n}function Tw(e){let n=e[0],t=1,r=2,o="",i=!1;for(;t<e.length;){let s=e[t];if(typeof s=="string")if(r&2){let a=e[++t];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!Xe(s)&&(n+=Kp(i,o),o=""),r=s,i=i||!Xe(r);t++}return o!==""&&(n+=Kp(i,o)),n}function Sw(e){return e.map(Tw).join(",")}function Mw(e){let n=[],t=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&n.push(i,e[++r]):o===8&&t.push(i);else{if(!Xe(o))break;o=i}r++}return t.length&&n.push(1,...t),n}var tt={};function Aw(e,n){return e.createText(n)}function Rw(e,n,t){e.setValue(n,t)}function Nw(e,n){return e.createComment(pw(n))}function Xg(e,n,t){return e.createElement(n,t)}function Ys(e,n,t,r,o){e.insertBefore(n,t,r,o)}function em(e,n,t){e.appendChild(n,t)}function Jp(e,n,t,r,o){r!==null?Ys(e,n,t,r,o):em(e,n,t)}function xw(e,n,t){e.removeChild(null,n,t)}function Ow(e,n,t){e.setAttribute(n,"style",t)}function kw(e,n,t){t===""?e.removeAttribute(n,"class"):e.setAttribute(n,"class",t)}function tm(e,n,t){let{mergedAttrs:r,classes:o,styles:i}=t;r!==null&&VC(e,n,r),o!==null&&kw(e,n,o),i!==null&&Ow(e,n,i)}function td(e,n,t,r,o,i,s,a,c,u,l){let d=Se+r,h=d+o,f=Pw(d,h),m=typeof u=="function"?u():u;return f[x]={type:e,blueprint:f,template:t,queries:null,viewQuery:a,declTNode:n,data:f.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:h,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:m,incompleteFirstPass:!1,ssrId:l}}function Pw(e,n){let t=[];for(let r=0;r<n;r++)t.push(r<e?null:tt);return t}function Fw(e){let n=e.tView;return n===null||n.incompleteFirstPass?e.tView=td(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):n}function nd(e,n,t,r,o,i,s,a,c,u,l){let d=n.blueprint.slice();return d[Qe]=o,d[S]=r|4|128|8|64|1024,(u!==null||e&&e[S]&2048)&&(d[S]|=2048),zu(d),d[ue]=d[Tn]=e,d[ge]=t,d[ct]=s||e&&e[ct],d[Y]=a||e&&e[Y],d[_n]=c||e&&e[_n]||null,d[ve]=i,d[yo]=ew(),d[vr]=l,d[Hu]=u,d[Te]=n.type==2?e[Te]:d,d}function Lw(e,n,t){let r=Je(n,e),o=Fw(t),i=e[ct].rendererFactory,s=rd(e,nd(e,o,null,nm(t),r,n,null,i.createRenderer(r,t),null,null,null));return e[n.index]=s}function nm(e){let n=16;return e.signals?n=4096:e.onPush&&(n=64),n}function rm(e,n,t,r){if(t===0)return-1;let o=n.length;for(let i=0;i<t;i++)n.push(r),e.blueprint.push(r),e.data.push(null);return o}function rd(e,n){return e[Dr]?e[Uu][je]=n:e[Dr]=n,e[Uu]=n,n}function jw(e=1){om(ne(),L(),Zt()+e,!1)}function om(e,n,t,r){if(!r)if((n[S]&3)===3){let i=e.preOrderCheckHooks;i!==null&&Vs(n,i,t)}else{let i=e.preOrderHooks;i!==null&&Bs(n,i,0,t)}Yt(t)}var la=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(la||{});function Tl(e,n,t,r){let o=R(null);try{let[i,s,a]=e.inputs[t],c=null;(s&la.SignalBased)!==0&&(c=n[i][he]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(n,r)),e.setInput!==null?e.setInput(n,c,r,t,i):Eg(n,c,i,r)}finally{R(o)}}function im(e,n,t,r,o){let i=Zt(),s=r&2;try{Yt(-1),s&&n.length>Se&&om(e,n,Se,!1),U(s?2:0,o,t),t(r,o)}finally{Yt(i),U(s?3:1,o,t)}}function da(e,n,t){Gw(e,n,t),(t.flags&64)===64&&Ww(e,n,t)}function od(e,n,t=Je){let r=n.localNames;if(r!==null){let o=n.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?t(n,e):e[s];e[o++]=a}}}function Vw(e,n,t,r){let i=r.get(Hg,Ug)||t===Rt.ShadowDom,s=e.selectRootElement(n,i);return Bw(s),s}function Bw(e){Uw(e)}var Uw=()=>null;function Hw(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function sm(e,n,t,r,o,i){let s=n[x];if(sd(e,s,n,t,r)){Wt(e)&&zw(n,e.index);return}$w(e,n,t,r,o,i)}function $w(e,n,t,r,o,i){if(e.type&3){let s=Je(e,n);t=Hw(t),r=i!=null?i(r,e.value||"",t):r,o.setProperty(s,t,r)}else e.type&12}function zw(e,n){let t=Be(n,e);t[S]&16||(t[S]|=64)}function Gw(e,n,t){let r=t.directiveStart,o=t.directiveEnd;Wt(t)&&Lw(n,t,e.data[r+t.componentOffset]),e.firstCreatePass||Ws(t,n);let i=t.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=No(n,e,s,t);if(Mr(c,n),i!==null&&Qw(n,s-r,c,a,t,i),dt(a)){let u=Be(t.index,n);u[ge]=No(n,e,s,t)}}}function Ww(e,n,t){let r=t.directiveStart,o=t.directiveEnd,i=t.index,s=Op();try{Yt(i);for(let a=r;a<o;a++){let c=e.data[a],u=n[a];As(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&qw(c,u)}}finally{Yt(-1),As(s)}}function qw(e,n){e.hostBindings!==null&&e.hostBindings(1,n)}function id(e,n){let t=e.directiveRegistry,r=null;if(t)for(let o=0;o<t.length;o++){let i=t[o];Jg(n,i.selectors,!1)&&(r??=[],dt(i)?r.unshift(i):r.push(i))}return r}function Zw(e,n,t,r,o,i){let s=Je(e,n);Yw(n[Y],s,i,e.value,t,r,o)}function Yw(e,n,t,r,o,i,s){if(i==null)e.removeAttribute(n,o,t);else{let a=s==null?In(i):s(i,r||"",o);e.setAttribute(n,o,a,t)}}function Qw(e,n,t,r,o,i){let s=i[n];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],u=s[a+1];Tl(r,t,c,u)}}function Kw(e,n){let t=e[_n];if(!t)return;t.get(Me,null)?.(n)}function sd(e,n,t,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let u=s[c],l=s[c+1],d=n.data[u];Tl(d,t[u],l,o),a=!0}if(i)for(let c of i){let u=t[c],l=n.data[c];Tl(l,u,r,o),a=!0}return a}function Jw(e,n){let t=Be(n,e),r=t[x];Xw(r,t);let o=t[Qe];o!==null&&t[vr]===null&&(t[vr]=$g(o,t[_n])),U(18),ad(r,t,t[ge]),U(19,t[ge])}function Xw(e,n){for(let t=n.length;t<e.blueprint.length;t++)n.push(e.blueprint[t])}function ad(e,n,t){Ns(n);try{let r=e.viewQuery;r!==null&&_l(1,r,t);let o=e.template;o!==null&&im(e,n,o,1,t),e.firstCreatePass&&(e.firstCreatePass=!1),n[ut]?.finishViewCreation(e),e.staticContentQueries&&zg(e,n),e.staticViewQueries&&_l(2,e.viewQuery,t);let i=e.components;i!==null&&eI(n,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{n[S]&=-5,xs()}}function eI(e,n){for(let t=0;t<n.length;t++)Jw(e,n[t])}function am(e,n,t,r){let o=R(null);try{let i=n.tView,a=e[S]&4096?4096:16,c=nd(e,i,t,a,null,n,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),u=e[n.index];c[zt]=u;let l=e[ut];return l!==null&&(c[ut]=l.createEmbeddedView(i)),ad(i,c,t),c}finally{R(o)}}function Sl(e,n){return!n||n.firstChild===null||Pg(e)}var Xp=!1,tI=new D(""),nI;function cd(e,n){return nI(e,n)}var ht=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(ht||{});function fa(e){return(e.flags&32)===32}function wr(e,n,t,r,o){if(r!=null){let i,s=!1;Ke(r)?i=r:lt(r)&&(s=!0,r=r[Qe]);let a=Ve(r);e===0&&t!==null?o==null?em(n,t,a):Ys(n,t,a,o||null,!0):e===1&&t!==null?Ys(n,t,a,o||null,!0):e===2?xw(n,a,s):e===3&&n.destroyNode(a),i!=null&&fI(n,e,i,t,o)}}function rI(e,n){cm(e,n),n[Qe]=null,n[ve]=null}function oI(e,n,t,r,o,i){r[Qe]=o,r[ve]=n,pa(e,r,t,1,o,i)}function cm(e,n){n[ct].changeDetectionScheduler?.notify(9),pa(e,n,n[Y],2,null,null)}function iI(e){let n=e[Dr];if(!n)return fl(e[x],e);for(;n;){let t=null;if(lt(n))t=n[Dr];else{let r=n[ye];r&&(t=r)}if(!t){for(;n&&!n[je]&&n!==e;)lt(n)&&fl(n[x],n),n=n[ue];n===null&&(n=e),lt(n)&&fl(n[x],n),t=n&&n[je]}n=t}}function ud(e,n){let t=e[An],r=t.indexOf(n);t.splice(r,1)}function um(e,n){if(Rn(n))return;let t=n[Y];t.destroyNode&&pa(e,n,t,3,null,null),iI(n)}function fl(e,n){if(Rn(n))return;let t=R(null);try{n[S]&=-129,n[S]|=256,n[Oe]&&Vi(n[Oe]),aI(e,n),sI(e,n),n[x].type===1&&n[Y].destroy();let r=n[zt];if(r!==null&&Ke(n[ue])){r!==n[ue]&&ud(r,n);let o=n[ut];o!==null&&o.detachView(e)}Il(n)}finally{R(t)}}function sI(e,n){let t=e.cleanup,r=n[yr];if(t!==null)for(let s=0;s<t.length-1;s+=2)if(typeof t[s]=="string"){let a=t[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[t[s+1]];t[s].call(a)}r!==null&&(n[yr]=null);let o=n[Tt];if(o!==null){n[Tt]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=n[Do];if(i!==null){n[Do]=null;for(let s of i)s.destroy()}}function aI(e,n){let t;if(e!=null&&(t=e.destroyHooks)!=null)for(let r=0;r<t.length;r+=2){let o=n[t[r]];if(!(o instanceof On)){let i=t[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];U(4,a,c);try{c.call(a)}finally{U(5,a,c)}}else{U(4,o,i);try{i.call(o)}finally{U(5,o,i)}}}}}function lm(e,n,t){return cI(e,n.parent,t)}function cI(e,n,t){let r=n;for(;r!==null&&r.type&168;)n=r,r=n.parent;if(r===null)return t[Qe];if(Wt(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===Rt.None||o===Rt.Emulated)return null}return Je(r,t)}function dm(e,n,t){return lI(e,n,t)}function uI(e,n,t){return e.type&40?Je(e,t):null}var lI=uI,eg;function ha(e,n,t,r){let o=lm(e,r,n),i=n[Y],s=r.parent||n[ve],a=dm(s,r,n);if(o!=null)if(Array.isArray(t))for(let c=0;c<t.length;c++)Jp(i,o,t[c],a,!1);else Jp(i,o,t,a,!1);eg!==void 0&&eg(i,r,n,t,o)}function Ao(e,n){if(n!==null){let t=n.type;if(t&3)return Je(n,e);if(t&4)return Ml(-1,e[n.index]);if(t&8){let r=n.child;if(r!==null)return Ao(e,r);{let o=e[n.index];return Ke(o)?Ml(-1,o):Ve(o)}}else{if(t&128)return Ao(e,n.next);if(t&32)return cd(n,e)()||Ve(e[n.index]);{let r=fm(e,n);if(r!==null){if(Array.isArray(r))return r[0];let o=Ut(e[Te]);return Ao(o,r)}else return Ao(e,n.next)}}}return null}function fm(e,n){if(n!==null){let r=e[Te][ve],o=n.projection;return r.projection[o]}return null}function Ml(e,n){let t=ye+e+1;if(t<n.length){let r=n[t],o=r[x].firstChild;if(o!==null)return Ao(r,o)}return n[Gt]}function ld(e,n,t,r,o,i,s){for(;t!=null;){if(t.type===128){t=t.next;continue}let a=r[t.index],c=t.type;if(s&&n===0&&(a&&Mr(Ve(a),r),t.flags|=2),!fa(t))if(c&8)ld(e,n,t.child,r,o,i,!1),wr(n,e,o,a,i);else if(c&32){let u=cd(t,r),l;for(;l=u();)wr(n,e,o,l,i);wr(n,e,o,a,i)}else c&16?hm(e,n,r,t,o,i):wr(n,e,o,a,i);t=s?t.projectionNext:t.next}}function pa(e,n,t,r,o,i){ld(t,r,e.firstChild,n,o,i,!1)}function dI(e,n,t){let r=n[Y],o=lm(e,t,n),i=t.parent||n[ve],s=dm(i,t,n);hm(r,0,n,t,o,s)}function hm(e,n,t,r,o,i){let s=t[Te],c=s[ve].projection[r.projection];if(Array.isArray(c))for(let u=0;u<c.length;u++){let l=c[u];wr(n,e,o,l,i)}else{let u=c,l=s[ue];Pg(r)&&(u.flags|=128),ld(e,n,u,l,o,i,!0)}}function fI(e,n,t,r,o){let i=t[Gt],s=Ve(t);i!==s&&wr(n,e,r,i,o);for(let a=ye;a<t.length;a++){let c=t[a];pa(c[x],c,e,n,r,i)}}function hI(e,n,t,r,o){if(n)o?e.addClass(t,r):e.removeClass(t,r);else{let i=r.indexOf("-")===-1?void 0:ht.DashCase;o==null?e.removeStyle(t,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=ht.Important),e.setStyle(t,r,o,i))}}function xo(e,n,t,r,o=!1){for(;t!==null;){if(t.type===128){t=o?t.projectionNext:t.next;continue}let i=n[t.index];i!==null&&r.push(Ve(i)),Ke(i)&&pm(i,r);let s=t.type;if(s&8)xo(e,n,t.child,r);else if(s&32){let a=cd(t,n),c;for(;c=a();)r.push(c)}else if(s&16){let a=fm(n,t);if(Array.isArray(a))r.push(...a);else{let c=Ut(n[Te]);xo(c[x],c,a,r,!0)}}t=o?t.projectionNext:t.next}return r}function pm(e,n){for(let t=ye;t<e.length;t++){let r=e[t],o=r[x].firstChild;o!==null&&xo(r[x],r,o,n)}e[Gt]!==e[Qe]&&n.push(e[Gt])}function gm(e){if(e[Mn]!==null){for(let n of e[Mn])n.impl.addSequence(n);e[Mn].length=0}}var mm=[];function pI(e){return e[Oe]??gI(e)}function gI(e){let n=mm.pop()??Object.create(vI);return n.lView=e,n}function mI(e){e.lView[Oe]!==e&&(e.lView=null,mm.push(e))}var vI=A(g({},dn),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{Cr(e.lView)},consumerOnSignalRead(){this.lView[Oe]=this}});function yI(e){let n=e[Oe]??Object.create(DI);return n.lView=e,n}var DI=A(g({},dn),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let n=Ut(e.lView);for(;n&&!vm(n[x]);)n=Ut(n);n&&Gu(n)},consumerOnSignalRead(){this.lView[Oe]=this}});function vm(e){return e.type!==2}function ym(e){if(e[Do]===null)return;let n=!0;for(;n;){let t=!1;for(let r of e[Do])r.dirty&&(t=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));n=t&&!!(e[S]&8192)}}var EI=100;function dd(e,n=0){let r=e[ct].rendererFactory,o=!1;o||r.begin?.();try{CI(e,n)}finally{o||r.end?.()}}function CI(e,n){let t=Ju();try{Xu(!0),Al(e,n);let r=0;for(;wo(e);){if(r===EI)throw new C(103,!1);r++,Al(e,1)}}finally{Xu(t)}}function Dm(e,n){Ku(n?Io.Exhaustive:Io.OnlyDirtyViews);try{dd(e)}finally{Ku(Io.Off)}}function wI(e,n,t,r){if(Rn(n))return;let o=n[S],i=!1,s=!1;Ns(n);let a=!0,c=null,u=null;i||(vm(e)?(u=pI(n),c=fn(u)):Li()===null?(a=!1,u=yI(n),c=fn(u)):n[Oe]&&(Vi(n[Oe]),n[Oe]=null));try{zu(n),Rp(e.bindingStartIndex),t!==null&&im(e,n,t,2,r);let l=(o&3)===3;if(!i)if(l){let f=e.preOrderCheckHooks;f!==null&&Vs(n,f,null)}else{let f=e.preOrderHooks;f!==null&&Bs(n,f,0,null),ll(n,0)}if(s||II(n),ym(n),Em(n,0),e.contentQueries!==null&&zg(e,n),!i)if(l){let f=e.contentCheckHooks;f!==null&&Vs(n,f)}else{let f=e.contentHooks;f!==null&&Bs(n,f,1),ll(n,1)}_I(e,n);let d=e.components;d!==null&&wm(n,d,0);let h=e.viewQuery;if(h!==null&&_l(2,h,r),!i)if(l){let f=e.viewCheckHooks;f!==null&&Vs(n,f)}else{let f=e.viewHooks;f!==null&&Bs(n,f,2),ll(n,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),n[Cs]){for(let f of n[Cs])f();n[Cs]=null}i||(gm(n),n[S]&=-73)}catch(l){throw i||Cr(n),l}finally{u!==null&&(Jn(u,c),a&&mI(u)),xs()}}function Em(e,n){for(let t=Lg(e);t!==null;t=jg(t))for(let r=ye;r<t.length;r++){let o=t[r];Cm(o,n)}}function II(e){for(let n=Lg(e);n!==null;n=jg(n)){if(!(n[S]&2))continue;let t=n[An];for(let r=0;r<t.length;r++){let o=t[r];Gu(o)}}}function bI(e,n,t){U(18);let r=Be(n,e);Cm(r,t),U(19,r[ge])}function Cm(e,n){bs(e)&&Al(e,n)}function Al(e,n){let r=e[x],o=e[S],i=e[Oe],s=!!(n===0&&o&16);if(s||=!!(o&64&&n===0),s||=!!(o&1024),s||=!!(i?.dirty&&oo(i)),s||=!1,i&&(i.dirty=!1),e[S]&=-9217,s)wI(r,e,r.template,e[ge]);else if(o&8192){let a=R(null);try{ym(e),Em(e,1);let c=r.components;c!==null&&wm(e,c,1),gm(e)}finally{R(a)}}}function wm(e,n,t){for(let r=0;r<n.length;r++)bI(e,n[r],t)}function _I(e,n){let t=e.hostBindingOpCodes;if(t!==null)try{for(let r=0;r<t.length;r++){let o=t[r];if(o<0)Yt(~o);else{let i=o,s=t[++r],a=t[++r];xp(s,i);let c=n[i];U(24,c),a(2,c),U(25,c)}}}finally{Yt(-1)}}function fd(e,n){let t=Ju()?64:1088;for(e[ct].changeDetectionScheduler?.notify(n);e;){e[S]|=t;let r=Ut(e);if(Er(e)&&!r)return e;e=r}return null}function Im(e,n,t,r){return[e,!0,0,n,null,r,null,t,null,null]}function bm(e,n,t,r=!0){let o=n[x];if(TI(o,n,e,t),r){let s=Ml(t,e),a=n[Y],c=a.parentNode(e[Gt]);c!==null&&oI(o,e[ve],a,n,c,s)}let i=n[vr];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function Rl(e,n){if(e.length<=ye)return;let t=ye+n,r=e[t];if(r){let o=r[zt];o!==null&&o!==e&&ud(o,r),n>0&&(e[t-1][je]=r[je]);let i=mo(e,ye+n);rI(r[x],r);let s=i[ut];s!==null&&s.detachView(i[x]),r[ue]=null,r[je]=null,r[S]&=-129}return r}function TI(e,n,t,r){let o=ye+r,i=t.length;r>0&&(t[o-1][je]=n),r<i-ye?(n[je]=t[o],Ou(t,ye+r,n)):(t.push(n),n[je]=null),n[ue]=t;let s=n[zt];s!==null&&t!==s&&_m(s,n);let a=n[ut];a!==null&&a.insertView(e),_s(n),n[S]|=128}function _m(e,n){let t=e[An],r=n[ue];if(lt(r))e[S]|=2;else{let o=r[ue][Te];n[Te]!==o&&(e[S]|=2)}t===null?e[An]=[n]:t.push(n)}var Kt=class{_lView;_cdRefInjectingView;_appRef=null;_attachedToViewContainer=!1;exhaustive;get rootNodes(){let n=this._lView,t=n[x];return xo(t,n,t.firstChild,[])}constructor(n,t){this._lView=n,this._cdRefInjectingView=t}get context(){return this._lView[ge]}set context(n){this._lView[ge]=n}get destroyed(){return Rn(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let n=this._lView[ue];if(Ke(n)){let t=n[Eo],r=t?t.indexOf(this):-1;r>-1&&(Rl(n,r),mo(t,r))}this._attachedToViewContainer=!1}um(this._lView[x],this._lView)}onDestroy(n){Wu(this._lView,n)}markForCheck(){fd(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[S]&=-129}reattach(){_s(this._lView),this._lView[S]|=128}detectChanges(){this._lView[S]|=1024,dd(this._lView)}checkNoChanges(){return;try{this.exhaustive??=this._lView[_n].get(tI,Xp)}catch{this.exhaustive=Xp}}attachToViewContainerRef(){if(this._appRef)throw new C(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let n=Er(this._lView),t=this._lView[zt];t!==null&&!n&&ud(t,this._lView),cm(this._lView[x],this._lView)}attachToAppRef(n){if(this._attachedToViewContainer)throw new C(902,!1);this._appRef=n;let t=Er(this._lView),r=this._lView[zt];r!==null&&!t&&_m(r,this._lView),_s(this._lView)}};var et=(()=>{class e{_declarationLView;_declarationTContainer;elementRef;static __NG_ELEMENT_ID__=SI;constructor(t,r,o){this._declarationLView=t,this._declarationTContainer=r,this.elementRef=o}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,r){return this.createEmbeddedViewImpl(t,r)}createEmbeddedViewImpl(t,r,o){let i=am(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:r,dehydratedView:o});return new Kt(i)}}return e})();function SI(){return hd(le(),L())}function hd(e,n){return e.type&4?new et(n,e,Sr(e,n)):null}function Fo(e,n,t,r,o){let i=e.data[n];if(i===null)i=MI(e,n,t,r,o),Np()&&(i.flags|=32);else if(i.type&64){i.type=t,i.value=r,i.attrs=o;let s=Sp();i.injectorIndex=s===null?-1:s.injectorIndex}return qt(i,!0),i}function MI(e,n,t,r,o){let i=Qu(),s=Ss(),a=s?i:i&&i.parent,c=e.data[n]=RI(e,a,t,n,r,o);return AI(e,c,i,s),c}function AI(e,n,t,r){e.firstChild===null&&(e.firstChild=n),t!==null&&(r?t.child==null&&n.parent!==null&&(t.child=n):t.next===null&&(t.next=n,n.prev=t))}function RI(e,n,t,r,o,i){let s=n?n.injectorIndex:-1,a=0;return Yu()&&(a|=128),{type:t,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:n,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var zF=new RegExp(`^(\\d+)*(${ow}|${rw})*(.*)`);var NI=()=>null;function Nl(e,n){return NI(e,n)}var Tm=class{},ga=class{},xl=class{resolveComponentFactory(n){throw new C(917,!1)}},Lo=class{static NULL=new xl},kn=class{},tn=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>xI()}return e})();function xI(){let e=L(),n=le(),t=Be(n.index,e);return(lt(t)?t:e)[Y]}var Sm=(()=>{class e{static \u0275prov=E({token:e,providedIn:"root",factory:()=>null})}return e})();var Hs={},Ol=class{injector;parentInjector;constructor(n,t){this.injector=n,this.parentInjector=t}get(n,t,r){let o=this.injector.get(n,Hs,r);return o!==Hs||t===Hs?o:this.parentInjector.get(n,t,r)}};function kl(e,n,t){let r=t?e.styles:null,o=t?e.classes:null,i=0;if(n!==null)for(let s=0;s<n.length;s++){let a=n[s];if(typeof a=="number")i=a;else if(i==1)o=wu(o,a);else if(i==2){let c=a,u=n[++s];r=wu(r,c+": "+u+";")}}t?e.styles=r:e.stylesWithoutHost=r,t?e.classes=o:e.classesWithoutHost=o}function v(e,n=0){let t=L();if(t===null)return I(e,n);let r=le();return xg(r,t,de(e),n)}function pd(){let e="invalid";throw new Error(e)}function gd(e,n,t,r,o){let i=r===null?null:{"":-1},s=o(e,t);if(s!==null){let a=s,c=null,u=null;for(let l of s)if(l.resolveHostDirectives!==null){[a,c,u]=l.resolveHostDirectives(s);break}PI(e,n,t,a,i,c,u)}i!==null&&r!==null&&OI(t,r,i)}function OI(e,n,t){let r=e.localNames=[];for(let o=0;o<n.length;o+=2){let i=t[n[o+1]];if(i==null)throw new C(-301,!1);r.push(n[o],i)}}function kI(e,n,t){n.componentOffset=t,(e.components??=[]).push(n.index)}function PI(e,n,t,r,o,i,s){let a=r.length,c=!1;for(let h=0;h<a;h++){let f=r[h];!c&&dt(f)&&(c=!0,kI(e,t,h)),Cl(Ws(t,n),e,f.type)}UI(t,e.data.length,a);for(let h=0;h<a;h++){let f=r[h];f.providersResolver&&f.providersResolver(f)}let u=!1,l=!1,d=rm(e,n,a,null);a>0&&(t.directiveToIndex=new Map);for(let h=0;h<a;h++){let f=r[h];if(t.mergedAttrs=br(t.mergedAttrs,f.hostAttrs),LI(e,t,n,d,f),BI(d,f,o),s!==null&&s.has(f)){let[y,w]=s.get(f);t.directiveToIndex.set(f.type,[d,y+t.directiveStart,w+t.directiveStart])}else(i===null||!i.has(f))&&t.directiveToIndex.set(f.type,d);f.contentQueries!==null&&(t.flags|=4),(f.hostBindings!==null||f.hostAttrs!==null||f.hostVars!==0)&&(t.flags|=64);let m=f.type.prototype;!u&&(m.ngOnChanges||m.ngOnInit||m.ngDoCheck)&&((e.preOrderHooks??=[]).push(t.index),u=!0),!l&&(m.ngOnChanges||m.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(t.index),l=!0),d++}FI(e,t,i)}function FI(e,n,t){for(let r=n.directiveStart;r<n.directiveEnd;r++){let o=e.data[r];if(t===null||!t.has(o))tg(0,n,o,r),tg(1,n,o,r),rg(n,r,!1);else{let i=t.get(o);ng(0,n,i,r),ng(1,n,i,r),rg(n,r,!0)}}}function tg(e,n,t,r){let o=e===0?t.inputs:t.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=n.inputs??={}:s=n.outputs??={},s[i]??=[],s[i].push(r),Mm(n,i)}}function ng(e,n,t,r){let o=e===0?t.inputs:t.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=n.hostDirectiveInputs??={}:a=n.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),Mm(n,s)}}function Mm(e,n){n==="class"?e.flags|=8:n==="style"&&(e.flags|=16)}function rg(e,n,t){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!t&&o===null||t&&i===null||ed(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!t&&o.hasOwnProperty(c)){let u=o[c];for(let l of u)if(l===n){s??=[],s.push(c,r[a+1]);break}}else if(t&&i.hasOwnProperty(c)){let u=i[c];for(let l=0;l<u.length;l+=2)if(u[l]===n){s??=[],s.push(u[l+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function LI(e,n,t,r,o){e.data[r]=o;let i=o.factory||(o.factory=Dn(o.type,!0)),s=new On(i,dt(o),v);e.blueprint[r]=s,t[r]=s,jI(e,n,r,rm(e,t,o.hostVars,tt),o)}function jI(e,n,t,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~n.index;VI(s)!=a&&s.push(a),s.push(t,r,i)}}function VI(e){let n=e.length;for(;n>0;){let t=e[--n];if(typeof t=="number"&&t<0)return t}return 0}function BI(e,n,t){if(t){if(n.exportAs)for(let r=0;r<n.exportAs.length;r++)t[n.exportAs[r]]=e;dt(n)&&(t[""]=e)}}function UI(e,n,t){e.flags|=1,e.directiveStart=n,e.directiveEnd=n+t,e.providerIndexes=n}function Am(e,n,t,r,o,i,s,a){let c=n.consts,u=Nn(c,s),l=Fo(n,e,2,r,u);return i&&gd(n,t,l,Nn(c,a),o),l.mergedAttrs=br(l.mergedAttrs,l.attrs),l.attrs!==null&&kl(l,l.attrs,!1),l.mergedAttrs!==null&&kl(l,l.mergedAttrs,!0),n.queries!==null&&n.queries.elementStart(n,l),l}function Rm(e,n){ql(e,n),ws(n)&&e.queries.elementEnd(n)}function md(e){return xm(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function Nm(e,n){if(Array.isArray(e))for(let t=0;t<e.length;t++)n(e[t]);else{let t=e[Symbol.iterator](),r;for(;!(r=t.next()).done;)n(r.value)}}function xm(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function HI(e,n,t){return e[n]=t}function $I(e,n){return e[n]}function Pn(e,n,t){if(t===tt)return!1;let r=e[n];return Object.is(r,t)?!1:(e[n]=t,!0)}function zI(e,n,t,r){let o=Pn(e,n,t);return Pn(e,n+1,r)||o}function hl(e,n,t){return function r(o){let i=Wt(e)?Be(e.index,n):n;fd(i,5);let s=n[ge],a=og(n,s,t,o),c=r.__ngNextListenerFn__;for(;c;)a=og(n,s,c,o)&&a,c=c.__ngNextListenerFn__;return a}}function og(e,n,t,r){let o=R(null);try{return U(6,n,t),t(r)!==!1}catch(i){return Kw(e,i),!1}finally{U(7,n,t),R(o)}}function GI(e,n,t,r,o,i,s,a){let c=Co(e),u=!1,l=null;if(!r&&c&&(l=WI(n,t,i,e.index)),l!==null){let d=l.__ngLastListenerFn__||l;d.__ngNextListenerFn__=s,l.__ngLastListenerFn__=s,u=!0}else{let d=Je(e,t),h=r?r(d):d;sw(t,h,i,a);let f=o.listen(h,i,a),m=r?y=>r(Ve(y[e.index])):e.index;Om(m,n,t,i,a,f,!1)}return u}function WI(e,n,t,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===t&&o[i+1]===r){let a=n[yr],c=o[i+2];return a&&a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function Om(e,n,t,r,o,i,s){let a=n.firstCreatePass?Zu(n):null,c=qu(t),u=c.length;c.push(o,i),a&&a.push(r,e,u,(u+1)*(s?-1:1))}function ig(e,n,t,r,o,i){let s=n[t],a=n[x],u=a.data[t].outputs[r],d=s[u].subscribe(i);Om(e.index,a,n,o,i,d,!0)}var Pl=Symbol("BINDING");var Qs=class extends Lo{ngModule;constructor(n){super(),this.ngModule=n}resolveComponentFactory(n){let t=at(n);return new Jt(t,this.ngModule)}};function qI(e){return Object.keys(e).map(n=>{let[t,r,o]=e[n],i={propName:t,templateName:n,isSignal:(r&la.SignalBased)!==0};return o&&(i.transform=o),i})}function ZI(e){return Object.keys(e).map(n=>({propName:e[n],templateName:n}))}function YI(e,n,t){let r=n instanceof z?n:n?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new Ol(t,r):t}function QI(e){let n=e.get(kn,null);if(n===null)throw new C(407,!1);let t=e.get(Sm,null),r=e.get(St,null);return{rendererFactory:n,sanitizer:t,changeDetectionScheduler:r,ngReflect:!1}}function KI(e,n){let t=(e.selectors[0][0]||"div").toLowerCase();return Xg(n,t,t==="svg"?hp:t==="math"?pp:null)}var Jt=class extends ga{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=qI(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=ZI(this.componentDef.outputs),this.cachedOutputs}constructor(n,t){super(),this.componentDef=n,this.ngModule=t,this.componentType=n.type,this.selector=Sw(n.selectors),this.ngContentSelectors=n.ngContentSelectors??[],this.isBoundToModule=!!t}create(n,t,r,o,i,s){U(22);let a=R(null);try{let c=this.componentDef,u=JI(r,c,s,i),l=YI(c,o||this.ngModule,n),d=QI(l),h=d.rendererFactory.createRenderer(null,c),f=r?Vw(h,r,c.encapsulation,l):KI(c,h),m=s?.some(sg)||i?.some(T=>typeof T!="function"&&T.bindings.some(sg)),y=nd(null,u,null,512|nm(c),null,null,d,h,l,null,$g(f,l,!0));y[Se]=f,Ns(y);let w=null;try{let T=Am(Se,u,y,"#host",()=>u.directiveRegistry,!0,0);f&&(tm(h,f,T),Mr(f,y)),da(u,y,T),Kl(u,T,y),Rm(u,T),t!==void 0&&eb(T,this.ngContentSelectors,t),w=Be(T.index,y),y[ge]=w[ge],ad(u,y,null)}catch(T){throw w!==null&&Il(w),Il(y),T}finally{U(23),xs()}return new Ks(this.componentType,y,!!m)}finally{R(a)}}};function JI(e,n,t,r){let o=e?["ng-version","20.0.6"]:Mw(n.selectors[0]),i=null,s=null,a=0;if(t)for(let l of t)a+=l[Pl].requiredVars,l.create&&(l.targetIdx=0,(i??=[]).push(l)),l.update&&(l.targetIdx=0,(s??=[]).push(l));if(r)for(let l=0;l<r.length;l++){let d=r[l];if(typeof d!="function")for(let h of d.bindings){a+=h[Pl].requiredVars;let f=l+1;h.create&&(h.targetIdx=f,(i??=[]).push(h)),h.update&&(h.targetIdx=f,(s??=[]).push(h))}}let c=[n];if(r)for(let l of r){let d=typeof l=="function"?l:l.type,h=Lu(d);c.push(h)}return td(0,null,XI(i,s),1,a,c,null,null,null,[o],null)}function XI(e,n){return!e&&!n?null:t=>{if(t&1&&e)for(let r of e)r.create();if(t&2&&n)for(let r of n)r.update()}}function sg(e){let n=e[Pl].kind;return n==="input"||n==="twoWay"}var Ks=class extends Tm{_rootLView;_hasInputBindings;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(n,t,r){super(),this._rootLView=t,this._hasInputBindings=r,this._tNode=Is(t[x],Se),this.location=Sr(this._tNode,t),this.instance=Be(this._tNode.index,t)[ge],this.hostView=this.changeDetectorRef=new Kt(t,void 0),this.componentType=n}setInput(n,t){this._hasInputBindings;let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(n)&&Object.is(this.previousInputValues.get(n),t))return;let o=this._rootLView,i=sd(r,o[x],o,n,t);this.previousInputValues.set(n,t);let s=Be(r.index,o);fd(s,1)}get injector(){return new xn(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(n){this.hostView.onDestroy(n)}};function eb(e,n,t){let r=e.projection=[];for(let o=0;o<n.length;o++){let i=t[o];r.push(i!=null&&i.length?Array.from(i):null)}}var Ue=(()=>{class e{static __NG_ELEMENT_ID__=tb}return e})();function tb(){let e=le();return Pm(e,L())}var nb=Ue,km=class extends nb{_lContainer;_hostTNode;_hostLView;constructor(n,t,r){super(),this._lContainer=n,this._hostTNode=t,this._hostLView=r}get element(){return Sr(this._hostTNode,this._hostLView)}get injector(){return new xn(this._hostTNode,this._hostLView)}get parentInjector(){let n=Zl(this._hostTNode,this._hostLView);if(Tg(n)){let t=Gs(n,this._hostLView),r=zs(n),o=t[x].data[r+8];return new xn(o,t)}else return new xn(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(n){let t=ag(this._lContainer);return t!==null&&t[n]||null}get length(){return this._lContainer.length-ye}createEmbeddedView(n,t,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=Nl(this._lContainer,n.ssrId),a=n.createEmbeddedViewImpl(t||{},i,s);return this.insertImpl(a,o,Sl(this._hostTNode,s)),a}createComponent(n,t,r,o,i,s,a){let c=n&&!NC(n),u;if(c)u=t;else{let w=t||{};u=w.index,r=w.injector,o=w.projectableNodes,i=w.environmentInjector||w.ngModuleRef,s=w.directives,a=w.bindings}let l=c?n:new Jt(at(n)),d=r||this.parentInjector;if(!i&&l.ngModule==null){let T=(c?d:this.parentInjector).get(z,null);T&&(i=T)}let h=at(l.componentType??{}),f=Nl(this._lContainer,h?.id??null),m=f?.firstChild??null,y=l.create(d,o,m,i,s,a);return this.insertImpl(y.hostView,u,Sl(this._hostTNode,f)),y}insert(n,t){return this.insertImpl(n,t,!0)}insertImpl(n,t,r){let o=n._lView;if(mp(o)){let a=this.indexOf(n);if(a!==-1)this.detach(a);else{let c=o[ue],u=new km(c,c[ve],c[ue]);u.detach(u.indexOf(n))}}let i=this._adjustIndex(t),s=this._lContainer;return bm(s,o,i,r),n.attachToViewContainerRef(),Ou(pl(s),i,n),n}move(n,t){return this.insert(n,t)}indexOf(n){let t=ag(this._lContainer);return t!==null?t.indexOf(n):-1}remove(n){let t=this._adjustIndex(n,-1),r=Rl(this._lContainer,t);r&&(mo(pl(this._lContainer),t),um(r[x],r))}detach(n){let t=this._adjustIndex(n,-1),r=Rl(this._lContainer,t);return r&&mo(pl(this._lContainer),t)!=null?new Kt(r):null}_adjustIndex(n,t=0){return n??this.length+t}};function ag(e){return e[Eo]}function pl(e){return e[Eo]||(e[Eo]=[])}function Pm(e,n){let t,r=n[e.index];return Ke(r)?t=r:(t=Im(r,n,null,e),n[e.index]=t,rd(n,t)),ob(t,n,e,r),new km(t,e,n)}function rb(e,n){let t=e[Y],r=t.createComment(""),o=Je(n,e),i=t.parentNode(o);return Ys(t,i,r,t.nextSibling(o),!1),r}var ob=ab,ib=()=>!1;function sb(e,n,t){return ib(e,n,t)}function ab(e,n,t,r){if(e[Gt])return;let o;t.type&8?o=Ve(r):o=rb(n,t),e[Gt]=o}var Fl=class e{queryList;matches=null;constructor(n){this.queryList=n}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},Ll=class e{queries;constructor(n=[]){this.queries=n}createEmbeddedView(n){let t=n.queries;if(t!==null){let r=n.contentQueries!==null?n.contentQueries[0]:t.length,o=[];for(let i=0;i<r;i++){let s=t.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(n){this.dirtyQueriesWithMatches(n)}detachView(n){this.dirtyQueriesWithMatches(n)}finishViewCreation(n){this.dirtyQueriesWithMatches(n)}dirtyQueriesWithMatches(n){for(let t=0;t<this.queries.length;t++)vd(n,t).matches!==null&&this.queries[t].setDirty()}},Js=class{flags;read;predicate;constructor(n,t,r=null){this.flags=t,this.read=r,typeof n=="string"?this.predicate=gb(n):this.predicate=n}},jl=class e{queries;constructor(n=[]){this.queries=n}elementStart(n,t){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(n,t)}elementEnd(n){for(let t=0;t<this.queries.length;t++)this.queries[t].elementEnd(n)}embeddedTView(n){let t=null;for(let r=0;r<this.length;r++){let o=t!==null?t.length:0,i=this.getByIndex(r).embeddedTView(n,o);i&&(i.indexInDeclarationView=r,t!==null?t.push(i):t=[i])}return t!==null?new e(t):null}template(n,t){for(let r=0;r<this.queries.length;r++)this.queries[r].template(n,t)}getByIndex(n){return this.queries[n]}get length(){return this.queries.length}track(n){this.queries.push(n)}},Vl=class e{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(n,t=-1){this.metadata=n,this._declarationNodeIndex=t}elementStart(n,t){this.isApplyingToNode(t)&&this.matchTNode(n,t)}elementEnd(n){this._declarationNodeIndex===n.index&&(this._appliesToNextNode=!1)}template(n,t){this.elementStart(n,t)}embeddedTView(n,t){return this.isApplyingToNode(n)?(this.crossesNgTemplate=!0,this.addMatch(-n.index,t),new e(this.metadata)):null}isApplyingToNode(n){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let t=this._declarationNodeIndex,r=n.parent;for(;r!==null&&r.type&8&&r.index!==t;)r=r.parent;return t===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(n,t){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(n,t,cb(t,i)),this.matchTNodeWithReadOption(n,t,Us(t,n,i,!1,!1))}else r===et?t.type&4&&this.matchTNodeWithReadOption(n,t,-1):this.matchTNodeWithReadOption(n,t,Us(t,n,r,!1,!1))}matchTNodeWithReadOption(n,t,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===Q||o===Ue||o===et&&t.type&4)this.addMatch(t.index,-2);else{let i=Us(t,n,o,!1,!1);i!==null&&this.addMatch(t.index,i)}else this.addMatch(t.index,r)}}addMatch(n,t){this.matches===null?this.matches=[n,t]:this.matches.push(n,t)}};function cb(e,n){let t=e.localNames;if(t!==null){for(let r=0;r<t.length;r+=2)if(t[r]===n)return t[r+1]}return null}function ub(e,n){return e.type&11?Sr(e,n):e.type&4?hd(e,n):null}function lb(e,n,t,r){return t===-1?ub(n,e):t===-2?db(e,n,r):No(e,e[x],t,n)}function db(e,n,t){if(t===Q)return Sr(n,e);if(t===et)return hd(n,e);if(t===Ue)return Pm(n,e)}function Fm(e,n,t,r){let o=n[ut].queries[r];if(o.matches===null){let i=e.data,s=t.matches,a=[];for(let c=0;s!==null&&c<s.length;c+=2){let u=s[c];if(u<0)a.push(null);else{let l=i[u];a.push(lb(n,l,s[c+1],t.metadata.read))}}o.matches=a}return o.matches}function Bl(e,n,t,r){let o=e.queries.getByIndex(t),i=o.matches;if(i!==null){let s=Fm(e,n,o,t);for(let a=0;a<i.length;a+=2){let c=i[a];if(c>0)r.push(s[a/2]);else{let u=i[a+1],l=n[-c];for(let d=ye;d<l.length;d++){let h=l[d];h[zt]===h[ue]&&Bl(h[x],h,u,r)}if(l[An]!==null){let d=l[An];for(let h=0;h<d.length;h++){let f=d[h];Bl(f[x],f,u,r)}}}}}return r}function fb(e,n){return e[ut].queries[n].queryList}function Lm(e,n,t){let r=new qs((t&4)===4);return Dp(e,n,r,r.destroy),(n[ut]??=new Ll).queries.push(new Fl(r))-1}function hb(e,n,t){let r=ne();return r.firstCreatePass&&(jm(r,new Js(e,n,t),-1),(n&2)===2&&(r.staticViewQueries=!0)),Lm(r,L(),n)}function pb(e,n,t,r){let o=ne();if(o.firstCreatePass){let i=le();jm(o,new Js(n,t,r),i.index),mb(o,e),(t&2)===2&&(o.staticContentQueries=!0)}return Lm(o,L(),t)}function gb(e){return e.split(",").map(n=>n.trim())}function jm(e,n,t){e.queries===null&&(e.queries=new jl),e.queries.track(new Vl(n,t))}function mb(e,n){let t=e.contentQueries||(e.contentQueries=[]),r=t.length?t[t.length-1]:-1;n!==r&&t.push(e.queries.length-1,n)}function vd(e,n){return e.queries.getByIndex(n)}function vb(e,n){let t=e[x],r=vd(t,n);return r.crossesNgTemplate?Bl(t,e,n,[]):Fm(t,e,r,n)}var cg=new Set;function Rr(e){cg.has(e)||(cg.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var Fn=class{},ma=class{};var Xs=class extends Fn{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new Qs(this);constructor(n,t,r,o=!0){super(),this.ngModuleType=n,this._parent=t;let i=Fu(n);this._bootstrapComponents=Qg(i.bootstrap),this._r3Injector=ol(n,t,[{provide:Fn,useValue:this},{provide:Lo,useValue:this.componentFactoryResolver},...r],be(n),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let n=this._r3Injector;!n.destroyed&&n.destroy(),this.destroyCbs.forEach(t=>t()),this.destroyCbs=null}onDestroy(n){this.destroyCbs.push(n)}},ea=class extends ma{moduleType;constructor(n){super(),this.moduleType=n}create(n){return new Xs(this.moduleType,n,[])}};var Oo=class extends Fn{injector;componentFactoryResolver=new Qs(this);instance=null;constructor(n){super();let t=new Cn([...n.providers,{provide:Fn,useValue:this},{provide:Lo,useValue:this.componentFactoryResolver}],n.parent||mr(),n.debugName,new Set(["environment"]));this.injector=t,n.runEnvironmentInitializers&&t.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(n){this.injector.onDestroy(n)}};function Nr(e,n,t=null){return new Oo({providers:e,parent:n,debugName:t,runEnvironmentInitializers:!0}).injector}var yb=(()=>{class e{_injector;cachedInjectors=new Map;constructor(t){this._injector=t}getOrCreateStandaloneInjector(t){if(!t.standalone)return null;if(!this.cachedInjectors.has(t)){let r=ju(!1,t.type),o=r.length>0?Nr([r],this._injector,`Standalone[${t.type.name}]`):null;this.cachedInjectors.set(t,o)}return this.cachedInjectors.get(t)}ngOnDestroy(){try{for(let t of this.cachedInjectors.values())t!==null&&t.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=E({token:e,providedIn:"environment",factory:()=>new e(I(z))})}return e})();function yd(e){return Tr(()=>{let n=Vm(e),t=A(g({},n),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Yl.OnPush,directiveDefs:null,pipeDefs:null,dependencies:n.standalone&&e.dependencies||null,getStandaloneInjector:n.standalone?o=>o.get(yb).getOrCreateStandaloneInjector(t):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||Rt.Emulated,styles:e.styles||xe,_:null,schemas:e.schemas||null,tView:null,id:""});n.standalone&&Rr("NgStandalone"),Bm(t);let r=e.dependencies;return t.directiveDefs=ug(r,!1),t.pipeDefs=ug(r,!0),t.id=Ib(t),t})}function Db(e){return at(e)||Lu(e)}function Eb(e){return e!==null}function ke(e){return Tr(()=>({type:e.type,bootstrap:e.bootstrap||xe,declarations:e.declarations||xe,imports:e.imports||xe,exports:e.exports||xe,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function Cb(e,n){if(e==null)return Ht;let t={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=la.None,c=null),t[i]=[r,a,c],n[i]=s}return t}function wb(e){if(e==null)return Ht;let n={};for(let t in e)e.hasOwnProperty(t)&&(n[e[t]]=t);return n}function j(e){return Tr(()=>{let n=Vm(e);return Bm(n),n})}function Vm(e){let n={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:n,inputConfig:e.inputs||Ht,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||xe,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,resolveHostDirectives:null,hostDirectives:null,inputs:Cb(e.inputs,n),outputs:wb(e.outputs),debugInfo:null}}function Bm(e){e.features?.forEach(n=>n(e))}function ug(e,n){if(!e)return null;let t=n?sp:Db;return()=>(typeof e=="function"?e():e).map(r=>t(r)).filter(Eb)}function Ib(e){let n=0,t=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,t,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))n=Math.imul(31,n)+i.charCodeAt(0)<<0;return n+=2147483648,"c"+n}function bb(e){return Object.getPrototypeOf(e.prototype).constructor}function He(e){let n=bb(e.type),t=!0,r=[e];for(;n;){let o;if(dt(e))o=n.\u0275cmp||n.\u0275dir;else{if(n.\u0275cmp)throw new C(903,!1);o=n.\u0275dir}if(o){if(t){r.push(o);let s=e;s.inputs=gl(e.inputs),s.declaredInputs=gl(e.declaredInputs),s.outputs=gl(e.outputs);let a=o.hostBindings;a&&Ab(e,a);let c=o.viewQuery,u=o.contentQueries;if(c&&Sb(e,c),u&&Mb(e,u),_b(e,o),Kh(e.outputs,o.outputs),dt(o)&&o.data.animation){let l=e.data;l.animation=(l.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===He&&(t=!1)}}n=Object.getPrototypeOf(n)}Tb(r)}function _b(e,n){for(let t in n.inputs){if(!n.inputs.hasOwnProperty(t)||e.inputs.hasOwnProperty(t))continue;let r=n.inputs[t];r!==void 0&&(e.inputs[t]=r,e.declaredInputs[t]=n.declaredInputs[t])}}function Tb(e){let n=0,t=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=n+=o.hostVars,o.hostAttrs=br(o.hostAttrs,t=br(t,o.hostAttrs))}}function gl(e){return e===Ht?{}:e===xe?[]:e}function Sb(e,n){let t=e.viewQuery;t?e.viewQuery=(r,o)=>{n(r,o),t(r,o)}:e.viewQuery=n}function Mb(e,n){let t=e.contentQueries;t?e.contentQueries=(r,o,i)=>{n(r,o,i),t(r,o,i)}:e.contentQueries=n}function Ab(e,n){let t=e.hostBindings;t?e.hostBindings=(r,o)=>{n(r,o),t(r,o)}:e.hostBindings=n}function Rb(e,n,t,r,o,i,s,a,c){let u=n.consts,l=Fo(n,e,4,s||null,a||null);Ts()&&gd(n,t,l,Nn(u,c),id),l.mergedAttrs=br(l.mergedAttrs,l.attrs),ql(n,l);let d=l.tView=td(2,l,r,o,i,n.directiveRegistry,n.pipeRegistry,null,n.schemas,u,null);return n.queries!==null&&(n.queries.template(n,l),d.queries=n.queries.embeddedTView(l)),l}function Um(e,n,t,r,o,i,s,a,c,u,l){let d=t+Se,h=n.firstCreatePass?Rb(d,n,e,r,o,i,s,a,u):n.data[d];c&&(h.flags|=c),qt(h,!1);let f=Nb(n,e,h,t);_o()&&ha(n,e,f,h),Mr(f,e);let m=Im(f,e,f,h);return e[d]=m,rd(e,m),sb(m,h,e),Co(h)&&da(n,e,h),u!=null&&od(e,h,l),h}function Hm(e,n,t,r,o,i,s,a){let c=L(),u=ne(),l=Nn(u.consts,i);return Um(c,u,e,n,t,r,o,l,void 0,s,a),Hm}var Nb=xb;function xb(e,n,t,r){return To(!0),n[Y].createComment("")}var va=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(va||{}),jn=new D(""),$m=!1,Ul=class extends H{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(n=!1){super(),this.__isAsync=n,dp()&&(this.destroyRef=p(Qt,{optional:!0})??void 0,this.pendingTasks=p(At,{optional:!0})??void 0)}emit(n){let t=R(null);try{super.next(n)}finally{R(t)}}subscribe(n,t,r){let o=n,i=t||(()=>null),s=r;if(n&&typeof n=="object"){let c=n;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return n instanceof ee&&n.add(a),a}wrapInTimeout(n){return t=>{let r=this.pendingTasks?.add();setTimeout(()=>{try{n(t)}finally{r!==void 0&&this.pendingTasks?.remove(r)}})}}},$=Ul;function zm(e){let n,t;function r(){e=Mo;try{t!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(t),n!==void 0&&clearTimeout(n)}catch{}}return n=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(t=requestAnimationFrame(()=>{e(),r()})),()=>r()}function lg(e){return queueMicrotask(()=>e()),()=>{e=Mo}}var Dd="isAngularZone",ta=Dd+"_ID",Ob=0,B=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new $(!1);onMicrotaskEmpty=new $(!1);onStable=new $(!1);onError=new $(!1);constructor(n){let{enableLongStackTrace:t=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=$m}=n;if(typeof Zone>"u")throw new C(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),t&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,Fb(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(Dd)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new C(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new C(909,!1)}run(n,t,r){return this._inner.run(n,t,r)}runTask(n,t,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,n,kb,Mo,Mo);try{return i.runTask(s,t,r)}finally{i.cancelTask(s)}}runGuarded(n,t,r){return this._inner.runGuarded(n,t,r)}runOutsideAngular(n){return this._outer.run(n)}},kb={};function Ed(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function Pb(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function n(){zm(()=>{e.callbackScheduled=!1,Hl(e),e.isCheckStableRunning=!0,Ed(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{n()}):e._outer.run(()=>{n()}),Hl(e)}function Fb(e){let n=()=>{Pb(e)},t=Ob++;e._inner=e._inner.fork({name:"angular",properties:{[Dd]:!0,[ta]:t,[ta+t]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if(Lb(c))return r.invokeTask(i,s,a,c);try{return dg(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&n(),fg(e)}},onInvoke:(r,o,i,s,a,c,u)=>{try{return dg(e),r.invoke(i,s,a,c,u)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!jb(c)&&n(),fg(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,Hl(e),Ed(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function Hl(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function dg(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function fg(e){e._nesting--,Ed(e)}var na=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new $;onMicrotaskEmpty=new $;onStable=new $;onError=new $;run(n,t,r){return n.apply(t,r)}runGuarded(n,t,r){return n.apply(t,r)}runOutsideAngular(n){return n()}runTask(n,t,r,o){return n.apply(t,r)}};function Lb(e){return Gm(e,"__ignore_ng_zone__")}function jb(e){return Gm(e,"__scheduler_tick__")}function Gm(e,n){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[n]===!0}var Cd=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=E({token:e,providedIn:"root",factory:()=>new e})}return e})(),Wm=[0,1,2,3],qm=(()=>{class e{ngZone=p(B);scheduler=p(St);errorHandler=p(Ze,{optional:!0});sequences=new Set;deferredRegistrations=new Set;executing=!1;constructor(){p(jn,{optional:!0})}execute(){let t=this.sequences.size>0;t&&U(16),this.executing=!0;for(let r of Wm)for(let o of this.sequences)if(!(o.erroredOrDestroyed||!o.hooks[r]))try{o.pipelinedValue=this.ngZone.runOutsideAngular(()=>this.maybeTrace(()=>{let i=o.hooks[r];return i(o.pipelinedValue)},o.snapshot))}catch(i){o.erroredOrDestroyed=!0,this.errorHandler?.handleError(i)}this.executing=!1;for(let r of this.sequences)r.afterRun(),r.once&&(this.sequences.delete(r),r.destroy());for(let r of this.deferredRegistrations)this.sequences.add(r);this.deferredRegistrations.size>0&&this.scheduler.notify(7),this.deferredRegistrations.clear(),t&&U(17)}register(t){let{view:r}=t;r!==void 0?((r[Mn]??=[]).push(t),Cr(r),r[S]|=8192):this.executing?this.deferredRegistrations.add(t):this.addSequence(t)}addSequence(t){this.sequences.add(t),this.scheduler.notify(7)}unregister(t){this.executing&&this.sequences.has(t)?(t.erroredOrDestroyed=!0,t.pipelinedValue=void 0,t.once=!0):(this.sequences.delete(t),this.deferredRegistrations.delete(t))}maybeTrace(t,r){return r?r.run(va.AFTER_NEXT_RENDER,t):t()}static \u0275prov=E({token:e,providedIn:"root",factory:()=>new e})}return e})(),ra=class{impl;hooks;view;once;snapshot;erroredOrDestroyed=!1;pipelinedValue=void 0;unregisterOnDestroy;constructor(n,t,r,o,i,s=null){this.impl=n,this.hooks=t,this.view=r,this.once=o,this.snapshot=s,this.unregisterOnDestroy=i?.onDestroy(()=>this.destroy())}afterRun(){this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,this.snapshot?.dispose(),this.snapshot=null}destroy(){this.impl.unregister(this),this.unregisterOnDestroy?.();let n=this.view?.[Mn];n&&(this.view[Mn]=n.filter(t=>t!==this))}};function wd(e,n){let t=n?.injector??p(se);return Rr("NgAfterNextRender"),Bb(e,t,n,!0)}function Vb(e){return e instanceof Function?[void 0,void 0,e,void 0]:[e.earlyRead,e.write,e.mixedReadWrite,e.read]}function Bb(e,n,t,r){let o=n.get(Cd);o.impl??=n.get(qm);let i=n.get(jn,null,{optional:!0}),s=t?.manualCleanup!==!0?n.get(Qt):null,a=n.get(Ps,null,{optional:!0}),c=new ra(o.impl,Vb(e),a?.view,r,s,i?.snapshot(null));return o.impl.register(c),c}var Id=(()=>{class e{log(t){console.log(t)}warn(t){console.warn(t)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var bd=new D("");function nn(e){return!!e&&typeof e.then=="function"}function _d(e){return!!e&&typeof e.subscribe=="function"}var Td=new D("");function ya(e){return bn([{provide:Td,multi:!0,useValue:e}])}var Sd=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((t,r)=>{this.resolve=t,this.reject=r});appInits=p(Td,{optional:!0})??[];injector=p(se);constructor(){}runInitializers(){if(this.initialized)return;let t=[];for(let o of this.appInits){let i=pe(this.injector,o);if(nn(i))t.push(i);else if(_d(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});t.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(t).then(()=>{r()}).catch(o=>{this.reject(o)}),t.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),jo=new D("");function Zm(){Wc(()=>{let e="";throw new C(600,e)})}function Ym(e){return e.isBoundToModule}var Ub=10;var Nt=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=p(Me);afterRenderManager=p(Cd);zonelessEnabled=p(ks);rootEffectScheduler=p(ul);dirtyFlags=0;tracingSnapshot=null;allTestViews=new Set;autoDetectTestViews=new Set;includeAllTestViews=!1;afterTick=new H;get allViews(){return[...(this.includeAllTestViews?this.allTestViews:this.autoDetectTestViews).keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];internalPendingTask=p(At);get isStable(){return this.internalPendingTask.hasPendingTasksObservable.pipe(N(t=>!t))}constructor(){p(jn,{optional:!0})}whenStable(){let t;return new Promise(r=>{t=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{t.unsubscribe()})}_injector=p(z);_rendererFactory=null;get injector(){return this._injector}bootstrap(t,r){return this.bootstrapImpl(t,r)}bootstrapImpl(t,r,o=se.NULL){return this._injector.get(B).run(()=>{U(10);let s=t instanceof ga;if(!this._injector.get(Sd).done){let m="";throw new C(405,m)}let c;s?c=t:c=this._injector.get(Lo).resolveComponentFactory(t),this.componentTypes.push(c.componentType);let u=Ym(c)?void 0:this._injector.get(Fn),l=r||c.selector,d=c.create(o,[],l,u),h=d.location.nativeElement,f=d.injector.get(bd,null);return f?.registerApplication(h),d.onDestroy(()=>{this.detachView(d.hostView),Ro(this.components,d),f?.unregisterApplication(h)}),this._loadComponent(d),U(11,d),d})}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){U(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(va.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new C(101,!1);let t=R(null);try{this._runningTick=!0,this.synchronize()}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,R(t),this.afterTick.next(),U(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(kn,null,{optional:!0}));let t=0;for(;this.dirtyFlags!==0&&t++<Ub;)U(14),this.synchronizeOnce(),U(15)}synchronizeOnce(){this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush());let t=!1;if(this.dirtyFlags&7){let r=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:o}of this.allViews){if(!r&&!wo(o))continue;let i=r&&!this.zonelessEnabled?0:1;dd(o,i),t=!0}if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}t||(this._rendererFactory?.begin?.(),this._rendererFactory?.end?.()),this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:t})=>wo(t))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(t){let r=t;this._views.push(r),r.attachToAppRef(this)}detachView(t){let r=t;Ro(this._views,r),r.detachFromAppRef()}_loadComponent(t){this.attachView(t.hostView);try{this.tick()}catch(o){this.internalErrorHandler(o)}this.components.push(t),this._injector.get(jo,[]).forEach(o=>o(t))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(t=>t()),this._views.slice().forEach(t=>t.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(t){return this._destroyListeners.push(t),()=>Ro(this._destroyListeners,t)}destroy(){if(this._destroyed)throw new C(406,!1);let t=this._injector;t.destroy&&!t.destroyed&&t.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Ro(e,n){let t=e.indexOf(n);t>-1&&e.splice(t,1)}function Vn(e,n,t,r){let o=L(),i=bo();if(Pn(o,i,n)){let s=ne(),a=Os();Zw(a,o,e,n,t,r)}return Vn}function Qm(e,n,t){let r=L(),o=bo();if(Pn(r,o,n)){let i=ne(),s=Os();sm(s,r,e,n,r[Y],t)}return Qm}function hg(e,n,t,r,o){sd(n,e,t,o?"class":"style",r)}function Md(e,n,t,r){let o=L(),i=ne(),s=Se+e,a=o[Y],c=i.firstCreatePass?Am(s,i,o,n,id,Ts(),t,r):i.data[s],u=Hb(i,o,c,a,n,e);o[s]=u;let l=Co(c);return qt(c,!0),tm(a,u,c),!fa(c)&&_o()&&ha(i,o,u,c),(Ep()===0||l)&&Mr(u,o),Cp(),l&&(da(i,o,c),Kl(i,c,o)),r!==null&&od(o,c),Md}function Ad(){let e=le();Ss()?Ms():(e=e.parent,qt(e,!1));let n=e;Ip(n)&&bp(),wp();let t=ne();return t.firstCreatePass&&Rm(t,n),n.classesWithoutHost!=null&&LC(n)&&hg(t,n,L(),n.classesWithoutHost,!0),n.stylesWithoutHost!=null&&jC(n)&&hg(t,n,L(),n.stylesWithoutHost,!1),Ad}function Da(e,n,t,r){return Md(e,n,t,r),Ad(),Da}var Hb=(e,n,t,r,o,i)=>(To(!0),Xg(r,o,Vp()));function $b(e,n,t,r,o){let i=n.consts,s=Nn(i,r),a=Fo(n,e,8,"ng-container",s);s!==null&&kl(a,s,!0);let c=Nn(i,o);return Ts()&&gd(n,t,a,c,id),a.mergedAttrs=br(a.mergedAttrs,a.attrs),n.queries!==null&&n.queries.elementStart(n,a),a}function Rd(e,n,t){let r=L(),o=ne(),i=e+Se,s=o.firstCreatePass?$b(i,o,r,n,t):o.data[i];qt(s,!0);let a=zb(o,r,s,e);return r[i]=a,_o()&&ha(o,r,a,s),Mr(a,r),Co(s)&&(da(o,r,s),Kl(o,s,r)),t!=null&&od(r,s),Rd}function Nd(){let e=le(),n=ne();return Ss()?Ms():(e=e.parent,qt(e,!1)),n.firstCreatePass&&(ql(n,e),ws(e)&&n.queries.elementEnd(e)),Nd}function Km(e,n,t){return Rd(e,n,t),Nd(),Km}var zb=(e,n,t,r)=>(To(!0),Nw(n[Y],""));function Gb(){return L()}var Vo="en-US";var Wb=Vo;function Jm(e){typeof e=="string"&&(Wb=e.toLowerCase().replace(/_/g,"-"))}function Pe(e,n,t){let r=L(),o=ne(),i=le();return Xm(o,r,r[Y],i,e,n,t),Pe}function Xm(e,n,t,r,o,i,s){let a=!0,c=null;if((r.type&3||s)&&(c??=hl(r,n,i),GI(r,e,n,s,t,o,i,c)&&(a=!1)),a){let u=r.outputs?.[o],l=r.hostDirectiveOutputs?.[o];if(l&&l.length)for(let d=0;d<l.length;d+=2){let h=l[d],f=l[d+1];c??=hl(r,n,i),ig(r,n,h,f,o,c)}if(u&&u.length)for(let d of u)c??=hl(r,n,i),ig(r,n,d,o,o,c)}}function qb(e=1){return jp(e)}function Zb(e,n){let t=null,r=ww(e);for(let o=0;o<n.length;o++){let i=n[o];if(i==="*"){t=o;continue}if(r===null?Jg(e,i,!0):_w(r,i))return o}return t}function Yb(e){let n=L()[Te][ve];if(!n.projection){let t=e?e.length:1,r=n.projection=rp(t,null),o=r.slice(),i=n.child;for(;i!==null;){if(i.type!==128){let s=e?Zb(i,e):0;s!==null&&(o[s]?o[s].projectionNext=i:r[s]=i,o[s]=i)}i=i.next}}}function Qb(e,n=0,t,r,o,i){let s=L(),a=ne(),c=r?e+1:null;c!==null&&Um(s,a,c,r,o,i,null,t);let u=Fo(a,Se+e,16,null,t||null);u.projection===null&&(u.projection=n),Ms();let d=!s[vr]||Yu();s[Te][ve].projection[u.projection]===null&&c!==null?Kb(s,a,c):d&&!fa(u)&&dI(a,s,u)}function Kb(e,n,t){let r=Se+t,o=n.data[r],i=e[r],s=Nl(i,o.tView.ssrId),a=am(e,o,void 0,{dehydratedView:s});bm(i,a,0,Sl(o,s))}function Bo(e,n,t,r){pb(e,n,t,r)}function xd(e,n,t){hb(e,n,t)}function xr(e){let n=L(),t=ne(),r=tl();Rs(r+1);let o=vd(t,r);if(e.dirty&&gp(n)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=vb(n,r);e.reset(i,KC),e.notifyOnChanges()}return!0}return!1}function Or(){return fb(L(),tl())}function js(e,n){return e<<17|n<<2}function Ln(e){return e>>17&32767}function Jb(e){return(e&2)==2}function Xb(e,n){return e&131071|n<<17}function $l(e){return e|2}function _r(e){return(e&131068)>>2}function ml(e,n){return e&-131069|n<<2}function e_(e){return(e&1)===1}function zl(e){return e|1}function t_(e,n,t,r,o,i){let s=i?n.classBindings:n.styleBindings,a=Ln(s),c=_r(s);e[r]=t;let u=!1,l;if(Array.isArray(t)){let d=t;l=d[1],(l===null||gr(d,l)>0)&&(u=!0)}else l=t;if(o)if(c!==0){let h=Ln(e[a+1]);e[r+1]=js(h,a),h!==0&&(e[h+1]=ml(e[h+1],r)),e[a+1]=Xb(e[a+1],r)}else e[r+1]=js(a,0),a!==0&&(e[a+1]=ml(e[a+1],r)),a=r;else e[r+1]=js(c,0),a===0?a=r:e[c+1]=ml(e[c+1],r),c=r;u&&(e[r+1]=$l(e[r+1])),pg(e,l,r,!0),pg(e,l,r,!1),n_(n,l,e,r,i),s=js(a,c),i?n.classBindings=s:n.styleBindings=s}function n_(e,n,t,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof n=="string"&&gr(i,n)>=0&&(t[r+1]=zl(t[r+1]))}function pg(e,n,t,r){let o=e[t+1],i=n===null,s=r?Ln(o):_r(o),a=!1;for(;s!==0&&(a===!1||i);){let c=e[s],u=e[s+1];r_(c,n)&&(a=!0,e[s+1]=r?zl(u):$l(u)),s=r?Ln(u):_r(u)}a&&(e[t+1]=r?$l(o):zl(o))}function r_(e,n){return e===null||n==null||(Array.isArray(e)?e[1]:e)===n?!0:Array.isArray(e)&&typeof n=="string"?gr(e,n)>=0:!1}function ev(e,n,t){return tv(e,n,t,!1),ev}function Uo(e,n){return tv(e,n,null,!0),Uo}function tv(e,n,t,r){let o=L(),i=ne(),s=el(2);if(i.firstUpdatePass&&i_(i,e,s,r),n!==tt&&Pn(o,s,n)){let a=i.data[Zt()];l_(i,a,o,o[Y],e,o[s+1]=d_(n,t),r,s)}}function o_(e,n){return n>=e.expandoStartIndex}function i_(e,n,t,r){let o=e.data;if(o[t+1]===null){let i=o[Zt()],s=o_(e,t);f_(i,r)&&n===null&&!s&&(n=!1),n=s_(o,i,n,r),t_(o,i,n,t,s,r)}}function s_(e,n,t,r){let o=kp(e),i=r?n.residualClasses:n.residualStyles;if(o===null)(r?n.classBindings:n.styleBindings)===0&&(t=vl(null,e,n,t,r),t=ko(t,n.attrs,r),i=null);else{let s=n.directiveStylingLast;if(s===-1||e[s]!==o)if(t=vl(o,e,n,t,r),i===null){let c=a_(e,n,r);c!==void 0&&Array.isArray(c)&&(c=vl(null,e,n,c[1],r),c=ko(c,n.attrs,r),c_(e,n,r,c))}else i=u_(e,n,r)}return i!==void 0&&(r?n.residualClasses=i:n.residualStyles=i),t}function a_(e,n,t){let r=t?n.classBindings:n.styleBindings;if(_r(r)!==0)return e[Ln(r)]}function c_(e,n,t,r){let o=t?n.classBindings:n.styleBindings;e[Ln(o)]=r}function u_(e,n,t){let r,o=n.directiveEnd;for(let i=1+n.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=ko(r,s,t)}return ko(r,n.attrs,t)}function vl(e,n,t,r,o){let i=null,s=t.directiveEnd,a=t.directiveStylingLast;for(a===-1?a=t.directiveStart:a++;a<s&&(i=n[a],r=ko(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(t.directiveStylingLast=a),r}function ko(e,n,t){let r=t?1:2,o=-1;if(n!==null)for(let i=0;i<n.length;i++){let s=n[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),ip(e,s,t?!0:n[++i]))}return e===void 0?null:e}function l_(e,n,t,r,o,i,s,a){if(!(n.type&3))return;let c=e.data,u=c[a+1],l=e_(u)?gg(c,n,t,o,_r(u),s):void 0;if(!oa(l)){oa(i)||Jb(u)&&(i=gg(c,null,t,o,a,s));let d=$u(Zt(),t);hI(r,s,d,o,i)}}function gg(e,n,t,r,o,i){let s=n===null,a;for(;o>0;){let c=e[o],u=Array.isArray(c),l=u?c[1]:c,d=l===null,h=t[o+1];h===tt&&(h=d?xe:void 0);let f=d?Es(h,r):l===r?h:void 0;if(u&&!oa(f)&&(f=Es(c,r)),oa(f)&&(a=f,s))return a;let m=e[o+1];o=s?Ln(m):_r(m)}if(n!==null){let c=i?n.residualClasses:n.residualStyles;c!=null&&(a=Es(c,r))}return a}function oa(e){return e!==void 0}function d_(e,n){return e==null||e===""||(typeof n=="string"?e=e+n:typeof e=="object"&&(e=be(Po(e)))),e}function f_(e,n){return(e.flags&(n?8:16))!==0}function h_(e,n=""){let t=L(),r=ne(),o=e+Se,i=r.firstCreatePass?Fo(r,o,1,n,null):r.data[o],s=p_(r,t,i,n,e);t[o]=s,_o()&&ha(r,t,s,i),qt(i,!1)}var p_=(e,n,t,r,o)=>(To(!0),Aw(n[Y],r));function g_(e,n,t,r=""){return Pn(e,bo(),t)?n+In(t)+r:tt}function m_(e,n,t,r,o,i=""){let s=Ap(),a=zI(e,s,t,o);return el(2),a?n+In(t)+r+In(o)+i:tt}function nv(e){return Od("",e),nv}function Od(e,n,t){let r=L(),o=g_(r,e,n,t);return o!==tt&&ov(r,Zt(),o),Od}function rv(e,n,t,r,o){let i=L(),s=m_(i,e,n,t,r,o);return s!==tt&&ov(i,Zt(),s),rv}function ov(e,n,t){let r=$u(n,e);Rw(e[Y],r,t)}function iv(e,n,t){sl(n)&&(n=n());let r=L(),o=bo();if(Pn(r,o,n)){let i=ne(),s=Os();sm(s,r,e,n,r[Y],t)}return iv}function v_(e,n){let t=sl(e);return t&&e.set(n),t}function sv(e,n){let t=L(),r=ne(),o=le();return Xm(r,t,t[Y],o,e,n),sv}function y_(e,n,t){let r=ne();if(r.firstCreatePass){let o=dt(e);Gl(t,r.data,r.blueprint,o,!0),Gl(n,r.data,r.blueprint,o,!1)}}function Gl(e,n,t,r,o){if(e=de(e),Array.isArray(e))for(let i=0;i<e.length;i++)Gl(e[i],n,t,r,o);else{let i=ne(),s=L(),a=le(),c=En(e)?e:de(e.provide),u=Bu(e),l=a.providerIndexes&1048575,d=a.directiveStart,h=a.providerIndexes>>20;if(En(e)||!e.multi){let f=new On(u,o,v),m=Dl(c,n,o?l:l+h,d);m===-1?(Cl(Ws(a,s),i,c),yl(i,e,n.length),n.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),t.push(f),s.push(f)):(t[m]=f,s[m]=f)}else{let f=Dl(c,n,l+h,d),m=Dl(c,n,l,l+h),y=f>=0&&t[f],w=m>=0&&t[m];if(o&&!w||!o&&!y){Cl(Ws(a,s),i,c);let T=C_(o?E_:D_,t.length,o,r,u);!o&&w&&(t[m].providerFactory=T),yl(i,e,n.length,0),n.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),t.push(T),s.push(T)}else{let T=av(t[o?m:f],u,!o&&r);yl(i,e,f>-1?f:m,T)}!o&&r&&w&&t[m].componentProviders++}}}function yl(e,n,t,r){let o=En(n),i=lp(n);if(o||i){let c=(i?de(n.useClass):n).prototype.ngOnDestroy;if(c){let u=e.destroyHooks||(e.destroyHooks=[]);if(!o&&n.multi){let l=u.indexOf(t);l===-1?u.push(t,[r,c]):u[l+1].push(r,c)}else u.push(t,c)}}}function av(e,n,t){return t&&e.componentProviders++,e.multi.push(n)-1}function Dl(e,n,t,r){for(let o=t;o<r;o++)if(n[o]===e)return o;return-1}function D_(e,n,t,r){return Wl(this.multi,[])}function E_(e,n,t,r){let o=this.multi,i;if(this.providerFactory){let s=this.providerFactory.componentProviders,a=No(t,t[x],this.providerFactory.index,r);i=a.slice(0,s),Wl(o,i);for(let c=s;c<a.length;c++)i.push(a[c])}else i=[],Wl(o,i);return i}function Wl(e,n){for(let t=0;t<e.length;t++){let r=e[t];n.push(r())}return n}function C_(e,n,t,r,o){let i=new On(e,t,v);return i.multi=[],i.index=n,i.componentProviders=0,av(i,o,r&&!t),i}function rn(e,n=[]){return t=>{t.providersResolver=(r,o)=>y_(r,o?o(e):e,n)}}function w_(e,n,t){let r=Mp()+e,o=L();return o[r]===tt?HI(o,r,t?n.call(t):n()):$I(o,r)}var ia=class{ngModuleFactory;componentFactories;constructor(n,t){this.ngModuleFactory=n,this.componentFactories=t}},kd=(()=>{class e{compileModuleSync(t){return new ea(t)}compileModuleAsync(t){return Promise.resolve(this.compileModuleSync(t))}compileModuleAndAllComponentsSync(t){let r=this.compileModuleSync(t),o=Fu(t),i=Qg(o.declarations).reduce((s,a)=>{let c=at(a);return c&&s.push(new Jt(c)),s},[]);return new ia(r,i)}compileModuleAndAllComponentsAsync(t){return Promise.resolve(this.compileModuleAndAllComponentsSync(t))}clearCache(){}clearCacheFor(t){}getModuleId(t){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var I_=(()=>{class e{zone=p(B);changeDetectionScheduler=p(St);applicationRef=p(Nt);applicationErrorHandler=p(Me);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{try{this.applicationRef.dirtyFlags|=1,this.applicationRef._tick()}catch(t){this.applicationErrorHandler(t)}})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function cv({ngZoneFactory:e,ignoreChangesOutsideZone:n,scheduleInRootZone:t}){return e??=()=>new B(A(g({},uv()),{scheduleInRootZone:t})),[{provide:B,useFactory:e},{provide:$t,multi:!0,useFactory:()=>{let r=p(I_,{optional:!0});return()=>r.initialize()}},{provide:$t,multi:!0,useFactory:()=>{let r=p(b_);return()=>{r.initialize()}}},n===!0?{provide:al,useValue:!0}:[],{provide:cl,useValue:t??$m},{provide:Me,useFactory:()=>{let r=p(B),o=p(z),i;return s=>{r.runOutsideAngular(()=>{o.destroyed&&!i?setTimeout(()=>{throw s}):(i??=o.get(Ze),i.handleError(s))})}}}]}function uv(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var b_=(()=>{class e{subscription=new ee;initialized=!1;zone=p(B);pendingTasks=p(At);initialize(){if(this.initialized)return;this.initialized=!0;let t=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(t=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{B.assertNotInAngularZone(),queueMicrotask(()=>{t!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(t),t=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{B.assertInAngularZone(),t??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var lv=(()=>{class e{applicationErrorHandler=p(Me);appRef=p(Nt);taskService=p(At);ngZone=p(B);zonelessEnabled=p(ks);tracing=p(jn,{optional:!0});disableScheduling=p(al,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new ee;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(ta):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(p(cl,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof na||!this.zoneIsDefined)}notify(t){if(!this.zonelessEnabled&&t===5)return;let r=!1;switch(t){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?lg:zm;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(t){return!(this.disableScheduling&&!t||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(ta+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let t=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){this.taskService.remove(t),this.applicationErrorHandler(r)}finally{this.cleanup()}this.useMicrotaskScheduler=!0,lg(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(t)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let t=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(t)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function __(){return typeof $localize<"u"&&$localize.locale||Vo}var Ea=new D("",{providedIn:"root",factory:()=>p(Ea,{optional:!0,skipSelf:!0})||__()});function $e(e){return qh(e)}function kr(e,n){return $i(e,n?.equal)}var dv=class{[he];constructor(n){this[he]=n}destroy(){this[he].destroy()}};var mv=Symbol("InputSignalNode#UNSET"),B_=A(g({},zi),{transformFn:void 0,applyValueToInputSignal(e,n){Xn(e,n)}});function vv(e,n){let t=Object.create(B_);t.value=e,t.transformFn=n?.transform;function r(){if(Kn(t),t.value===mv){let o=null;throw new C(-950,o)}return t.value}return r[he]=t,r}var wa=class{attributeName;constructor(n){this.attributeName=n}__NG_ELEMENT_ID__=()=>en(this.attributeName);toString(){return`HostAttributeToken ${this.attributeName}`}},U_=new D("");U_.__NG_ELEMENT_ID__=e=>{let n=le();if(n===null)throw new C(204,!1);if(n.type&2)return n.value;if(e&8)return null;throw new C(204,!1)};function fv(e,n){return vv(e,n)}function H_(e){return vv(mv,e)}var yv=(fv.required=H_,fv);var Pd=new D(""),$_=new D("");function Ho(e){return!e.moduleRef}function z_(e){let n=Ho(e)?e.r3Injector:e.moduleRef.injector,t=n.get(B);return t.run(()=>{Ho(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=n.get(Me),o;if(t.runOutsideAngular(()=>{o=t.onError.subscribe({next:r})}),Ho(e)){let i=()=>n.destroy(),s=e.platformInjector.get(Pd);s.add(i),n.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(Pd);s.add(i),e.moduleRef.onDestroy(()=>{Ro(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return W_(r,t,()=>{let i=n.get(Sd);return i.runInitializers(),i.donePromise.then(()=>{let s=n.get(Ea,Vo);if(Jm(s||Vo),!n.get($_,!0))return Ho(e)?n.get(Nt):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(Ho(e)){let c=n.get(Nt);return e.rootComponent!==void 0&&c.bootstrap(e.rootComponent),c}else return G_?.(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}var G_;function W_(e,n,t){try{let r=t();return nn(r)?r.catch(o=>{throw n.runOutsideAngular(()=>e(o)),o}):r}catch(r){throw n.runOutsideAngular(()=>e(r)),r}}var Ca=null;function q_(e=[],n){return se.create({name:n,providers:[{provide:vo,useValue:"platform"},{provide:Pd,useValue:new Set([()=>Ca=null])},...e]})}function Z_(e=[]){if(Ca)return Ca;let n=q_(e);return Ca=n,Zm(),Y_(n),n}function Y_(e){let n=e.get(aa,null);pe(e,()=>{n?.forEach(t=>t())})}var ze=(()=>{class e{static __NG_ELEMENT_ID__=Q_}return e})();function Q_(e){return K_(le(),L(),(e&16)===16)}function K_(e,n,t){if(Wt(e)&&!t){let r=Be(e.index,n);return new Kt(r,r)}else if(e.type&175){let r=n[Te];return new Kt(r,n)}return null}var Fd=class{constructor(){}supports(n){return md(n)}create(n){return new Ld(n)}},J_=(e,n)=>n,Ld=class{length=0;collection;_linkedRecords=null;_unlinkedRecords=null;_previousItHead=null;_itHead=null;_itTail=null;_additionsHead=null;_additionsTail=null;_movesHead=null;_movesTail=null;_removalsHead=null;_removalsTail=null;_identityChangesHead=null;_identityChangesTail=null;_trackByFn;constructor(n){this._trackByFn=n||J_}forEachItem(n){let t;for(t=this._itHead;t!==null;t=t._next)n(t)}forEachOperation(n){let t=this._itHead,r=this._removalsHead,o=0,i=null;for(;t||r;){let s=!r||t&&t.currentIndex<hv(r,o,i)?t:r,a=hv(s,o,i),c=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(t=t._next,s.previousIndex==null)o++;else{i||(i=[]);let u=a-o,l=c-o;if(u!=l){for(let h=0;h<u;h++){let f=h<i.length?i[h]:i[h]=0,m=f+h;l<=m&&m<u&&(i[h]=f+1)}let d=s.previousIndex;i[d]=l-u}}a!==c&&n(s,a,c)}}forEachPreviousItem(n){let t;for(t=this._previousItHead;t!==null;t=t._nextPrevious)n(t)}forEachAddedItem(n){let t;for(t=this._additionsHead;t!==null;t=t._nextAdded)n(t)}forEachMovedItem(n){let t;for(t=this._movesHead;t!==null;t=t._nextMoved)n(t)}forEachRemovedItem(n){let t;for(t=this._removalsHead;t!==null;t=t._nextRemoved)n(t)}forEachIdentityChange(n){let t;for(t=this._identityChangesHead;t!==null;t=t._nextIdentityChange)n(t)}diff(n){if(n==null&&(n=[]),!md(n))throw new C(900,!1);return this.check(n)?this:null}onDestroy(){}check(n){this._reset();let t=this._itHead,r=!1,o,i,s;if(Array.isArray(n)){this.length=n.length;for(let a=0;a<this.length;a++)i=n[a],s=this._trackByFn(a,i),t===null||!Object.is(t.trackById,s)?(t=this._mismatch(t,i,s,a),r=!0):(r&&(t=this._verifyReinsertion(t,i,s,a)),Object.is(t.item,i)||this._addIdentityChange(t,i)),t=t._next}else o=0,Nm(n,a=>{s=this._trackByFn(o,a),t===null||!Object.is(t.trackById,s)?(t=this._mismatch(t,a,s,o),r=!0):(r&&(t=this._verifyReinsertion(t,a,s,o)),Object.is(t.item,a)||this._addIdentityChange(t,a)),t=t._next,o++}),this.length=o;return this._truncate(t),this.collection=n,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let n;for(n=this._previousItHead=this._itHead;n!==null;n=n._next)n._nextPrevious=n._next;for(n=this._additionsHead;n!==null;n=n._nextAdded)n.previousIndex=n.currentIndex;for(this._additionsHead=this._additionsTail=null,n=this._movesHead;n!==null;n=n._nextMoved)n.previousIndex=n.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(n,t,r,o){let i;return n===null?i=this._itTail:(i=n._prev,this._remove(n)),n=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),n!==null?(Object.is(n.item,t)||this._addIdentityChange(n,t),this._reinsertAfter(n,i,o)):(n=this._linkedRecords===null?null:this._linkedRecords.get(r,o),n!==null?(Object.is(n.item,t)||this._addIdentityChange(n,t),this._moveAfter(n,i,o)):n=this._addAfter(new jd(t,r),i,o)),n}_verifyReinsertion(n,t,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?n=this._reinsertAfter(i,n._prev,o):n.currentIndex!=o&&(n.currentIndex=o,this._addToMoves(n,o)),n}_truncate(n){for(;n!==null;){let t=n._next;this._addToRemovals(this._unlink(n)),n=t}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(n,t,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(n);let o=n._prevRemoved,i=n._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(n,t,r),this._addToMoves(n,r),n}_moveAfter(n,t,r){return this._unlink(n),this._insertAfter(n,t,r),this._addToMoves(n,r),n}_addAfter(n,t,r){return this._insertAfter(n,t,r),this._additionsTail===null?this._additionsTail=this._additionsHead=n:this._additionsTail=this._additionsTail._nextAdded=n,n}_insertAfter(n,t,r){let o=t===null?this._itHead:t._next;return n._next=o,n._prev=t,o===null?this._itTail=n:o._prev=n,t===null?this._itHead=n:t._next=n,this._linkedRecords===null&&(this._linkedRecords=new Ia),this._linkedRecords.put(n),n.currentIndex=r,n}_remove(n){return this._addToRemovals(this._unlink(n))}_unlink(n){this._linkedRecords!==null&&this._linkedRecords.remove(n);let t=n._prev,r=n._next;return t===null?this._itHead=r:t._next=r,r===null?this._itTail=t:r._prev=t,n}_addToMoves(n,t){return n.previousIndex===t||(this._movesTail===null?this._movesTail=this._movesHead=n:this._movesTail=this._movesTail._nextMoved=n),n}_addToRemovals(n){return this._unlinkedRecords===null&&(this._unlinkedRecords=new Ia),this._unlinkedRecords.put(n),n.currentIndex=null,n._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=n,n._prevRemoved=null):(n._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=n),n}_addIdentityChange(n,t){return n.item=t,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=n:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=n,n}},jd=class{item;trackById;currentIndex=null;previousIndex=null;_nextPrevious=null;_prev=null;_next=null;_prevDup=null;_nextDup=null;_prevRemoved=null;_nextRemoved=null;_nextAdded=null;_nextMoved=null;_nextIdentityChange=null;constructor(n,t){this.item=n,this.trackById=t}},Vd=class{_head=null;_tail=null;add(n){this._head===null?(this._head=this._tail=n,n._nextDup=null,n._prevDup=null):(this._tail._nextDup=n,n._prevDup=this._tail,n._nextDup=null,this._tail=n)}get(n,t){let r;for(r=this._head;r!==null;r=r._nextDup)if((t===null||t<=r.currentIndex)&&Object.is(r.trackById,n))return r;return null}remove(n){let t=n._prevDup,r=n._nextDup;return t===null?this._head=r:t._nextDup=r,r===null?this._tail=t:r._prevDup=t,this._head===null}},Ia=class{map=new Map;put(n){let t=n.trackById,r=this.map.get(t);r||(r=new Vd,this.map.set(t,r)),r.add(n)}get(n,t){let r=n,o=this.map.get(r);return o?o.get(n,t):null}remove(n){let t=n.trackById;return this.map.get(t).remove(n)&&this.map.delete(t),n}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function hv(e,n,t){let r=e.previousIndex;if(r===null)return r;let o=0;return t&&r<t.length&&(o=t[r]),r+n+o}function pv(){return new Bd([new Fd])}var Bd=(()=>{class e{factories;static \u0275prov=E({token:e,providedIn:"root",factory:pv});constructor(t){this.factories=t}static create(t,r){if(r!=null){let o=r.factories.slice();t=t.concat(o)}return new e(t)}static extend(t){return{provide:e,useFactory:r=>e.create(t,r||pv()),deps:[[e,new Dg,new yg]]}}find(t){let r=this.factories.find(o=>o.supports(t));if(r!=null)return r;throw new C(901,!1)}}return e})();function Dv(e){U(8);try{let{rootComponent:n,appProviders:t,platformProviders:r}=e,o=Z_(r),i=[cv({}),{provide:St,useExisting:lv},Up,...t||[]],s=new Oo({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return z_({r3Injector:s.injector,platformInjector:o,rootComponent:n})}catch(n){return Promise.reject(n)}finally{U(9)}}function Bn(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function Ev(e,n){let t=at(e),r=n.elementInjector||mr();return new Jt(t).create(r,n.projectableNodes,n.hostElement,n.environmentInjector,n.directives,n.bindings)}function ba(e){let n=at(e);if(!n)return null;let t=new Jt(n);return{get selector(){return t.selector},get type(){return t.componentType},get inputs(){return t.inputs},get outputs(){return t.outputs},get ngContentSelectors(){return t.ngContentSelectors},get isStandalone(){return n.standalone},get isSignal(){return n.signals}}}var Iv=null;function Ge(){return Iv}function Ud(e){Iv??=e}var $o=class{},zo=(()=>{class e{historyGo(t){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:()=>p(bv),providedIn:"platform"})}return e})(),Hd=new D(""),bv=(()=>{class e extends zo{_location;_history;_doc=p(q);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return Ge().getBaseHref(this._doc)}onPopState(t){let r=Ge().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",t,!1),()=>r.removeEventListener("popstate",t)}onHashChange(t){let r=Ge().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",t,!1),()=>r.removeEventListener("hashchange",t)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(t){this._location.pathname=t}pushState(t,r,o){this._history.pushState(t,r,o)}replaceState(t,r,o){this._history.replaceState(t,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(t=0){this._history.go(t)}getState(){return this._history.state}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:()=>new e,providedIn:"platform"})}return e})();function _a(e,n){return e?n?e.endsWith("/")?n.startsWith("/")?e+n.slice(1):e+n:n.startsWith("/")?e+n:`${e}/${n}`:e:n}function Cv(e){let n=e.search(/#|\?|$/);return e[n-1]==="/"?e.slice(0,n-1)+e.slice(n):e}function nt(e){return e&&e[0]!=="?"?`?${e}`:e}var Ae=(()=>{class e{historyGo(t){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:()=>p(Sa),providedIn:"root"})}return e})(),Ta=new D(""),Sa=(()=>{class e extends Ae{_platformLocation;_baseHref;_removeListenerFns=[];constructor(t,r){super(),this._platformLocation=t,this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??p(q).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(t){this._removeListenerFns.push(this._platformLocation.onPopState(t),this._platformLocation.onHashChange(t))}getBaseHref(){return this._baseHref}prepareExternalUrl(t){return _a(this._baseHref,t)}path(t=!1){let r=this._platformLocation.pathname+nt(this._platformLocation.search),o=this._platformLocation.hash;return o&&t?`${r}${o}`:r}pushState(t,r,o,i){let s=this.prepareExternalUrl(o+nt(i));this._platformLocation.pushState(t,r,s)}replaceState(t,r,o,i){let s=this.prepareExternalUrl(o+nt(i));this._platformLocation.replaceState(t,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(t=0){this._platformLocation.historyGo?.(t)}static \u0275fac=function(r){return new(r||e)(I(zo),I(Ta,8))};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),rt=(()=>{class e{_subject=new H;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(t){this._locationStrategy=t;let r=this._locationStrategy.getBaseHref();this._basePath=tT(Cv(wv(r))),this._locationStrategy.onPopState(o=>{this._subject.next({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(t=!1){return this.normalize(this._locationStrategy.path(t))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(t,r=""){return this.path()==this.normalize(t+nt(r))}normalize(t){return e.stripTrailingSlash(eT(this._basePath,wv(t)))}prepareExternalUrl(t){return t&&t[0]!=="/"&&(t="/"+t),this._locationStrategy.prepareExternalUrl(t)}go(t,r="",o=null){this._locationStrategy.pushState(o,"",t,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(t+nt(r)),o)}replaceState(t,r="",o=null){this._locationStrategy.replaceState(o,"",t,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(t+nt(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(t=0){this._locationStrategy.historyGo?.(t)}onUrlChange(t){return this._urlChangeListeners.push(t),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(t);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(t="",r){this._urlChangeListeners.forEach(o=>o(t,r))}subscribe(t,r,o){return this._subject.subscribe({next:t,error:r??void 0,complete:o??void 0})}static normalizeQueryParams=nt;static joinWithSlash=_a;static stripTrailingSlash=Cv;static \u0275fac=function(r){return new(r||e)(I(Ae))};static \u0275prov=E({token:e,factory:()=>X_(),providedIn:"root"})}return e})();function X_(){return new rt(I(Ae))}function eT(e,n){if(!e||!n.startsWith(e))return n;let t=n.substring(e.length);return t===""||["/",";","?","#"].includes(t[0])?t:n}function wv(e){return e.replace(/\/index.html$/,"")}function tT(e){if(new RegExp("^(https?:)?//").test(e)){let[,t]=e.split(/\/\/[^\/]+/);return t}return e}var $d=(()=>{class e extends Ae{_platformLocation;_baseHref="";_removeListenerFns=[];constructor(t,r){super(),this._platformLocation=t,r!=null&&(this._baseHref=r)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(t){this._removeListenerFns.push(this._platformLocation.onPopState(t),this._platformLocation.onHashChange(t))}getBaseHref(){return this._baseHref}path(t=!1){let r=this._platformLocation.hash??"#";return r.length>0?r.substring(1):r}prepareExternalUrl(t){let r=_a(this._baseHref,t);return r.length>0?"#"+r:r}pushState(t,r,o,i){let s=this.prepareExternalUrl(o+nt(i))||this._platformLocation.pathname;this._platformLocation.pushState(t,r,s)}replaceState(t,r,o,i){let s=this.prepareExternalUrl(o+nt(i))||this._platformLocation.pathname;this._platformLocation.replaceState(t,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(t=0){this._platformLocation.historyGo?.(t)}static \u0275fac=function(r){return new(r||e)(I(zo),I(Ta,8))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})();var Ma=class{$implicit;ngForOf;index;count;constructor(n,t,r,o){this.$implicit=n,this.ngForOf=t,this.index=r,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},Sv=(()=>{class e{_viewContainer;_template;_differs;set ngForOf(t){this._ngForOf=t,this._ngForOfDirty=!0}set ngForTrackBy(t){this._trackByFn=t}get ngForTrackBy(){return this._trackByFn}_ngForOf=null;_ngForOfDirty=!0;_differ=null;_trackByFn;constructor(t,r,o){this._viewContainer=t,this._template=r,this._differs=o}set ngForTemplate(t){t&&(this._template=t)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let t=this._ngForOf;!this._differ&&t&&(this._differ=this._differs.find(t).create(this.ngForTrackBy))}if(this._differ){let t=this._differ.diff(this._ngForOf);t&&this._applyChanges(t)}}_applyChanges(t){let r=this._viewContainer;t.forEachOperation((o,i,s)=>{if(o.previousIndex==null)r.createEmbeddedView(this._template,new Ma(o.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)r.remove(i===null?void 0:i);else if(i!==null){let a=r.get(i);r.move(a,s),_v(a,o)}});for(let o=0,i=r.length;o<i;o++){let a=r.get(o).context;a.index=o,a.count=i,a.ngForOf=this._ngForOf}t.forEachIdentityChange(o=>{let i=r.get(o.currentIndex);_v(i,o)})}static ngTemplateContextGuard(t,r){return!0}static \u0275fac=function(r){return new(r||e)(v(Ue),v(et),v(Bd))};static \u0275dir=j({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"}})}return e})();function _v(e,n){e.context.$implicit=n.item}var nT=(()=>{class e{_viewContainer;_context=new Aa;_thenTemplateRef=null;_elseTemplateRef=null;_thenViewRef=null;_elseViewRef=null;constructor(t,r){this._viewContainer=t,this._thenTemplateRef=r}set ngIf(t){this._context.$implicit=this._context.ngIf=t,this._updateView()}set ngIfThen(t){Tv(t,!1),this._thenTemplateRef=t,this._thenViewRef=null,this._updateView()}set ngIfElse(t){Tv(t,!1),this._elseTemplateRef=t,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngIfUseIfTypeGuard;static ngTemplateGuard_ngIf;static ngTemplateContextGuard(t,r){return!0}static \u0275fac=function(r){return new(r||e)(v(Ue),v(et))};static \u0275dir=j({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}})}return e})(),Aa=class{$implicit=null;ngIf=null};function Tv(e,n){if(e&&!e.createEmbeddedView)throw new C(2020,!1)}var rT=(()=>{class e{_viewContainerRef;_viewRef=null;ngTemplateOutletContext=null;ngTemplateOutlet=null;ngTemplateOutletInjector=null;constructor(t){this._viewContainerRef=t}ngOnChanges(t){if(this._shouldRecreateView(t)){let r=this._viewContainerRef;if(this._viewRef&&r.remove(r.indexOf(this._viewRef)),!this.ngTemplateOutlet){this._viewRef=null;return}let o=this._createContextForwardProxy();this._viewRef=r.createEmbeddedView(this.ngTemplateOutlet,o,{injector:this.ngTemplateOutletInjector??void 0})}}_shouldRecreateView(t){return!!t.ngTemplateOutlet||!!t.ngTemplateOutletInjector}_createContextForwardProxy(){return new Proxy({},{set:(t,r,o)=>this.ngTemplateOutletContext?Reflect.set(this.ngTemplateOutletContext,r,o):!1,get:(t,r,o)=>{if(this.ngTemplateOutletContext)return Reflect.get(this.ngTemplateOutletContext,r,o)}})}static \u0275fac=function(r){return new(r||e)(v(Ue))};static \u0275dir=j({type:e,selectors:[["","ngTemplateOutlet",""]],inputs:{ngTemplateOutletContext:"ngTemplateOutletContext",ngTemplateOutlet:"ngTemplateOutlet",ngTemplateOutletInjector:"ngTemplateOutletInjector"},features:[De]})}return e})();var Mv=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=ke({type:e});static \u0275inj=_e({})}return e})();function Go(e,n){n=encodeURIComponent(n);for(let t of e.split(";")){let r=t.indexOf("="),[o,i]=r==-1?[t,""]:[t.slice(0,r),t.slice(r+1)];if(o.trim()===n)return decodeURIComponent(i)}return null}var Un=class{};var Av="browser",iT="server";function Rv(e){return e===iT}var Nv=(()=>{class e{static \u0275prov=E({token:e,providedIn:"root",factory:()=>new zd(p(q),window)})}return e})(),zd=class{document;window;offset=()=>[0,0];constructor(n,t){this.document=n,this.window=t}setOffset(n){Array.isArray(n)?this.offset=()=>n:this.offset=n}getScrollPosition(){return[this.window.scrollX,this.window.scrollY]}scrollToPosition(n,t){this.window.scrollTo(A(g({},t),{left:n[0],top:n[1]}))}scrollToAnchor(n,t){let r=sT(this.document,n);r&&(this.scrollToElement(r,t),r.focus())}setHistoryScrollRestoration(n){try{this.window.history.scrollRestoration=n}catch{console.warn(pr(2400,!1))}}scrollToElement(n,t){let r=n.getBoundingClientRect(),o=r.left+this.window.pageXOffset,i=r.top+this.window.pageYOffset,s=this.offset();this.window.scrollTo(A(g({},t),{left:o-s[0],top:i-s[1]}))}};function sT(e,n){let t=e.getElementById(n)||e.getElementsByName(n)[0];if(t)return t;if(typeof e.createTreeWalker=="function"&&e.body&&typeof e.body.attachShadow=="function"){let r=e.createTreeWalker(e.body,NodeFilter.SHOW_ELEMENT),o=r.currentNode;for(;o;){let i=o.shadowRoot;if(i){let s=i.getElementById(n)||i.querySelector(`[name="${n}"]`);if(s)return s}o=r.nextNode()}}return null}var xa=new D(""),Zd=(()=>{class e{_zone;_plugins;_eventNameToPlugin=new Map;constructor(t,r){this._zone=r,t.forEach(o=>{o.manager=this}),this._plugins=t.slice().reverse()}addEventListener(t,r,o,i){return this._findPluginFor(r).addEventListener(t,r,o,i)}getZone(){return this._zone}_findPluginFor(t){let r=this._eventNameToPlugin.get(t);if(r)return r;if(r=this._plugins.find(i=>i.supports(t)),!r)throw new C(5101,!1);return this._eventNameToPlugin.set(t,r),r}static \u0275fac=function(r){return new(r||e)(I(xa),I(B))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})(),Wo=class{_doc;constructor(n){this._doc=n}manager},Ra="ng-app-id";function Ov(e){for(let n of e)n.remove()}function kv(e,n){let t=n.createElement("style");return t.textContent=e,t}function aT(e,n,t,r){let o=e.head?.querySelectorAll(`style[${Ra}="${n}"],link[${Ra}="${n}"]`);if(o)for(let i of o)i.removeAttribute(Ra),i instanceof HTMLLinkElement?r.set(i.href.slice(i.href.lastIndexOf("/")+1),{usage:0,elements:[i]}):i.textContent&&t.set(i.textContent,{usage:0,elements:[i]})}function Wd(e,n){let t=n.createElement("link");return t.setAttribute("rel","stylesheet"),t.setAttribute("href",e),t}var Yd=(()=>{class e{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;isServer;constructor(t,r,o,i={}){this.doc=t,this.appId=r,this.nonce=o,this.isServer=Rv(i),aT(t,r,this.inline,this.external),this.hosts.add(t.head)}addStyles(t,r){for(let o of t)this.addUsage(o,this.inline,kv);r?.forEach(o=>this.addUsage(o,this.external,Wd))}removeStyles(t,r){for(let o of t)this.removeUsage(o,this.inline);r?.forEach(o=>this.removeUsage(o,this.external))}addUsage(t,r,o){let i=r.get(t);i?i.usage++:r.set(t,{usage:1,elements:[...this.hosts].map(s=>this.addElement(s,o(t,this.doc)))})}removeUsage(t,r){let o=r.get(t);o&&(o.usage--,o.usage<=0&&(Ov(o.elements),r.delete(t)))}ngOnDestroy(){for(let[,{elements:t}]of[...this.inline,...this.external])Ov(t);this.hosts.clear()}addHost(t){this.hosts.add(t);for(let[r,{elements:o}]of this.inline)o.push(this.addElement(t,kv(r,this.doc)));for(let[r,{elements:o}]of this.external)o.push(this.addElement(t,Wd(r,this.doc)))}removeHost(t){this.hosts.delete(t)}addElement(t,r){return this.nonce&&r.setAttribute("nonce",this.nonce),this.isServer&&r.setAttribute(Ra,this.appId),t.appendChild(r)}static \u0275fac=function(r){return new(r||e)(I(q),I(sa),I(ca,8),I(Ar))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})(),Gd={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},Qd=/%COMP%/g;var Fv="%COMP%",cT=`_nghost-${Fv}`,uT=`_ngcontent-${Fv}`,lT=!0,dT=new D("",{providedIn:"root",factory:()=>lT});function fT(e){return uT.replace(Qd,e)}function hT(e){return cT.replace(Qd,e)}function Lv(e,n){return n.map(t=>t.replace(Qd,e))}var Kd=(()=>{class e{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(t,r,o,i,s,a,c,u=null,l=null){this.eventManager=t,this.sharedStylesHost=r,this.appId=o,this.removeStylesOnCompDestroy=i,this.doc=s,this.platformId=a,this.ngZone=c,this.nonce=u,this.tracingService=l,this.platformIsServer=!1,this.defaultRenderer=new qo(t,s,c,this.platformIsServer,this.tracingService)}createRenderer(t,r){if(!t||!r)return this.defaultRenderer;let o=this.getOrCreateRenderer(t,r);return o instanceof Na?o.applyToHost(t):o instanceof Zo&&o.applyStyles(),o}getOrCreateRenderer(t,r){let o=this.rendererByCompId,i=o.get(r.id);if(!i){let s=this.doc,a=this.ngZone,c=this.eventManager,u=this.sharedStylesHost,l=this.removeStylesOnCompDestroy,d=this.platformIsServer,h=this.tracingService;switch(r.encapsulation){case Rt.Emulated:i=new Na(c,u,r,this.appId,l,s,a,d,h);break;case Rt.ShadowDom:return new qd(c,u,t,r,s,a,this.nonce,d,h);default:i=new Zo(c,u,r,l,s,a,d,h);break}o.set(r.id,i)}return i}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(t){this.rendererByCompId.delete(t)}static \u0275fac=function(r){return new(r||e)(I(Zd),I(Yd),I(sa),I(dT),I(q),I(Ar),I(B),I(ca),I(jn,8))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})(),qo=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(n,t,r,o,i){this.eventManager=n,this.doc=t,this.ngZone=r,this.platformIsServer=o,this.tracingService=i}destroy(){}destroyNode=null;createElement(n,t){return t?this.doc.createElementNS(Gd[t]||t,n):this.doc.createElement(n)}createComment(n){return this.doc.createComment(n)}createText(n){return this.doc.createTextNode(n)}appendChild(n,t){(Pv(n)?n.content:n).appendChild(t)}insertBefore(n,t,r){n&&(Pv(n)?n.content:n).insertBefore(t,r)}removeChild(n,t){t.remove()}selectRootElement(n,t){let r=typeof n=="string"?this.doc.querySelector(n):n;if(!r)throw new C(-5104,!1);return t||(r.textContent=""),r}parentNode(n){return n.parentNode}nextSibling(n){return n.nextSibling}setAttribute(n,t,r,o){if(o){t=o+":"+t;let i=Gd[o];i?n.setAttributeNS(i,t,r):n.setAttribute(t,r)}else n.setAttribute(t,r)}removeAttribute(n,t,r){if(r){let o=Gd[r];o?n.removeAttributeNS(o,t):n.removeAttribute(`${r}:${t}`)}else n.removeAttribute(t)}addClass(n,t){n.classList.add(t)}removeClass(n,t){n.classList.remove(t)}setStyle(n,t,r,o){o&(ht.DashCase|ht.Important)?n.style.setProperty(t,r,o&ht.Important?"important":""):n.style[t]=r}removeStyle(n,t,r){r&ht.DashCase?n.style.removeProperty(t):n.style[t]=""}setProperty(n,t,r){n!=null&&(n[t]=r)}setValue(n,t){n.nodeValue=t}listen(n,t,r,o){if(typeof n=="string"&&(n=Ge().getGlobalEventTarget(this.doc,n),!n))throw new C(5102,!1);let i=this.decoratePreventDefault(r);return this.tracingService?.wrapEventListener&&(i=this.tracingService.wrapEventListener(n,t,i)),this.eventManager.addEventListener(n,t,i,o)}decoratePreventDefault(n){return t=>{if(t==="__ngUnwrap__")return n;n(t)===!1&&t.preventDefault()}}};function Pv(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var qd=class extends qo{sharedStylesHost;hostEl;shadowRoot;constructor(n,t,r,o,i,s,a,c,u){super(n,i,s,c,u),this.sharedStylesHost=t,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let l=o.styles;l=Lv(o.id,l);for(let h of l){let f=document.createElement("style");a&&f.setAttribute("nonce",a),f.textContent=h,this.shadowRoot.appendChild(f)}let d=o.getExternalStyles?.();if(d)for(let h of d){let f=Wd(h,i);a&&f.setAttribute("nonce",a),this.shadowRoot.appendChild(f)}}nodeOrShadowRoot(n){return n===this.hostEl?this.shadowRoot:n}appendChild(n,t){return super.appendChild(this.nodeOrShadowRoot(n),t)}insertBefore(n,t,r){return super.insertBefore(this.nodeOrShadowRoot(n),t,r)}removeChild(n,t){return super.removeChild(null,t)}parentNode(n){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(n)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},Zo=class extends qo{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(n,t,r,o,i,s,a,c,u){super(n,i,s,a,c),this.sharedStylesHost=t,this.removeStylesOnCompDestroy=o;let l=r.styles;this.styles=u?Lv(u,l):l,this.styleUrls=r.getExternalStyles?.(u)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},Na=class extends Zo{contentAttr;hostAttr;constructor(n,t,r,o,i,s,a,c,u){let l=o+"-"+r.id;super(n,t,r,i,s,a,c,u,l),this.contentAttr=fT(l),this.hostAttr=hT(l)}applyToHost(n){this.applyStyles(),this.setAttribute(n,this.hostAttr,"")}createElement(n,t){let r=super.createElement(n,t);return super.setAttribute(r,this.contentAttr,""),r}};var Oa=class e extends $o{supportsDOMEvents=!0;static makeCurrent(){Ud(new e)}onAndCancel(n,t,r,o){return n.addEventListener(t,r,o),()=>{n.removeEventListener(t,r,o)}}dispatchEvent(n,t){n.dispatchEvent(t)}remove(n){n.remove()}createElement(n,t){return t=t||this.getDefaultDocument(),t.createElement(n)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(n){return n.nodeType===Node.ELEMENT_NODE}isShadowRoot(n){return n instanceof DocumentFragment}getGlobalEventTarget(n,t){return t==="window"?window:t==="document"?n:t==="body"?n.body:null}getBaseHref(n){let t=pT();return t==null?null:gT(t)}resetBaseElement(){Yo=null}getUserAgent(){return window.navigator.userAgent}getCookie(n){return Go(document.cookie,n)}},Yo=null;function pT(){return Yo=Yo||document.head.querySelector("base"),Yo?Yo.getAttribute("href"):null}function gT(e){return new URL(e,document.baseURI).pathname}var mT=(()=>{class e{build(){return new XMLHttpRequest}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})(),Vv=(()=>{class e extends Wo{constructor(t){super(t)}supports(t){return!0}addEventListener(t,r,o,i){return t.addEventListener(r,o,i),()=>this.removeEventListener(t,r,o,i)}removeEventListener(t,r,o,i){return t.removeEventListener(r,o,i)}static \u0275fac=function(r){return new(r||e)(I(q))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})(),jv=["alt","control","meta","shift"],vT={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},yT={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},Bv=(()=>{class e extends Wo{constructor(t){super(t)}supports(t){return e.parseEventName(t)!=null}addEventListener(t,r,o,i){let s=e.parseEventName(r),a=e.eventCallback(s.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>Ge().onAndCancel(t,s.domEventName,a,i))}static parseEventName(t){let r=t.toLowerCase().split("."),o=r.shift();if(r.length===0||!(o==="keydown"||o==="keyup"))return null;let i=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),jv.forEach(u=>{let l=r.indexOf(u);l>-1&&(r.splice(l,1),s+=u+".")}),s+=i,r.length!=0||i.length===0)return null;let c={};return c.domEventName=o,c.fullKey=s,c}static matchEventFullKeyCode(t,r){let o=vT[t.key]||t.key,i="";return r.indexOf("code.")>-1&&(o=t.code,i="code."),o==null||!o?!1:(o=o.toLowerCase(),o===" "?o="space":o==="."&&(o="dot"),jv.forEach(s=>{if(s!==o){let a=yT[s];a(t)&&(i+=s+".")}}),i+=o,i===r)}static eventCallback(t,r,o){return i=>{e.matchEventFullKeyCode(i,t)&&o.runGuarded(()=>r(i))}}static _normalizeKey(t){return t==="esc"?"escape":t}static \u0275fac=function(r){return new(r||e)(I(q))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})();function DT(e,n){return Dv(g({rootComponent:e},ET(n)))}function ET(e){return{appProviders:[..._T,...e?.providers??[]],platformProviders:bT}}function CT(){Oa.makeCurrent()}function wT(){return new Ze}function IT(){return Ql(document),document}var bT=[{provide:Ar,useValue:Av},{provide:aa,useValue:CT,multi:!0},{provide:q,useFactory:IT}];var _T=[{provide:vo,useValue:"root"},{provide:Ze,useFactory:wT},{provide:xa,useClass:Vv,multi:!0,deps:[q]},{provide:xa,useClass:Bv,multi:!0,deps:[q]},Kd,Yd,Zd,{provide:kn,useExisting:Kd},{provide:Un,useClass:mT},[]];var Fr=class{},Qo=class{},on=class e{headers;normalizedNames=new Map;lazyInit;lazyUpdate=null;constructor(n){n?typeof n=="string"?this.lazyInit=()=>{this.headers=new Map,n.split(`
`).forEach(t=>{let r=t.indexOf(":");if(r>0){let o=t.slice(0,r),i=t.slice(r+1).trim();this.addHeaderEntry(o,i)}})}:typeof Headers<"u"&&n instanceof Headers?(this.headers=new Map,n.forEach((t,r)=>{this.addHeaderEntry(r,t)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(n).forEach(([t,r])=>{this.setHeaderEntries(t,r)})}:this.headers=new Map}has(n){return this.init(),this.headers.has(n.toLowerCase())}get(n){this.init();let t=this.headers.get(n.toLowerCase());return t&&t.length>0?t[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(n){return this.init(),this.headers.get(n.toLowerCase())||null}append(n,t){return this.clone({name:n,value:t,op:"a"})}set(n,t){return this.clone({name:n,value:t,op:"s"})}delete(n,t){return this.clone({name:n,value:t,op:"d"})}maybeSetNormalizedName(n,t){this.normalizedNames.has(t)||this.normalizedNames.set(t,n)}init(){this.lazyInit&&(this.lazyInit instanceof e?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(n=>this.applyUpdate(n)),this.lazyUpdate=null))}copyFrom(n){n.init(),Array.from(n.headers.keys()).forEach(t=>{this.headers.set(t,n.headers.get(t)),this.normalizedNames.set(t,n.normalizedNames.get(t))})}clone(n){let t=new e;return t.lazyInit=this.lazyInit&&this.lazyInit instanceof e?this.lazyInit:this,t.lazyUpdate=(this.lazyUpdate||[]).concat([n]),t}applyUpdate(n){let t=n.name.toLowerCase();switch(n.op){case"a":case"s":let r=n.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(n.name,t);let o=(n.op==="a"?this.headers.get(t):void 0)||[];o.push(...r),this.headers.set(t,o);break;case"d":let i=n.value;if(!i)this.headers.delete(t),this.normalizedNames.delete(t);else{let s=this.headers.get(t);if(!s)return;s=s.filter(a=>i.indexOf(a)===-1),s.length===0?(this.headers.delete(t),this.normalizedNames.delete(t)):this.headers.set(t,s)}break}}addHeaderEntry(n,t){let r=n.toLowerCase();this.maybeSetNormalizedName(n,r),this.headers.has(r)?this.headers.get(r).push(t):this.headers.set(r,[t])}setHeaderEntries(n,t){let r=(Array.isArray(t)?t:[t]).map(i=>i.toString()),o=n.toLowerCase();this.headers.set(o,r),this.maybeSetNormalizedName(n,o)}forEach(n){this.init(),Array.from(this.normalizedNames.keys()).forEach(t=>n(this.normalizedNames.get(t),this.headers.get(t)))}};var Pa=class{encodeKey(n){return Uv(n)}encodeValue(n){return Uv(n)}decodeKey(n){return decodeURIComponent(n)}decodeValue(n){return decodeURIComponent(n)}};function TT(e,n){let t=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(o=>{let i=o.indexOf("="),[s,a]=i==-1?[n.decodeKey(o),""]:[n.decodeKey(o.slice(0,i)),n.decodeValue(o.slice(i+1))],c=t.get(s)||[];c.push(a),t.set(s,c)}),t}var ST=/%(\d[a-f0-9])/gi,MT={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function Uv(e){return encodeURIComponent(e).replace(ST,(n,t)=>MT[t]??n)}function ka(e){return`${e}`}var xt=class e{map;encoder;updates=null;cloneFrom=null;constructor(n={}){if(this.encoder=n.encoder||new Pa,n.fromString){if(n.fromObject)throw new C(2805,!1);this.map=TT(n.fromString,this.encoder)}else n.fromObject?(this.map=new Map,Object.keys(n.fromObject).forEach(t=>{let r=n.fromObject[t],o=Array.isArray(r)?r.map(ka):[ka(r)];this.map.set(t,o)})):this.map=null}has(n){return this.init(),this.map.has(n)}get(n){this.init();let t=this.map.get(n);return t?t[0]:null}getAll(n){return this.init(),this.map.get(n)||null}keys(){return this.init(),Array.from(this.map.keys())}append(n,t){return this.clone({param:n,value:t,op:"a"})}appendAll(n){let t=[];return Object.keys(n).forEach(r=>{let o=n[r];Array.isArray(o)?o.forEach(i=>{t.push({param:r,value:i,op:"a"})}):t.push({param:r,value:o,op:"a"})}),this.clone(t)}set(n,t){return this.clone({param:n,value:t,op:"s"})}delete(n,t){return this.clone({param:n,value:t,op:"d"})}toString(){return this.init(),this.keys().map(n=>{let t=this.encoder.encodeKey(n);return this.map.get(n).map(r=>t+"="+this.encoder.encodeValue(r)).join("&")}).filter(n=>n!=="").join("&")}clone(n){let t=new e({encoder:this.encoder});return t.cloneFrom=this.cloneFrom||this,t.updates=(this.updates||[]).concat(n),t}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(n=>this.map.set(n,this.cloneFrom.map.get(n))),this.updates.forEach(n=>{switch(n.op){case"a":case"s":let t=(n.op==="a"?this.map.get(n.param):void 0)||[];t.push(ka(n.value)),this.map.set(n.param,t);break;case"d":if(n.value!==void 0){let r=this.map.get(n.param)||[],o=r.indexOf(ka(n.value));o!==-1&&r.splice(o,1),r.length>0?this.map.set(n.param,r):this.map.delete(n.param)}else{this.map.delete(n.param);break}}}),this.cloneFrom=this.updates=null)}};var Fa=class{map=new Map;set(n,t){return this.map.set(n,t),this}get(n){return this.map.has(n)||this.map.set(n,n.defaultValue()),this.map.get(n)}delete(n){return this.map.delete(n),this}has(n){return this.map.has(n)}keys(){return this.map.keys()}};function AT(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function Hv(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function $v(e){return typeof Blob<"u"&&e instanceof Blob}function zv(e){return typeof FormData<"u"&&e instanceof FormData}function RT(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}var Gv="Content-Type",Wv="Accept",Zv="X-Request-URL",Yv="text/plain",Qv="application/json",NT=`${Qv}, ${Yv}, */*`,Pr=class e{url;body=null;headers;context;reportProgress=!1;withCredentials=!1;keepalive=!1;responseType="json";method;params;urlWithParams;transferCache;constructor(n,t,r,o){this.url=t,this.method=n.toUpperCase();let i;if(AT(this.method)||o?(this.body=r!==void 0?r:null,i=o):i=r,i&&(this.reportProgress=!!i.reportProgress,this.withCredentials=!!i.withCredentials,this.keepalive=!!i.keepalive,i.responseType&&(this.responseType=i.responseType),i.headers&&(this.headers=i.headers),i.context&&(this.context=i.context),i.params&&(this.params=i.params),this.transferCache=i.transferCache),this.headers??=new on,this.context??=new Fa,!this.params)this.params=new xt,this.urlWithParams=t;else{let s=this.params.toString();if(s.length===0)this.urlWithParams=t;else{let a=t.indexOf("?"),c=a===-1?"?":a<t.length-1?"&":"";this.urlWithParams=t+c+s}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||Hv(this.body)||$v(this.body)||zv(this.body)||RT(this.body)?this.body:this.body instanceof xt?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||zv(this.body)?null:$v(this.body)?this.body.type||null:Hv(this.body)?null:typeof this.body=="string"?Yv:this.body instanceof xt?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?Qv:null}clone(n={}){let t=n.method||this.method,r=n.url||this.url,o=n.responseType||this.responseType,i=n.keepalive??this.keepalive,s=n.transferCache??this.transferCache,a=n.body!==void 0?n.body:this.body,c=n.withCredentials??this.withCredentials,u=n.reportProgress??this.reportProgress,l=n.headers||this.headers,d=n.params||this.params,h=n.context??this.context;return n.setHeaders!==void 0&&(l=Object.keys(n.setHeaders).reduce((f,m)=>f.set(m,n.setHeaders[m]),l)),n.setParams&&(d=Object.keys(n.setParams).reduce((f,m)=>f.set(m,n.setParams[m]),d)),new e(t,r,a,{params:d,headers:l,context:h,reportProgress:u,responseType:o,withCredentials:c,transferCache:s,keepalive:i})}},Hn=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(Hn||{}),Lr=class{headers;status;statusText;url;ok;type;constructor(n,t=200,r="OK"){this.headers=n.headers||new on,this.status=n.status!==void 0?n.status:t,this.statusText=n.statusText||r,this.url=n.url||null,this.ok=this.status>=200&&this.status<300}},La=class e extends Lr{constructor(n={}){super(n)}type=Hn.ResponseHeader;clone(n={}){return new e({headers:n.headers||this.headers,status:n.status!==void 0?n.status:this.status,statusText:n.statusText||this.statusText,url:n.url||this.url||void 0})}},Ko=class e extends Lr{body;constructor(n={}){super(n),this.body=n.body!==void 0?n.body:null}type=Hn.Response;clone(n={}){return new e({body:n.body!==void 0?n.body:this.body,headers:n.headers||this.headers,status:n.status!==void 0?n.status:this.status,statusText:n.statusText||this.statusText,url:n.url||this.url||void 0})}},Jo=class extends Lr{name="HttpErrorResponse";message;error;ok=!1;constructor(n){super(n,0,"Unknown Error"),this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${n.url||"(unknown url)"}`:this.message=`Http failure response for ${n.url||"(unknown url)"}: ${n.status} ${n.statusText}`,this.error=n.error||null}},xT=200,OT=204;function Jd(e,n){return{body:n,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials,transferCache:e.transferCache,keepalive:e.keepalive}}var Kv=(()=>{class e{handler;constructor(t){this.handler=t}request(t,r,o={}){let i;if(t instanceof Pr)i=t;else{let c;o.headers instanceof on?c=o.headers:c=new on(o.headers);let u;o.params&&(o.params instanceof xt?u=o.params:u=new xt({fromObject:o.params})),i=new Pr(t,r,o.body!==void 0?o.body:null,{headers:c,context:o.context,params:u,reportProgress:o.reportProgress,responseType:o.responseType||"json",withCredentials:o.withCredentials,transferCache:o.transferCache,keepalive:o.keepalive})}let s=b(i).pipe(st(c=>this.handler.handle(c)));if(t instanceof Pr||o.observe==="events")return s;let a=s.pipe(oe(c=>c instanceof Ko));switch(o.observe||"body"){case"body":switch(i.responseType){case"arraybuffer":return a.pipe(N(c=>{if(c.body!==null&&!(c.body instanceof ArrayBuffer))throw new C(2806,!1);return c.body}));case"blob":return a.pipe(N(c=>{if(c.body!==null&&!(c.body instanceof Blob))throw new C(2807,!1);return c.body}));case"text":return a.pipe(N(c=>{if(c.body!==null&&typeof c.body!="string")throw new C(2808,!1);return c.body}));case"json":default:return a.pipe(N(c=>c.body))}case"response":return a;default:throw new C(2809,!1)}}delete(t,r={}){return this.request("DELETE",t,r)}get(t,r={}){return this.request("GET",t,r)}head(t,r={}){return this.request("HEAD",t,r)}jsonp(t,r){return this.request("JSONP",t,{params:new xt().append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(t,r={}){return this.request("OPTIONS",t,r)}patch(t,r,o={}){return this.request("PATCH",t,Jd(o,r))}post(t,r,o={}){return this.request("POST",t,Jd(o,r))}put(t,r,o={}){return this.request("PUT",t,Jd(o,r))}static \u0275fac=function(r){return new(r||e)(I(Fr))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})();var kT=new D("");function Jv(e,n){return n(e)}function PT(e,n){return(t,r)=>n.intercept(t,{handle:o=>e(o,r)})}function FT(e,n,t){return(r,o)=>pe(t,()=>n(r,i=>e(i,o)))}var Xv=new D(""),Va=new D(""),ey=new D(""),ef=new D("",{providedIn:"root",factory:()=>!0});function LT(){let e=null;return(n,t)=>{e===null&&(e=(p(Xv,{optional:!0})??[]).reduceRight(PT,Jv));let r=p(So);if(p(ef)){let i=r.add();return e(n,t).pipe(Bt(i))}else return e(n,t)}}var ja=(()=>{class e extends Fr{backend;injector;chain=null;pendingTasks=p(So);contributeToStability=p(ef);constructor(t,r){super(),this.backend=t,this.injector=r}handle(t){if(this.chain===null){let r=Array.from(new Set([...this.injector.get(Va),...this.injector.get(ey,[])]));this.chain=r.reduceRight((o,i)=>FT(o,i,this.injector),Jv)}if(this.contributeToStability){let r=this.pendingTasks.add();return this.chain(t,o=>this.backend.handle(o)).pipe(Bt(r))}else return this.chain(t,r=>this.backend.handle(r))}static \u0275fac=function(r){return new(r||e)(I(Qo),I(z))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})();var jT=/^\)\]\}',?\n/,VT=RegExp(`^${Zv}:`,"m");function BT(e){return"responseURL"in e&&e.responseURL?e.responseURL:VT.test(e.getAllResponseHeaders())?e.getResponseHeader(Zv):null}var Xd=(()=>{class e{xhrFactory;constructor(t){this.xhrFactory=t}handle(t){if(t.method==="JSONP")throw new C(-2800,!1);t.keepalive;let r=this.xhrFactory;return b(null).pipe(ie(()=>new O(i=>{let s=r.build();if(s.open(t.method,t.urlWithParams),t.withCredentials&&(s.withCredentials=!0),t.headers.forEach((y,w)=>s.setRequestHeader(y,w.join(","))),t.headers.has(Wv)||s.setRequestHeader(Wv,NT),!t.headers.has(Gv)){let y=t.detectContentTypeHeader();y!==null&&s.setRequestHeader(Gv,y)}if(t.responseType){let y=t.responseType.toLowerCase();s.responseType=y!=="json"?y:"text"}let a=t.serializeBody(),c=null,u=()=>{if(c!==null)return c;let y=s.statusText||"OK",w=new on(s.getAllResponseHeaders()),T=BT(s)||t.url;return c=new La({headers:w,status:s.status,statusText:y,url:T}),c},l=()=>{let{headers:y,status:w,statusText:T,url:Ct}=u(),K=null;w!==OT&&(K=typeof s.response>"u"?s.responseText:s.response),w===0&&(w=K?xT:0);let Yn=w>=200&&w<300;if(t.responseType==="json"&&typeof K=="string"){let Ai=K;K=K.replace(jT,"");try{K=K!==""?JSON.parse(K):null}catch(pE){K=Ai,Yn&&(Yn=!1,K={error:pE,text:K})}}Yn?(i.next(new Ko({body:K,headers:y,status:w,statusText:T,url:Ct||void 0})),i.complete()):i.error(new Jo({error:K,headers:y,status:w,statusText:T,url:Ct||void 0}))},d=y=>{let{url:w}=u(),T=new Jo({error:y,status:s.status||0,statusText:s.statusText||"Unknown Error",url:w||void 0});i.error(T)},h=!1,f=y=>{h||(i.next(u()),h=!0);let w={type:Hn.DownloadProgress,loaded:y.loaded};y.lengthComputable&&(w.total=y.total),t.responseType==="text"&&s.responseText&&(w.partialText=s.responseText),i.next(w)},m=y=>{let w={type:Hn.UploadProgress,loaded:y.loaded};y.lengthComputable&&(w.total=y.total),i.next(w)};return s.addEventListener("load",l),s.addEventListener("error",d),s.addEventListener("timeout",d),s.addEventListener("abort",d),t.reportProgress&&(s.addEventListener("progress",f),a!==null&&s.upload&&s.upload.addEventListener("progress",m)),s.send(a),i.next({type:Hn.Sent}),()=>{s.removeEventListener("error",d),s.removeEventListener("abort",d),s.removeEventListener("load",l),s.removeEventListener("timeout",d),t.reportProgress&&(s.removeEventListener("progress",f),a!==null&&s.upload&&s.upload.removeEventListener("progress",m)),s.readyState!==s.DONE&&s.abort()}})))}static \u0275fac=function(r){return new(r||e)(I(Un))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})(),ty=new D(""),UT="XSRF-TOKEN",HT=new D("",{providedIn:"root",factory:()=>UT}),$T="X-XSRF-TOKEN",zT=new D("",{providedIn:"root",factory:()=>$T}),Xo=class{},GT=(()=>{class e{doc;cookieName;lastCookieString="";lastToken=null;parseCount=0;constructor(t,r){this.doc=t,this.cookieName=r}getToken(){let t=this.doc.cookie||"";return t!==this.lastCookieString&&(this.parseCount++,this.lastToken=Go(t,this.cookieName),this.lastCookieString=t),this.lastToken}static \u0275fac=function(r){return new(r||e)(I(q),I(HT))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})();function WT(e,n){let t=e.url.toLowerCase();if(!p(ty)||e.method==="GET"||e.method==="HEAD"||t.startsWith("http://")||t.startsWith("https://"))return n(e);let r=p(Xo).getToken(),o=p(zT);return r!=null&&!e.headers.has(o)&&(e=e.clone({headers:e.headers.set(o,r)})),n(e)}var Ba=function(e){return e[e.Interceptors=0]="Interceptors",e[e.LegacyInterceptors=1]="LegacyInterceptors",e[e.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",e[e.NoXsrfProtection=3]="NoXsrfProtection",e[e.JsonpSupport=4]="JsonpSupport",e[e.RequestsMadeViaParent=5]="RequestsMadeViaParent",e[e.Fetch=6]="Fetch",e}(Ba||{});function ny(e,n){return{\u0275kind:e,\u0275providers:n}}function ry(...e){let n=[Kv,Xd,ja,{provide:Fr,useExisting:ja},{provide:Qo,useFactory:()=>p(kT,{optional:!0})??p(Xd)},{provide:Va,useValue:WT,multi:!0},{provide:ty,useValue:!0},{provide:Xo,useClass:GT}];for(let t of e)n.push(...t.\u0275providers);return bn(n)}function qT(e){return ny(Ba.Interceptors,e.map(n=>({provide:Va,useValue:n,multi:!0})))}var qv=new D("");function oy(){return ny(Ba.LegacyInterceptors,[{provide:qv,useFactory:LT},{provide:Va,useExisting:qv,multi:!0}])}var ZT=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=ke({type:e});static \u0275inj=_e({providers:[ry(oy())]})}return e})();var iy=(()=>{class e{_doc;constructor(t){this._doc=t}getTitle(){return this._doc.title}setTitle(t){this._doc.title=t||""}static \u0275fac=function(r){return new(r||e)(I(q))};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var M="primary",fi=Symbol("RouteTitle"),sf=class{params;constructor(n){this.params=n||{}}has(n){return Object.prototype.hasOwnProperty.call(this.params,n)}get(n){if(this.has(n)){let t=this.params[n];return Array.isArray(t)?t[0]:t}return null}getAll(n){if(this.has(n)){let t=this.params[n];return Array.isArray(t)?t:[t]}return[]}get keys(){return Object.keys(this.params)}};function Gn(e){return new sf(e)}function hy(e,n,t){let r=t.path.split("/");if(r.length>e.length||t.pathMatch==="full"&&(n.hasChildren()||r.length<e.length))return null;let o={};for(let i=0;i<r.length;i++){let s=r[i],a=e[i];if(s[0]===":")o[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,r.length),posParams:o}}function QT(e,n){if(e.length!==n.length)return!1;for(let t=0;t<e.length;++t)if(!gt(e[t],n[t]))return!1;return!0}function gt(e,n){let t=e?af(e):void 0,r=n?af(n):void 0;if(!t||!r||t.length!=r.length)return!1;let o;for(let i=0;i<t.length;i++)if(o=t[i],!py(e[o],n[o]))return!1;return!0}function af(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}function py(e,n){if(Array.isArray(e)&&Array.isArray(n)){if(e.length!==n.length)return!1;let t=[...e].sort(),r=[...n].sort();return t.every((o,i)=>r[i]===o)}else return e===n}function gy(e){return e.length>0?e[e.length-1]:null}function Ot(e){return ou(e)?e:nn(e)?G(Promise.resolve(e)):b(e)}var KT={exact:vy,subset:yy},my={exact:JT,subset:XT,ignored:()=>!0};function sy(e,n,t){return KT[t.paths](e.root,n.root,t.matrixParams)&&my[t.queryParams](e.queryParams,n.queryParams)&&!(t.fragment==="exact"&&e.fragment!==n.fragment)}function JT(e,n){return gt(e,n)}function vy(e,n,t){if(!$n(e.segments,n.segments)||!$a(e.segments,n.segments,t)||e.numberOfChildren!==n.numberOfChildren)return!1;for(let r in n.children)if(!e.children[r]||!vy(e.children[r],n.children[r],t))return!1;return!0}function XT(e,n){return Object.keys(n).length<=Object.keys(e).length&&Object.keys(n).every(t=>py(e[t],n[t]))}function yy(e,n,t){return Dy(e,n,n.segments,t)}function Dy(e,n,t,r){if(e.segments.length>t.length){let o=e.segments.slice(0,t.length);return!(!$n(o,t)||n.hasChildren()||!$a(o,t,r))}else if(e.segments.length===t.length){if(!$n(e.segments,t)||!$a(e.segments,t,r))return!1;for(let o in n.children)if(!e.children[o]||!yy(e.children[o],n.children[o],r))return!1;return!0}else{let o=t.slice(0,e.segments.length),i=t.slice(e.segments.length);return!$n(e.segments,o)||!$a(e.segments,o,r)||!e.children[M]?!1:Dy(e.children[M],n,i,r)}}function $a(e,n,t){return n.every((r,o)=>my[t](e[o].parameters,r.parameters))}var vt=class{root;queryParams;fragment;_queryParamMap;constructor(n=new V([],{}),t={},r=null){this.root=n,this.queryParams=t,this.fragment=r}get queryParamMap(){return this._queryParamMap??=Gn(this.queryParams),this._queryParamMap}toString(){return nS.serialize(this)}},V=class{segments;children;parent=null;constructor(n,t){this.segments=n,this.children=t,Object.values(t).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return za(this)}},sn=class{path;parameters;_parameterMap;constructor(n,t){this.path=n,this.parameters=t}get parameterMap(){return this._parameterMap??=Gn(this.parameters),this._parameterMap}toString(){return Cy(this)}};function eS(e,n){return $n(e,n)&&e.every((t,r)=>gt(t.parameters,n[r].parameters))}function $n(e,n){return e.length!==n.length?!1:e.every((t,r)=>t.path===n[r].path)}function tS(e,n){let t=[];return Object.entries(e.children).forEach(([r,o])=>{r===M&&(t=t.concat(n(o,r)))}),Object.entries(e.children).forEach(([r,o])=>{r!==M&&(t=t.concat(n(o,r)))}),t}var kt=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:()=>new an,providedIn:"root"})}return e})(),an=class{parse(n){let t=new uf(n);return new vt(t.parseRootSegment(),t.parseQueryParams(),t.parseFragment())}serialize(n){let t=`/${ei(n.root,!0)}`,r=iS(n.queryParams),o=typeof n.fragment=="string"?`#${rS(n.fragment)}`:"";return`${t}${r}${o}`}},nS=new an;function za(e){return e.segments.map(n=>Cy(n)).join("/")}function ei(e,n){if(!e.hasChildren())return za(e);if(n){let t=e.children[M]?ei(e.children[M],!1):"",r=[];return Object.entries(e.children).forEach(([o,i])=>{o!==M&&r.push(`${o}:${ei(i,!1)}`)}),r.length>0?`${t}(${r.join("//")})`:t}else{let t=tS(e,(r,o)=>o===M?[ei(e.children[M],!1)]:[`${o}:${ei(r,!1)}`]);return Object.keys(e.children).length===1&&e.children[M]!=null?`${za(e)}/${t[0]}`:`${za(e)}/(${t.join("//")})`}}function Ey(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function Ua(e){return Ey(e).replace(/%3B/gi,";")}function rS(e){return encodeURI(e)}function cf(e){return Ey(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function Ga(e){return decodeURIComponent(e)}function ay(e){return Ga(e.replace(/\+/g,"%20"))}function Cy(e){return`${cf(e.path)}${oS(e.parameters)}`}function oS(e){return Object.entries(e).map(([n,t])=>`;${cf(n)}=${cf(t)}`).join("")}function iS(e){let n=Object.entries(e).map(([t,r])=>Array.isArray(r)?r.map(o=>`${Ua(t)}=${Ua(o)}`).join("&"):`${Ua(t)}=${Ua(r)}`).filter(t=>t);return n.length?`?${n.join("&")}`:""}var sS=/^[^\/()?;#]+/;function tf(e){let n=e.match(sS);return n?n[0]:""}var aS=/^[^\/()?;=#]+/;function cS(e){let n=e.match(aS);return n?n[0]:""}var uS=/^[^=?&#]+/;function lS(e){let n=e.match(uS);return n?n[0]:""}var dS=/^[^&#]+/;function fS(e){let n=e.match(dS);return n?n[0]:""}var uf=class{url;remaining;constructor(n){this.url=n,this.remaining=n}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new V([],{}):new V([],this.parseChildren())}parseQueryParams(){let n={};if(this.consumeOptional("?"))do this.parseQueryParam(n);while(this.consumeOptional("&"));return n}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let n=[];for(this.peekStartsWith("(")||n.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),n.push(this.parseSegment());let t={};this.peekStartsWith("/(")&&(this.capture("/"),t=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(n.length>0||Object.keys(t).length>0)&&(r[M]=new V(n,t)),r}parseSegment(){let n=tf(this.remaining);if(n===""&&this.peekStartsWith(";"))throw new C(4009,!1);return this.capture(n),new sn(Ga(n),this.parseMatrixParams())}parseMatrixParams(){let n={};for(;this.consumeOptional(";");)this.parseParam(n);return n}parseParam(n){let t=cS(this.remaining);if(!t)return;this.capture(t);let r="";if(this.consumeOptional("=")){let o=tf(this.remaining);o&&(r=o,this.capture(r))}n[Ga(t)]=Ga(r)}parseQueryParam(n){let t=lS(this.remaining);if(!t)return;this.capture(t);let r="";if(this.consumeOptional("=")){let s=fS(this.remaining);s&&(r=s,this.capture(r))}let o=ay(t),i=ay(r);if(n.hasOwnProperty(o)){let s=n[o];Array.isArray(s)||(s=[s],n[o]=s),s.push(i)}else n[o]=i}parseParens(n){let t={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=tf(this.remaining),o=this.remaining[r.length];if(o!=="/"&&o!==")"&&o!==";")throw new C(4010,!1);let i;r.indexOf(":")>-1?(i=r.slice(0,r.indexOf(":")),this.capture(i),this.capture(":")):n&&(i=M);let s=this.parseChildren();t[i]=Object.keys(s).length===1?s[M]:new V([],s),this.consumeOptional("//")}return t}peekStartsWith(n){return this.remaining.startsWith(n)}consumeOptional(n){return this.peekStartsWith(n)?(this.remaining=this.remaining.substring(n.length),!0):!1}capture(n){if(!this.consumeOptional(n))throw new C(4011,!1)}};function wy(e){return e.segments.length>0?new V([],{[M]:e}):e}function Iy(e){let n={};for(let[r,o]of Object.entries(e.children)){let i=Iy(o);if(r===M&&i.segments.length===0&&i.hasChildren())for(let[s,a]of Object.entries(i.children))n[s]=a;else(i.segments.length>0||i.hasChildren())&&(n[r]=i)}let t=new V(e.segments,n);return hS(t)}function hS(e){if(e.numberOfChildren===1&&e.children[M]){let n=e.children[M];return new V(e.segments.concat(n.segments),n.children)}return e}function cn(e){return e instanceof vt}function by(e,n,t=null,r=null){let o=_y(e);return Ty(o,n,t,r)}function _y(e){let n;function t(i){let s={};for(let c of i.children){let u=t(c);s[c.outlet]=u}let a=new V(i.url,s);return i===e&&(n=a),a}let r=t(e.root),o=wy(r);return n??o}function Ty(e,n,t,r){let o=e;for(;o.parent;)o=o.parent;if(n.length===0)return nf(o,o,o,t,r);let i=pS(n);if(i.toRoot())return nf(o,o,new V([],{}),t,r);let s=gS(i,o,e),a=s.processChildren?ni(s.segmentGroup,s.index,i.commands):My(s.segmentGroup,s.index,i.commands);return nf(o,s.segmentGroup,a,t,r)}function Wa(e){return typeof e=="object"&&e!=null&&!e.outlets&&!e.segmentPath}function oi(e){return typeof e=="object"&&e!=null&&e.outlets}function nf(e,n,t,r,o){let i={};r&&Object.entries(r).forEach(([c,u])=>{i[c]=Array.isArray(u)?u.map(l=>`${l}`):`${u}`});let s;e===n?s=t:s=Sy(e,n,t);let a=wy(Iy(s));return new vt(a,i,o)}function Sy(e,n,t){let r={};return Object.entries(e.children).forEach(([o,i])=>{i===n?r[o]=t:r[o]=Sy(i,n,t)}),new V(e.segments,r)}var qa=class{isAbsolute;numberOfDoubleDots;commands;constructor(n,t,r){if(this.isAbsolute=n,this.numberOfDoubleDots=t,this.commands=r,n&&r.length>0&&Wa(r[0]))throw new C(4003,!1);let o=r.find(oi);if(o&&o!==gy(r))throw new C(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function pS(e){if(typeof e[0]=="string"&&e.length===1&&e[0]==="/")return new qa(!0,0,e);let n=0,t=!1,r=e.reduce((o,i,s)=>{if(typeof i=="object"&&i!=null){if(i.outlets){let a={};return Object.entries(i.outlets).forEach(([c,u])=>{a[c]=typeof u=="string"?u.split("/"):u}),[...o,{outlets:a}]}if(i.segmentPath)return[...o,i.segmentPath]}return typeof i!="string"?[...o,i]:s===0?(i.split("/").forEach((a,c)=>{c==0&&a==="."||(c==0&&a===""?t=!0:a===".."?n++:a!=""&&o.push(a))}),o):[...o,i]},[]);return new qa(t,n,r)}var Br=class{segmentGroup;processChildren;index;constructor(n,t,r){this.segmentGroup=n,this.processChildren=t,this.index=r}};function gS(e,n,t){if(e.isAbsolute)return new Br(n,!0,0);if(!t)return new Br(n,!1,NaN);if(t.parent===null)return new Br(t,!0,0);let r=Wa(e.commands[0])?0:1,o=t.segments.length-1+r;return mS(t,o,e.numberOfDoubleDots)}function mS(e,n,t){let r=e,o=n,i=t;for(;i>o;){if(i-=o,r=r.parent,!r)throw new C(4005,!1);o=r.segments.length}return new Br(r,!1,o-i)}function vS(e){return oi(e[0])?e[0].outlets:{[M]:e}}function My(e,n,t){if(e??=new V([],{}),e.segments.length===0&&e.hasChildren())return ni(e,n,t);let r=yS(e,n,t),o=t.slice(r.commandIndex);if(r.match&&r.pathIndex<e.segments.length){let i=new V(e.segments.slice(0,r.pathIndex),{});return i.children[M]=new V(e.segments.slice(r.pathIndex),e.children),ni(i,0,o)}else return r.match&&o.length===0?new V(e.segments,{}):r.match&&!e.hasChildren()?lf(e,n,t):r.match?ni(e,0,o):lf(e,n,t)}function ni(e,n,t){if(t.length===0)return new V(e.segments,{});{let r=vS(t),o={};if(Object.keys(r).some(i=>i!==M)&&e.children[M]&&e.numberOfChildren===1&&e.children[M].segments.length===0){let i=ni(e.children[M],n,t);return new V(e.segments,i.children)}return Object.entries(r).forEach(([i,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(o[i]=My(e.children[i],n,s))}),Object.entries(e.children).forEach(([i,s])=>{r[i]===void 0&&(o[i]=s)}),new V(e.segments,o)}}function yS(e,n,t){let r=0,o=n,i={match:!1,pathIndex:0,commandIndex:0};for(;o<e.segments.length;){if(r>=t.length)return i;let s=e.segments[o],a=t[r];if(oi(a))break;let c=`${a}`,u=r<t.length-1?t[r+1]:null;if(o>0&&c===void 0)break;if(c&&u&&typeof u=="object"&&u.outlets===void 0){if(!uy(c,u,s))return i;r+=2}else{if(!uy(c,{},s))return i;r++}o++}return{match:!0,pathIndex:o,commandIndex:r}}function lf(e,n,t){let r=e.segments.slice(0,n),o=0;for(;o<t.length;){let i=t[o];if(oi(i)){let c=DS(i.outlets);return new V(r,c)}if(o===0&&Wa(t[0])){let c=e.segments[n];r.push(new sn(c.path,cy(t[0]))),o++;continue}let s=oi(i)?i.outlets[M]:`${i}`,a=o<t.length-1?t[o+1]:null;s&&a&&Wa(a)?(r.push(new sn(s,cy(a))),o+=2):(r.push(new sn(s,{})),o++)}return new V(r,{})}function DS(e){let n={};return Object.entries(e).forEach(([t,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(n[t]=lf(new V([],{}),0,r))}),n}function cy(e){let n={};return Object.entries(e).forEach(([t,r])=>n[t]=`${r}`),n}function uy(e,n,t){return e==t.path&&gt(n,t.parameters)}var Ur="imperative",ae=function(e){return e[e.NavigationStart=0]="NavigationStart",e[e.NavigationEnd=1]="NavigationEnd",e[e.NavigationCancel=2]="NavigationCancel",e[e.NavigationError=3]="NavigationError",e[e.RoutesRecognized=4]="RoutesRecognized",e[e.ResolveStart=5]="ResolveStart",e[e.ResolveEnd=6]="ResolveEnd",e[e.GuardsCheckStart=7]="GuardsCheckStart",e[e.GuardsCheckEnd=8]="GuardsCheckEnd",e[e.RouteConfigLoadStart=9]="RouteConfigLoadStart",e[e.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",e[e.ChildActivationStart=11]="ChildActivationStart",e[e.ChildActivationEnd=12]="ChildActivationEnd",e[e.ActivationStart=13]="ActivationStart",e[e.ActivationEnd=14]="ActivationEnd",e[e.Scroll=15]="Scroll",e[e.NavigationSkipped=16]="NavigationSkipped",e}(ae||{}),Le=class{id;url;constructor(n,t){this.id=n,this.url=t}},yt=class extends Le{type=ae.NavigationStart;navigationTrigger;restoredState;constructor(n,t,r="imperative",o=null){super(n,t),this.navigationTrigger=r,this.restoredState=o}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},We=class extends Le{urlAfterRedirects;type=ae.NavigationEnd;constructor(n,t,r){super(n,t),this.urlAfterRedirects=r}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},Ee=function(e){return e[e.Redirect=0]="Redirect",e[e.SupersededByNewNavigation=1]="SupersededByNewNavigation",e[e.NoDataFromResolver=2]="NoDataFromResolver",e[e.GuardRejected=3]="GuardRejected",e[e.Aborted=4]="Aborted",e}(Ee||{}),$r=function(e){return e[e.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",e[e.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",e}($r||{}),mt=class extends Le{reason;code;type=ae.NavigationCancel;constructor(n,t,r,o){super(n,t),this.reason=r,this.code=o}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},Dt=class extends Le{reason;code;type=ae.NavigationSkipped;constructor(n,t,r,o){super(n,t),this.reason=r,this.code=o}},zr=class extends Le{error;target;type=ae.NavigationError;constructor(n,t,r,o){super(n,t),this.error=r,this.target=o}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},ii=class extends Le{urlAfterRedirects;state;type=ae.RoutesRecognized;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Za=class extends Le{urlAfterRedirects;state;type=ae.GuardsCheckStart;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Ya=class extends Le{urlAfterRedirects;state;shouldActivate;type=ae.GuardsCheckEnd;constructor(n,t,r,o,i){super(n,t),this.urlAfterRedirects=r,this.state=o,this.shouldActivate=i}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},Qa=class extends Le{urlAfterRedirects;state;type=ae.ResolveStart;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Ka=class extends Le{urlAfterRedirects;state;type=ae.ResolveEnd;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Ja=class{route;type=ae.RouteConfigLoadStart;constructor(n){this.route=n}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},Xa=class{route;type=ae.RouteConfigLoadEnd;constructor(n){this.route=n}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},ec=class{snapshot;type=ae.ChildActivationStart;constructor(n){this.snapshot=n}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},tc=class{snapshot;type=ae.ChildActivationEnd;constructor(n){this.snapshot=n}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},nc=class{snapshot;type=ae.ActivationStart;constructor(n){this.snapshot=n}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},rc=class{snapshot;type=ae.ActivationEnd;constructor(n){this.snapshot=n}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Gr=class{routerEvent;position;anchor;type=ae.Scroll;constructor(n,t,r){this.routerEvent=n,this.position=t,this.anchor=r}toString(){let n=this.position?`${this.position[0]}, ${this.position[1]}`:null;return`Scroll(anchor: '${this.anchor}', position: '${n}')`}},si=class{},Wr=class{url;navigationBehaviorOptions;constructor(n,t){this.url=n,this.navigationBehaviorOptions=t}};function ES(e){return!(e instanceof si)&&!(e instanceof Wr)}function CS(e,n){return e.providers&&!e._injector&&(e._injector=Nr(e.providers,n,`Route: ${e.path}`)),e._injector??n}function ot(e){return e.outlet||M}function wS(e,n){let t=e.filter(r=>ot(r)===n);return t.push(...e.filter(r=>ot(r)!==n)),t}function hi(e){if(!e)return null;if(e.routeConfig?._injector)return e.routeConfig._injector;for(let n=e.parent;n;n=n.parent){let t=n.routeConfig;if(t?._loadedInjector)return t._loadedInjector;if(t?._injector)return t._injector}return null}var oc=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){return hi(this.route?.snapshot)??this.rootInjector}constructor(n){this.rootInjector=n,this.children=new Et(this.rootInjector)}},Et=(()=>{class e{rootInjector;contexts=new Map;constructor(t){this.rootInjector=t}onChildOutletCreated(t,r){let o=this.getOrCreateContext(t);o.outlet=r,this.contexts.set(t,o)}onChildOutletDestroyed(t){let r=this.getContext(t);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let t=this.contexts;return this.contexts=new Map,t}onOutletReAttached(t){this.contexts=t}getOrCreateContext(t){let r=this.getContext(t);return r||(r=new oc(this.rootInjector),this.contexts.set(t,r)),r}getContext(t){return this.contexts.get(t)||null}static \u0275fac=function(r){return new(r||e)(I(z))};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),ic=class{_root;constructor(n){this._root=n}get root(){return this._root.value}parent(n){let t=this.pathFromRoot(n);return t.length>1?t[t.length-2]:null}children(n){let t=df(n,this._root);return t?t.children.map(r=>r.value):[]}firstChild(n){let t=df(n,this._root);return t&&t.children.length>0?t.children[0].value:null}siblings(n){let t=ff(n,this._root);return t.length<2?[]:t[t.length-2].children.map(o=>o.value).filter(o=>o!==n)}pathFromRoot(n){return ff(n,this._root).map(t=>t.value)}};function df(e,n){if(e===n.value)return n;for(let t of n.children){let r=df(e,t);if(r)return r}return null}function ff(e,n){if(e===n.value)return[n];for(let t of n.children){let r=ff(e,t);if(r.length)return r.unshift(n),r}return[]}var Fe=class{value;children;constructor(n,t){this.value=n,this.children=t}toString(){return`TreeNode(${this.value})`}};function Vr(e){let n={};return e&&e.children.forEach(t=>n[t.value.outlet]=t),n}var ai=class extends ic{snapshot;constructor(n,t){super(n),this.snapshot=t,Ef(this,n)}toString(){return this.snapshot.toString()}};function Ay(e){let n=IS(e),t=new te([new sn("",{})]),r=new te({}),o=new te({}),i=new te({}),s=new te(""),a=new Ce(t,r,i,s,o,M,e,n.root);return a.snapshot=n.root,new ai(new Fe(a,[]),n)}function IS(e){let n={},t={},r={},o="",i=new zn([],n,r,o,t,M,e,null,{});return new ci("",new Fe(i,[]))}var Ce=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(n,t,r,o,i,s,a,c){this.urlSubject=n,this.paramsSubject=t,this.queryParamsSubject=r,this.fragmentSubject=o,this.dataSubject=i,this.outlet=s,this.component=a,this._futureSnapshot=c,this.title=this.dataSubject?.pipe(N(u=>u[fi]))??b(void 0),this.url=n,this.params=t,this.queryParams=r,this.fragment=o,this.data=i}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(N(n=>Gn(n))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(N(n=>Gn(n))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function sc(e,n,t="emptyOnly"){let r,{routeConfig:o}=e;return n!==null&&(t==="always"||o?.path===""||!n.component&&!n.routeConfig?.loadComponent)?r={params:g(g({},n.params),e.params),data:g(g({},n.data),e.data),resolve:g(g(g(g({},e.data),n.data),o?.data),e._resolvedData)}:r={params:g({},e.params),data:g({},e.data),resolve:g(g({},e.data),e._resolvedData??{})},o&&Ny(o)&&(r.resolve[fi]=o.title),r}var zn=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){return this.data?.[fi]}constructor(n,t,r,o,i,s,a,c,u){this.url=n,this.params=t,this.queryParams=r,this.fragment=o,this.data=i,this.outlet=s,this.component=a,this.routeConfig=c,this._resolve=u}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=Gn(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=Gn(this.queryParams),this._queryParamMap}toString(){let n=this.url.map(r=>r.toString()).join("/"),t=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${n}', path:'${t}')`}},ci=class extends ic{url;constructor(n,t){super(t),this.url=n,Ef(this,t)}toString(){return Ry(this._root)}};function Ef(e,n){n.value._routerState=e,n.children.forEach(t=>Ef(e,t))}function Ry(e){let n=e.children.length>0?` { ${e.children.map(Ry).join(", ")} } `:"";return`${e.value}${n}`}function rf(e){if(e.snapshot){let n=e.snapshot,t=e._futureSnapshot;e.snapshot=t,gt(n.queryParams,t.queryParams)||e.queryParamsSubject.next(t.queryParams),n.fragment!==t.fragment&&e.fragmentSubject.next(t.fragment),gt(n.params,t.params)||e.paramsSubject.next(t.params),QT(n.url,t.url)||e.urlSubject.next(t.url),gt(n.data,t.data)||e.dataSubject.next(t.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function hf(e,n){let t=gt(e.params,n.params)&&eS(e.url,n.url),r=!e.parent!=!n.parent;return t&&!r&&(!e.parent||hf(e.parent,n.parent))}function Ny(e){return typeof e.title=="string"||e.title===null}var xy=new D(""),Cf=(()=>{class e{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=M;activateEvents=new $;deactivateEvents=new $;attachEvents=new $;detachEvents=new $;routerOutletData=yv(void 0);parentContexts=p(Et);location=p(Ue);changeDetector=p(ze);inputBinder=p(pi,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(t){if(t.name){let{firstChange:r,previousValue:o}=t.name;if(r)return;this.isTrackedInParentContexts(o)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(o)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(t){return this.parentContexts.getContext(t)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let t=this.parentContexts.getContext(this.name);t?.route&&(t.attachRef?this.attach(t.attachRef,t.route):this.activateWith(t.route,t.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new C(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new C(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new C(4012,!1);this.location.detach();let t=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(t.instance),t}attach(t,r){this.activated=t,this._activatedRoute=r,this.location.insert(t.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(t.instance)}deactivate(){if(this.activated){let t=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(t)}}activateWith(t,r){if(this.isActivated)throw new C(4013,!1);this._activatedRoute=t;let o=this.location,s=t.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,c=new pf(t,a,o.injector,this.routerOutletData);this.activated=o.createComponent(s,{index:o.length,injector:c,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static \u0275fac=function(r){return new(r||e)};static \u0275dir=j({type:e,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[De]})}return e})(),pf=class{route;childContexts;parent;outletData;constructor(n,t,r,o){this.route=n,this.childContexts=t,this.parent=r,this.outletData=o}get(n,t){return n===Ce?this.route:n===Et?this.childContexts:n===xy?this.outletData:this.parent.get(n,t)}},pi=new D(""),wf=(()=>{class e{outletDataSubscriptions=new Map;bindActivatedRouteToOutletComponent(t){this.unsubscribeFromRouteData(t),this.subscribeToRouteData(t)}unsubscribeFromRouteData(t){this.outletDataSubscriptions.get(t)?.unsubscribe(),this.outletDataSubscriptions.delete(t)}subscribeToRouteData(t){let{activatedRoute:r}=t,o=mn([r.queryParams,r.params,r.data]).pipe(ie(([i,s,a],c)=>(a=g(g(g({},i),s),a),c===0?b(a):Promise.resolve(a)))).subscribe(i=>{if(!t.isActivated||!t.activatedComponentRef||t.activatedRoute!==r||r.component===null){this.unsubscribeFromRouteData(t);return}let s=ba(r.component);if(!s){this.unsubscribeFromRouteData(t);return}for(let{templateName:a}of s.inputs)t.activatedComponentRef.setInput(a,i[a])});this.outletDataSubscriptions.set(t,o)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})(),If=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=yd({type:e,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(r,o){r&1&&Da(0,"router-outlet")},dependencies:[Cf],encapsulation:2})}return e})();function bf(e){let n=e.children&&e.children.map(bf),t=n?A(g({},e),{children:n}):g({},e);return!t.component&&!t.loadComponent&&(n||t.loadChildren)&&t.outlet&&t.outlet!==M&&(t.component=If),t}function bS(e,n,t){let r=ui(e,n._root,t?t._root:void 0);return new ai(r,n)}function ui(e,n,t){if(t&&e.shouldReuseRoute(n.value,t.value.snapshot)){let r=t.value;r._futureSnapshot=n.value;let o=_S(e,n,t);return new Fe(r,o)}else{if(e.shouldAttach(n.value)){let i=e.retrieve(n.value);if(i!==null){let s=i.route;return s.value._futureSnapshot=n.value,s.children=n.children.map(a=>ui(e,a)),s}}let r=TS(n.value),o=n.children.map(i=>ui(e,i));return new Fe(r,o)}}function _S(e,n,t){return n.children.map(r=>{for(let o of t.children)if(e.shouldReuseRoute(r.value,o.value.snapshot))return ui(e,r,o);return ui(e,r)})}function TS(e){return new Ce(new te(e.url),new te(e.params),new te(e.queryParams),new te(e.fragment),new te(e.data),e.outlet,e.component,e)}var qr=class{redirectTo;navigationBehaviorOptions;constructor(n,t){this.redirectTo=n,this.navigationBehaviorOptions=t}},Oy="ngNavigationCancelingError";function ac(e,n){let{redirectTo:t,navigationBehaviorOptions:r}=cn(n)?{redirectTo:n,navigationBehaviorOptions:void 0}:n,o=ky(!1,Ee.Redirect);return o.url=t,o.navigationBehaviorOptions=r,o}function ky(e,n){let t=new Error(`NavigationCancelingError: ${e||""}`);return t[Oy]=!0,t.cancellationCode=n,t}function SS(e){return Py(e)&&cn(e.url)}function Py(e){return!!e&&e[Oy]}var MS=(e,n,t,r)=>N(o=>(new gf(n,o.targetRouterState,o.currentRouterState,t,r).activate(e),o)),gf=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(n,t,r,o,i){this.routeReuseStrategy=n,this.futureState=t,this.currState=r,this.forwardEvent=o,this.inputBindingEnabled=i}activate(n){let t=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(t,r,n),rf(this.futureState.root),this.activateChildRoutes(t,r,n)}deactivateChildRoutes(n,t,r){let o=Vr(t);n.children.forEach(i=>{let s=i.value.outlet;this.deactivateRoutes(i,o[s],r),delete o[s]}),Object.values(o).forEach(i=>{this.deactivateRouteAndItsChildren(i,r)})}deactivateRoutes(n,t,r){let o=n.value,i=t?t.value:null;if(o===i)if(o.component){let s=r.getContext(o.outlet);s&&this.deactivateChildRoutes(n,t,s.children)}else this.deactivateChildRoutes(n,t,r);else i&&this.deactivateRouteAndItsChildren(t,r)}deactivateRouteAndItsChildren(n,t){n.value.component&&this.routeReuseStrategy.shouldDetach(n.value.snapshot)?this.detachAndStoreRouteSubtree(n,t):this.deactivateRouteAndOutlet(n,t)}detachAndStoreRouteSubtree(n,t){let r=t.getContext(n.value.outlet),o=r&&n.value.component?r.children:t,i=Vr(n);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);if(r&&r.outlet){let s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(n.value.snapshot,{componentRef:s,route:n,contexts:a})}}deactivateRouteAndOutlet(n,t){let r=t.getContext(n.value.outlet),o=r&&n.value.component?r.children:t,i=Vr(n);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(n,t,r){let o=Vr(t);n.children.forEach(i=>{this.activateRoutes(i,o[i.value.outlet],r),this.forwardEvent(new rc(i.value.snapshot))}),n.children.length&&this.forwardEvent(new tc(n.value.snapshot))}activateRoutes(n,t,r){let o=n.value,i=t?t.value:null;if(rf(o),o===i)if(o.component){let s=r.getOrCreateContext(o.outlet);this.activateChildRoutes(n,t,s.children)}else this.activateChildRoutes(n,t,r);else if(o.component){let s=r.getOrCreateContext(o.outlet);if(this.routeReuseStrategy.shouldAttach(o.snapshot)){let a=this.routeReuseStrategy.retrieve(o.snapshot);this.routeReuseStrategy.store(o.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),rf(a.route.value),this.activateChildRoutes(n,null,s.children)}else s.attachRef=null,s.route=o,s.outlet&&s.outlet.activateWith(o,s.injector),this.activateChildRoutes(n,null,s.children)}else this.activateChildRoutes(n,null,r)}},cc=class{path;route;constructor(n){this.path=n,this.route=this.path[this.path.length-1]}},Hr=class{component;route;constructor(n,t){this.component=n,this.route=t}};function AS(e,n,t){let r=e._root,o=n?n._root:null;return ti(r,o,t,[r.value])}function RS(e){let n=e.routeConfig?e.routeConfig.canActivateChild:null;return!n||n.length===0?null:{node:e,guards:n}}function Yr(e,n){let t=Symbol(),r=n.get(e,t);return r===t?typeof e=="function"&&!bu(e)?e:n.get(e):r}function ti(e,n,t,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=Vr(n);return e.children.forEach(s=>{NS(s,i[s.value.outlet],t,r.concat([s.value]),o),delete i[s.value.outlet]}),Object.entries(i).forEach(([s,a])=>ri(a,t.getContext(s),o)),o}function NS(e,n,t,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=e.value,s=n?n.value:null,a=t?t.getContext(e.value.outlet):null;if(s&&i.routeConfig===s.routeConfig){let c=xS(s,i,i.routeConfig.runGuardsAndResolvers);c?o.canActivateChecks.push(new cc(r)):(i.data=s.data,i._resolvedData=s._resolvedData),i.component?ti(e,n,a?a.children:null,r,o):ti(e,n,t,r,o),c&&a&&a.outlet&&a.outlet.isActivated&&o.canDeactivateChecks.push(new Hr(a.outlet.component,s))}else s&&ri(n,a,o),o.canActivateChecks.push(new cc(r)),i.component?ti(e,null,a?a.children:null,r,o):ti(e,null,t,r,o);return o}function xS(e,n,t){if(typeof t=="function")return t(e,n);switch(t){case"pathParamsChange":return!$n(e.url,n.url);case"pathParamsOrQueryParamsChange":return!$n(e.url,n.url)||!gt(e.queryParams,n.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!hf(e,n)||!gt(e.queryParams,n.queryParams);case"paramsChange":default:return!hf(e,n)}}function ri(e,n,t){let r=Vr(e),o=e.value;Object.entries(r).forEach(([i,s])=>{o.component?n?ri(s,n.children.getContext(i),t):ri(s,null,t):ri(s,n,t)}),o.component?n&&n.outlet&&n.outlet.isActivated?t.canDeactivateChecks.push(new Hr(n.outlet.component,o)):t.canDeactivateChecks.push(new Hr(null,o)):t.canDeactivateChecks.push(new Hr(null,o))}function gi(e){return typeof e=="function"}function OS(e){return typeof e=="boolean"}function kS(e){return e&&gi(e.canLoad)}function PS(e){return e&&gi(e.canActivate)}function FS(e){return e&&gi(e.canActivateChild)}function LS(e){return e&&gi(e.canDeactivate)}function jS(e){return e&&gi(e.canMatch)}function Fy(e){return e instanceof It||e?.name==="EmptyError"}var Ha=Symbol("INITIAL_VALUE");function Zr(){return ie(e=>mn(e.map(n=>n.pipe(bt(1),uu(Ha)))).pipe(N(n=>{for(let t of n)if(t!==!0){if(t===Ha)return Ha;if(t===!1||VS(t))return t}return!0}),oe(n=>n!==Ha),bt(1)))}function VS(e){return cn(e)||e instanceof qr}function BS(e,n){return Z(t=>{let{targetSnapshot:r,currentSnapshot:o,guards:{canActivateChecks:i,canDeactivateChecks:s}}=t;return s.length===0&&i.length===0?b(A(g({},t),{guardsResult:!0})):US(s,r,o,e).pipe(Z(a=>a&&OS(a)?HS(r,i,e,n):b(a)),N(a=>A(g({},t),{guardsResult:a})))})}function US(e,n,t,r){return G(e).pipe(Z(o=>qS(o.component,o.route,t,n,r)),_t(o=>o!==!0,!0))}function HS(e,n,t,r){return G(n).pipe(st(o=>dr(zS(o.route.parent,r),$S(o.route,r),WS(e,o.path,t),GS(e,o.route,t))),_t(o=>o!==!0,!0))}function $S(e,n){return e!==null&&n&&n(new nc(e)),b(!0)}function zS(e,n){return e!==null&&n&&n(new ec(e)),b(!0)}function GS(e,n,t){let r=n.routeConfig?n.routeConfig.canActivate:null;if(!r||r.length===0)return b(!0);let o=r.map(i=>co(()=>{let s=hi(n)??t,a=Yr(i,s),c=PS(a)?a.canActivate(n,e):pe(s,()=>a(n,e));return Ot(c).pipe(_t())}));return b(o).pipe(Zr())}function WS(e,n,t){let r=n[n.length-1],i=n.slice(0,n.length-1).reverse().map(s=>RS(s)).filter(s=>s!==null).map(s=>co(()=>{let a=s.guards.map(c=>{let u=hi(s.node)??t,l=Yr(c,u),d=FS(l)?l.canActivateChild(r,e):pe(u,()=>l(r,e));return Ot(d).pipe(_t())});return b(a).pipe(Zr())}));return b(i).pipe(Zr())}function qS(e,n,t,r,o){let i=n&&n.routeConfig?n.routeConfig.canDeactivate:null;if(!i||i.length===0)return b(!0);let s=i.map(a=>{let c=hi(n)??o,u=Yr(a,c),l=LS(u)?u.canDeactivate(e,n,t,r):pe(c,()=>u(e,n,t,r));return Ot(l).pipe(_t())});return b(s).pipe(Zr())}function ZS(e,n,t,r){let o=n.canLoad;if(o===void 0||o.length===0)return b(!0);let i=o.map(s=>{let a=Yr(s,e),c=kS(a)?a.canLoad(n,t):pe(e,()=>a(n,t));return Ot(c)});return b(i).pipe(Zr(),Ly(r))}function Ly(e){return eu(ce(n=>{if(typeof n!="boolean")throw ac(e,n)}),N(n=>n===!0))}function YS(e,n,t,r){let o=n.canMatch;if(!o||o.length===0)return b(!0);let i=o.map(s=>{let a=Yr(s,e),c=jS(a)?a.canMatch(n,t):pe(e,()=>a(n,t));return Ot(c)});return b(i).pipe(Zr(),Ly(r))}var li=class{segmentGroup;constructor(n){this.segmentGroup=n||null}},di=class extends Error{urlTree;constructor(n){super(),this.urlTree=n}};function jr(e){return cr(new li(e))}function QS(e){return cr(new C(4e3,!1))}function KS(e){return cr(ky(!1,Ee.GuardRejected))}var mf=class{urlSerializer;urlTree;constructor(n,t){this.urlSerializer=n,this.urlTree=t}lineralizeSegments(n,t){let r=[],o=t.root;for(;;){if(r=r.concat(o.segments),o.numberOfChildren===0)return b(r);if(o.numberOfChildren>1||!o.children[M])return QS(`${n.redirectTo}`);o=o.children[M]}}applyRedirectCommands(n,t,r,o,i){return JS(t,o,i).pipe(N(s=>{if(s instanceof vt)throw new di(s);let a=this.applyRedirectCreateUrlTree(s,this.urlSerializer.parse(s),n,r);if(s[0]==="/")throw new di(a);return a}))}applyRedirectCreateUrlTree(n,t,r,o){let i=this.createSegmentGroup(n,t.root,r,o);return new vt(i,this.createQueryParams(t.queryParams,this.urlTree.queryParams),t.fragment)}createQueryParams(n,t){let r={};return Object.entries(n).forEach(([o,i])=>{if(typeof i=="string"&&i[0]===":"){let a=i.substring(1);r[o]=t[a]}else r[o]=i}),r}createSegmentGroup(n,t,r,o){let i=this.createSegments(n,t.segments,r,o),s={};return Object.entries(t.children).forEach(([a,c])=>{s[a]=this.createSegmentGroup(n,c,r,o)}),new V(i,s)}createSegments(n,t,r,o){return t.map(i=>i.path[0]===":"?this.findPosParam(n,i,o):this.findOrReturn(i,r))}findPosParam(n,t,r){let o=r[t.path.substring(1)];if(!o)throw new C(4001,!1);return o}findOrReturn(n,t){let r=0;for(let o of t){if(o.path===n.path)return t.splice(r),o;r++}return n}};function JS(e,n,t){if(typeof e=="string")return b(e);let r=e,{queryParams:o,fragment:i,routeConfig:s,url:a,outlet:c,params:u,data:l,title:d}=n;return Ot(pe(t,()=>r({params:u,data:l,queryParams:o,fragment:i,routeConfig:s,url:a,outlet:c,title:d})))}var vf={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function XS(e,n,t,r,o){let i=jy(e,n,t);return i.matched?(r=CS(n,r),YS(r,n,t,o).pipe(N(s=>s===!0?i:g({},vf)))):b(i)}function jy(e,n,t){if(n.path==="**")return eM(t);if(n.path==="")return n.pathMatch==="full"&&(e.hasChildren()||t.length>0)?g({},vf):{matched:!0,consumedSegments:[],remainingSegments:t,parameters:{},positionalParamSegments:{}};let o=(n.matcher||hy)(t,e,n);if(!o)return g({},vf);let i={};Object.entries(o.posParams??{}).forEach(([a,c])=>{i[a]=c.path});let s=o.consumed.length>0?g(g({},i),o.consumed[o.consumed.length-1].parameters):i;return{matched:!0,consumedSegments:o.consumed,remainingSegments:t.slice(o.consumed.length),parameters:s,positionalParamSegments:o.posParams??{}}}function eM(e){return{matched:!0,parameters:e.length>0?gy(e).parameters:{},consumedSegments:e,remainingSegments:[],positionalParamSegments:{}}}function ly(e,n,t,r){return t.length>0&&rM(e,t,r)?{segmentGroup:new V(n,nM(r,new V(t,e.children))),slicedSegments:[]}:t.length===0&&oM(e,t,r)?{segmentGroup:new V(e.segments,tM(e,t,r,e.children)),slicedSegments:t}:{segmentGroup:new V(e.segments,e.children),slicedSegments:t}}function tM(e,n,t,r){let o={};for(let i of t)if(lc(e,n,i)&&!r[ot(i)]){let s=new V([],{});o[ot(i)]=s}return g(g({},r),o)}function nM(e,n){let t={};t[M]=n;for(let r of e)if(r.path===""&&ot(r)!==M){let o=new V([],{});t[ot(r)]=o}return t}function rM(e,n,t){return t.some(r=>lc(e,n,r)&&ot(r)!==M)}function oM(e,n,t){return t.some(r=>lc(e,n,r))}function lc(e,n,t){return(e.hasChildren()||n.length>0)&&t.pathMatch==="full"?!1:t.path===""}function iM(e,n,t){return n.length===0&&!e.children[t]}var yf=class{};function sM(e,n,t,r,o,i,s="emptyOnly"){return new Df(e,n,t,r,o,s,i).recognize()}var aM=31,Df=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(n,t,r,o,i,s,a){this.injector=n,this.configLoader=t,this.rootComponentType=r,this.config=o,this.urlTree=i,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new mf(this.urlSerializer,this.urlTree)}noMatchError(n){return new C(4002,`'${n.segmentGroup}'`)}recognize(){let n=ly(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(n).pipe(N(({children:t,rootSnapshot:r})=>{let o=new Fe(r,t),i=new ci("",o),s=by(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,i.url=this.urlSerializer.serialize(s),{state:i,tree:s}}))}match(n){let t=new zn([],Object.freeze({}),Object.freeze(g({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),M,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,n,M,t).pipe(N(r=>({children:r,rootSnapshot:t})),it(r=>{if(r instanceof di)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof li?this.noMatchError(r):r}))}processSegmentGroup(n,t,r,o,i){return r.segments.length===0&&r.hasChildren()?this.processChildren(n,t,r,i):this.processSegment(n,t,r,r.segments,o,!0,i).pipe(N(s=>s instanceof Fe?[s]:[]))}processChildren(n,t,r,o){let i=[];for(let s of Object.keys(r.children))s==="primary"?i.unshift(s):i.push(s);return G(i).pipe(st(s=>{let a=r.children[s],c=wS(t,s);return this.processSegmentGroup(n,c,a,s,o)}),cu((s,a)=>(s.push(...a),s)),Vt(null),au(),Z(s=>{if(s===null)return jr(r);let a=Vy(s);return cM(a),b(a)}))}processSegment(n,t,r,o,i,s,a){return G(t).pipe(st(c=>this.processSegmentAgainstRoute(c._injector??n,t,c,r,o,i,s,a).pipe(it(u=>{if(u instanceof li)return b(null);throw u}))),_t(c=>!!c),it(c=>{if(Fy(c))return iM(r,o,i)?b(new yf):jr(r);throw c}))}processSegmentAgainstRoute(n,t,r,o,i,s,a,c){return ot(r)!==s&&(s===M||!lc(o,i,r))?jr(o):r.redirectTo===void 0?this.matchSegmentAgainstRoute(n,o,r,i,s,c):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(n,o,t,r,i,s,c):jr(o)}expandSegmentAgainstRouteUsingRedirect(n,t,r,o,i,s,a){let{matched:c,parameters:u,consumedSegments:l,positionalParamSegments:d,remainingSegments:h}=jy(t,o,i);if(!c)return jr(t);typeof o.redirectTo=="string"&&o.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>aM&&(this.allowRedirects=!1));let f=new zn(i,u,Object.freeze(g({},this.urlTree.queryParams)),this.urlTree.fragment,dy(o),ot(o),o.component??o._loadedComponent??null,o,fy(o)),m=sc(f,a,this.paramsInheritanceStrategy);return f.params=Object.freeze(m.params),f.data=Object.freeze(m.data),this.applyRedirects.applyRedirectCommands(l,o.redirectTo,d,f,n).pipe(ie(w=>this.applyRedirects.lineralizeSegments(o,w)),Z(w=>this.processSegment(n,r,t,w.concat(h),s,!1,a)))}matchSegmentAgainstRoute(n,t,r,o,i,s){let a=XS(t,r,o,n,this.urlSerializer);return r.path==="**"&&(t.children={}),a.pipe(ie(c=>c.matched?(n=r._injector??n,this.getChildConfig(n,r,o).pipe(ie(({routes:u})=>{let l=r._loadedInjector??n,{parameters:d,consumedSegments:h,remainingSegments:f}=c,m=new zn(h,d,Object.freeze(g({},this.urlTree.queryParams)),this.urlTree.fragment,dy(r),ot(r),r.component??r._loadedComponent??null,r,fy(r)),y=sc(m,s,this.paramsInheritanceStrategy);m.params=Object.freeze(y.params),m.data=Object.freeze(y.data);let{segmentGroup:w,slicedSegments:T}=ly(t,h,f,u);if(T.length===0&&w.hasChildren())return this.processChildren(l,u,w,m).pipe(N(K=>new Fe(m,K)));if(u.length===0&&T.length===0)return b(new Fe(m,[]));let Ct=ot(r)===i;return this.processSegment(l,u,w,T,Ct?M:i,!0,m).pipe(N(K=>new Fe(m,K instanceof Fe?[K]:[])))}))):jr(t)))}getChildConfig(n,t,r){return t.children?b({routes:t.children,injector:n}):t.loadChildren?t._loadedRoutes!==void 0?b({routes:t._loadedRoutes,injector:t._loadedInjector}):ZS(n,t,r,this.urlSerializer).pipe(Z(o=>o?this.configLoader.loadChildren(n,t).pipe(ce(i=>{t._loadedRoutes=i.routes,t._loadedInjector=i.injector})):KS(t))):b({routes:[],injector:n})}};function cM(e){e.sort((n,t)=>n.value.outlet===M?-1:t.value.outlet===M?1:n.value.outlet.localeCompare(t.value.outlet))}function uM(e){let n=e.value.routeConfig;return n&&n.path===""}function Vy(e){let n=[],t=new Set;for(let r of e){if(!uM(r)){n.push(r);continue}let o=n.find(i=>r.value.routeConfig===i.value.routeConfig);o!==void 0?(o.children.push(...r.children),t.add(o)):n.push(r)}for(let r of t){let o=Vy(r.children);n.push(new Fe(r.value,o))}return n.filter(r=>!t.has(r))}function dy(e){return e.data||{}}function fy(e){return e.resolve||{}}function lM(e,n,t,r,o,i){return Z(s=>sM(e,n,t,r,s.extractedUrl,o,i).pipe(N(({state:a,tree:c})=>A(g({},s),{targetSnapshot:a,urlAfterRedirects:c}))))}function dM(e,n){return Z(t=>{let{targetSnapshot:r,guards:{canActivateChecks:o}}=t;if(!o.length)return b(t);let i=new Set(o.map(c=>c.route)),s=new Set;for(let c of i)if(!s.has(c))for(let u of By(c))s.add(u);let a=0;return G(s).pipe(st(c=>i.has(c)?fM(c,r,e,n):(c.data=sc(c,c.parent,e).resolve,b(void 0))),ce(()=>a++),fr(1),Z(c=>a===s.size?b(t):we))})}function By(e){let n=e.children.map(t=>By(t)).flat();return[e,...n]}function fM(e,n,t,r){let o=e.routeConfig,i=e._resolve;return o?.title!==void 0&&!Ny(o)&&(i[fi]=o.title),co(()=>(e.data=sc(e,e.parent,t).resolve,hM(i,e,n,r).pipe(N(s=>(e._resolvedData=s,e.data=g(g({},e.data),s),null)))))}function hM(e,n,t,r){let o=af(e);if(o.length===0)return b({});let i={};return G(o).pipe(Z(s=>pM(e[s],n,t,r).pipe(_t(),ce(a=>{if(a instanceof qr)throw ac(new an,a);i[s]=a}))),fr(1),N(()=>i),it(s=>Fy(s)?we:cr(s)))}function pM(e,n,t,r){let o=hi(n)??r,i=Yr(e,o),s=i.resolve?i.resolve(n,t):pe(o,()=>i(n,t));return Ot(s)}function of(e){return ie(n=>{let t=e(n);return t?G(t).pipe(N(()=>n)):b(n)})}var _f=(()=>{class e{buildTitle(t){let r,o=t.root;for(;o!==void 0;)r=this.getResolvedTitleForRoute(o)??r,o=o.children.find(i=>i.outlet===M);return r}getResolvedTitleForRoute(t){return t.data[fi]}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:()=>p(Uy),providedIn:"root"})}return e})(),Uy=(()=>{class e extends _f{title;constructor(t){super(),this.title=t}updateTitle(t){let r=this.buildTitle(t);r!==void 0&&this.title.setTitle(r)}static \u0275fac=function(r){return new(r||e)(I(iy))};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),un=new D("",{providedIn:"root",factory:()=>({})}),Wn=new D(""),dc=(()=>{class e{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=p(kd);loadComponent(t){if(this.componentLoaders.get(t))return this.componentLoaders.get(t);if(t._loadedComponent)return b(t._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(t);let r=Ot(t.loadComponent()).pipe(N($y),ce(i=>{this.onLoadEndListener&&this.onLoadEndListener(t),t._loadedComponent=i}),Bt(()=>{this.componentLoaders.delete(t)})),o=new sr(r,()=>new H).pipe(ir());return this.componentLoaders.set(t,o),o}loadChildren(t,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return b({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let i=Hy(r,this.compiler,t,this.onLoadEndListener).pipe(Bt(()=>{this.childrenLoaders.delete(r)})),s=new sr(i,()=>new H).pipe(ir());return this.childrenLoaders.set(r,s),s}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Hy(e,n,t,r){return Ot(e.loadChildren()).pipe(N($y),Z(o=>o instanceof ma||Array.isArray(o)?b(o):G(n.compileModuleAsync(o))),N(o=>{r&&r(e);let i,s,a=!1;return Array.isArray(o)?(s=o,a=!0):(i=o.create(t).injector,s=i.get(Wn,[],{optional:!0,self:!0}).flat()),{routes:s.map(bf),injector:i}}))}function gM(e){return e&&typeof e=="object"&&"default"in e}function $y(e){return gM(e)?e.default:e}var fc=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:()=>p(mM),providedIn:"root"})}return e})(),mM=(()=>{class e{shouldProcessUrl(t){return!0}extract(t){return t}merge(t,r){return t}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Tf=new D(""),Sf=new D("");function zy(e,n,t){let r=e.get(Sf),o=e.get(q);if(!o.startViewTransition||r.skipNextTransition)return r.skipNextTransition=!1,new Promise(u=>setTimeout(u));let i,s=new Promise(u=>{i=u}),a=o.startViewTransition(()=>(i(),vM(e))),{onViewTransitionCreated:c}=r;return c&&pe(e,()=>c({transition:a,from:n,to:t})),s}function vM(e){return new Promise(n=>{wd({read:()=>setTimeout(n)},{injector:e})})}var Mf=new D(""),hc=(()=>{class e{currentNavigation=null;currentTransition=null;lastSuccessfulNavigation=null;events=new H;transitionAbortWithErrorSubject=new H;configLoader=p(dc);environmentInjector=p(z);destroyRef=p(Qt);urlSerializer=p(kt);rootContexts=p(Et);location=p(rt);inputBindingEnabled=p(pi,{optional:!0})!==null;titleStrategy=p(_f);options=p(un,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=p(fc);createViewTransition=p(Tf,{optional:!0});navigationErrorHandler=p(Mf,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>b(void 0);rootComponentType=null;destroyed=!1;constructor(){let t=o=>this.events.next(new Ja(o)),r=o=>this.events.next(new Xa(o));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=t,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){this.transitions?.complete()}handleNavigationRequest(t){let r=++this.navigationId;this.transitions?.next(A(g({},t),{extractedUrl:this.urlHandlingStrategy.extract(t.rawUrl),targetSnapshot:null,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null,abortController:new AbortController,id:r}))}setupNavigations(t){return this.transitions=new te(null),this.transitions.pipe(oe(r=>r!==null),ie(r=>{let o=!1;return b(r).pipe(ie(i=>{if(this.navigationId>r.id)return this.cancelNavigationTransition(r,"",Ee.SupersededByNewNavigation),we;this.currentTransition=r,this.currentNavigation={id:i.id,initialUrl:i.rawUrl,extractedUrl:i.extractedUrl,targetBrowserUrl:typeof i.extras.browserUrl=="string"?this.urlSerializer.parse(i.extras.browserUrl):i.extras.browserUrl,trigger:i.source,extras:i.extras,previousNavigation:this.lastSuccessfulNavigation?A(g({},this.lastSuccessfulNavigation),{previousNavigation:null}):null,abort:()=>i.abortController.abort()};let s=!t.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),a=i.extras.onSameUrlNavigation??t.onSameUrlNavigation;if(!s&&a!=="reload"){let c="";return this.events.next(new Dt(i.id,this.urlSerializer.serialize(i.rawUrl),c,$r.IgnoredSameUrlNavigation)),i.resolve(!1),we}if(this.urlHandlingStrategy.shouldProcessUrl(i.rawUrl))return b(i).pipe(ie(c=>(this.events.next(new yt(c.id,this.urlSerializer.serialize(c.extractedUrl),c.source,c.restoredState)),c.id!==this.navigationId?we:Promise.resolve(c))),lM(this.environmentInjector,this.configLoader,this.rootComponentType,t.config,this.urlSerializer,this.paramsInheritanceStrategy),ce(c=>{r.targetSnapshot=c.targetSnapshot,r.urlAfterRedirects=c.urlAfterRedirects,this.currentNavigation=A(g({},this.currentNavigation),{finalUrl:c.urlAfterRedirects});let u=new ii(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(u)}));if(s&&this.urlHandlingStrategy.shouldProcessUrl(i.currentRawUrl)){let{id:c,extractedUrl:u,source:l,restoredState:d,extras:h}=i,f=new yt(c,this.urlSerializer.serialize(u),l,d);this.events.next(f);let m=Ay(this.rootComponentType).snapshot;return this.currentTransition=r=A(g({},i),{targetSnapshot:m,urlAfterRedirects:u,extras:A(g({},h),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=u,b(r)}else{let c="";return this.events.next(new Dt(i.id,this.urlSerializer.serialize(i.extractedUrl),c,$r.IgnoredByUrlHandlingStrategy)),i.resolve(!1),we}}),ce(i=>{let s=new Za(i.id,this.urlSerializer.serialize(i.extractedUrl),this.urlSerializer.serialize(i.urlAfterRedirects),i.targetSnapshot);this.events.next(s)}),N(i=>(this.currentTransition=r=A(g({},i),{guards:AS(i.targetSnapshot,i.currentSnapshot,this.rootContexts)}),r)),BS(this.environmentInjector,i=>this.events.next(i)),ce(i=>{if(r.guardsResult=i.guardsResult,i.guardsResult&&typeof i.guardsResult!="boolean")throw ac(this.urlSerializer,i.guardsResult);let s=new Ya(i.id,this.urlSerializer.serialize(i.extractedUrl),this.urlSerializer.serialize(i.urlAfterRedirects),i.targetSnapshot,!!i.guardsResult);this.events.next(s)}),oe(i=>i.guardsResult?!0:(this.cancelNavigationTransition(i,"",Ee.GuardRejected),!1)),of(i=>{if(i.guards.canActivateChecks.length!==0)return b(i).pipe(ce(s=>{let a=new Qa(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}),ie(s=>{let a=!1;return b(s).pipe(dM(this.paramsInheritanceStrategy,this.environmentInjector),ce({next:()=>a=!0,complete:()=>{a||this.cancelNavigationTransition(s,"",Ee.NoDataFromResolver)}}))}),ce(s=>{let a=new Ka(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}))}),of(i=>{let s=a=>{let c=[];a.routeConfig?.loadComponent&&!a.routeConfig._loadedComponent&&c.push(this.configLoader.loadComponent(a.routeConfig).pipe(ce(u=>{a.component=u}),N(()=>{})));for(let u of a.children)c.push(...s(u));return c};return mn(s(i.targetSnapshot.root)).pipe(Vt(null),bt(1))}),of(()=>this.afterPreactivation()),ie(()=>{let{currentSnapshot:i,targetSnapshot:s}=r,a=this.createViewTransition?.(this.environmentInjector,i.root,s.root);return a?G(a).pipe(N(()=>r)):b(r)}),N(i=>{let s=bS(t.routeReuseStrategy,i.targetSnapshot,i.currentRouterState);return this.currentTransition=r=A(g({},i),{targetRouterState:s}),this.currentNavigation.targetRouterState=s,r}),ce(()=>{this.events.next(new si)}),MS(this.rootContexts,t.routeReuseStrategy,i=>this.events.next(i),this.inputBindingEnabled),bt(1),ds(new O(i=>{let s=r.abortController.signal,a=()=>i.next();return s.addEventListener("abort",a),()=>s.removeEventListener("abort",a)}).pipe(oe(()=>!o&&!r.targetRouterState),ce(()=>{this.cancelNavigationTransition(r,r.abortController.signal.reason+"",Ee.Aborted)}))),ce({next:i=>{o=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new We(i.id,this.urlSerializer.serialize(i.extractedUrl),this.urlSerializer.serialize(i.urlAfterRedirects))),this.titleStrategy?.updateTitle(i.targetRouterState.snapshot),i.resolve(!0)},complete:()=>{o=!0}}),ds(this.transitionAbortWithErrorSubject.pipe(ce(i=>{throw i}))),Bt(()=>{o||this.cancelNavigationTransition(r,"",Ee.SupersededByNewNavigation),this.currentTransition?.id===r.id&&(this.currentNavigation=null,this.currentTransition=null)}),it(i=>{if(this.destroyed)return r.resolve(!1),we;if(o=!0,Py(i))this.events.next(new mt(r.id,this.urlSerializer.serialize(r.extractedUrl),i.message,i.cancellationCode)),SS(i)?this.events.next(new Wr(i.url,i.navigationBehaviorOptions)):r.resolve(!1);else{let s=new zr(r.id,this.urlSerializer.serialize(r.extractedUrl),i,r.targetSnapshot??void 0);try{let a=pe(this.environmentInjector,()=>this.navigationErrorHandler?.(s));if(a instanceof qr){let{message:c,cancellationCode:u}=ac(this.urlSerializer,a);this.events.next(new mt(r.id,this.urlSerializer.serialize(r.extractedUrl),c,u)),this.events.next(new Wr(a.redirectTo,a.navigationBehaviorOptions))}else throw this.events.next(s),i}catch(a){this.options.resolveNavigationPromiseOnError?r.resolve(!1):r.reject(a)}}return we}))}))}cancelNavigationTransition(t,r,o){let i=new mt(t.id,this.urlSerializer.serialize(t.extractedUrl),r,o);this.events.next(i),t.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let t=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return t.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function yM(e){return e!==Ur}var Gy=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:()=>p(DM),providedIn:"root"})}return e})(),uc=class{shouldDetach(n){return!1}store(n,t){}shouldAttach(n){return!1}retrieve(n){return null}shouldReuseRoute(n,t){return n.routeConfig===t.routeConfig}},DM=(()=>{class e extends uc{static \u0275fac=(()=>{let t;return function(o){return(t||(t=Xt(e)))(o||e)}})();static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Wy=(()=>{class e{urlSerializer=p(kt);options=p(un,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";location=p(rt);urlHandlingStrategy=p(fc);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new vt;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}createBrowserPath({finalUrl:t,initialUrl:r,targetBrowserUrl:o}){let i=t!==void 0?this.urlHandlingStrategy.merge(t,r):r,s=o??i;return s instanceof vt?this.urlSerializer.serialize(s):s}commitTransition({targetRouterState:t,finalUrl:r,initialUrl:o}){r&&t?(this.currentUrlTree=r,this.rawUrlTree=this.urlHandlingStrategy.merge(r,o),this.routerState=t):this.rawUrlTree=o}routerState=Ay(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();updateStateMemento(){this.stateMemento=this.createStateMemento()}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}resetInternalState({finalUrl:t}){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,t??this.rawUrlTree)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:()=>p(EM),providedIn:"root"})}return e})(),EM=(()=>{class e extends Wy{currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}registerNonRouterCurrentEntryChangeListener(t){return this.location.subscribe(r=>{r.type==="popstate"&&setTimeout(()=>{t(r.url,r.state,"popstate")})})}handleRouterEvent(t,r){t instanceof yt?this.updateStateMemento():t instanceof Dt?this.commitTransition(r):t instanceof ii?this.urlUpdateStrategy==="eager"&&(r.extras.skipLocationChange||this.setBrowserUrl(this.createBrowserPath(r),r)):t instanceof si?(this.commitTransition(r),this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(this.createBrowserPath(r),r)):t instanceof mt&&t.code!==Ee.SupersededByNewNavigation&&t.code!==Ee.Redirect?this.restoreHistory(r):t instanceof zr?this.restoreHistory(r,!0):t instanceof We&&(this.lastSuccessfulId=t.id,this.currentPageId=this.browserPageId)}setBrowserUrl(t,{extras:r,id:o}){let{replaceUrl:i,state:s}=r;if(this.location.isCurrentPathEqualTo(t)||i){let a=this.browserPageId,c=g(g({},s),this.generateNgRouterState(o,a));this.location.replaceState(t,"",c)}else{let a=g(g({},s),this.generateNgRouterState(o,this.browserPageId+1));this.location.go(t,"",a)}}restoreHistory(t,r=!1){if(this.canceledNavigationResolution==="computed"){let o=this.browserPageId,i=this.currentPageId-o;i!==0?this.location.historyGo(i):this.getCurrentUrlTree()===t.finalUrl&&i===0&&(this.resetInternalState(t),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetInternalState(t),this.resetUrlToCurrentUrlTree())}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.getRawUrlTree()),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(t,r){return this.canceledNavigationResolution==="computed"?{navigationId:t,\u0275routerPageId:r}:{navigationId:t}}static \u0275fac=(()=>{let t;return function(o){return(t||(t=Xt(e)))(o||e)}})();static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function pc(e,n){e.events.pipe(oe(t=>t instanceof We||t instanceof mt||t instanceof zr||t instanceof Dt),N(t=>t instanceof We||t instanceof Dt?0:(t instanceof mt?t.code===Ee.Redirect||t.code===Ee.SupersededByNewNavigation:!1)?2:1),oe(t=>t!==2),bt(1)).subscribe(()=>{n()})}var CM={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},wM={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},fe=(()=>{class e{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=p(Id);stateManager=p(Wy);options=p(un,{optional:!0})||{};pendingTasks=p(At);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=p(hc);urlSerializer=p(kt);location=p(rt);urlHandlingStrategy=p(fc);injector=p(z);_events=new H;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=p(Gy);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=p(Wn,{optional:!0})?.flat()??[];componentInputBindingEnabled=!!p(pi,{optional:!0});constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this).subscribe({error:t=>{this.console.warn(t)}}),this.subscribeToNavigationEvents()}eventsSubscription=new ee;subscribeToNavigationEvents(){let t=this.navigationTransitions.events.subscribe(r=>{try{let o=this.navigationTransitions.currentTransition,i=this.navigationTransitions.currentNavigation;if(o!==null&&i!==null){if(this.stateManager.handleRouterEvent(r,i),r instanceof mt&&r.code!==Ee.Redirect&&r.code!==Ee.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof We)this.navigated=!0;else if(r instanceof Wr){let s=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,o.currentRawUrl),c=g({browserUrl:o.extras.browserUrl,info:o.extras.info,skipLocationChange:o.extras.skipLocationChange,replaceUrl:o.extras.replaceUrl||this.urlUpdateStrategy==="eager"||yM(o.source)},s);this.scheduleNavigation(a,Ur,null,c,{resolve:o.resolve,reject:o.reject,promise:o.promise})}}ES(r)&&this._events.next(r)}catch(o){this.navigationTransitions.transitionAbortWithErrorSubject.next(o)}});this.eventsSubscription.add(t)}resetRootComponentType(t){this.routerState.root.component=t,this.navigationTransitions.rootComponentType=t}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),Ur,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((t,r,o)=>{this.navigateToSyncWithBrowser(t,o,r)})}navigateToSyncWithBrowser(t,r,o){let i={replaceUrl:!0},s=o?.navigationId?o:null;if(o){let c=g({},o);delete c.navigationId,delete c.\u0275routerPageId,Object.keys(c).length!==0&&(i.state=c)}let a=this.parseUrl(t);this.scheduleNavigation(a,r,s,i).catch(c=>{this.injector.get(Me)(c)})}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(t){this.config=t.map(bf),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(t,r={}){let{relativeTo:o,queryParams:i,fragment:s,queryParamsHandling:a,preserveFragment:c}=r,u=c?this.currentUrlTree.fragment:s,l=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":l=g(g({},this.currentUrlTree.queryParams),i);break;case"preserve":l=this.currentUrlTree.queryParams;break;default:l=i||null}l!==null&&(l=this.removeEmptyProps(l));let d;try{let h=o?o.snapshot:this.routerState.snapshot.root;d=_y(h)}catch{(typeof t[0]!="string"||t[0][0]!=="/")&&(t=[]),d=this.currentUrlTree.root}return Ty(d,t,l,u??null)}navigateByUrl(t,r={skipLocationChange:!1}){let o=cn(t)?t:this.parseUrl(t),i=this.urlHandlingStrategy.merge(o,this.rawUrlTree);return this.scheduleNavigation(i,Ur,null,r)}navigate(t,r={skipLocationChange:!1}){return IM(t),this.navigateByUrl(this.createUrlTree(t,r),r)}serializeUrl(t){return this.urlSerializer.serialize(t)}parseUrl(t){try{return this.urlSerializer.parse(t)}catch{return this.urlSerializer.parse("/")}}isActive(t,r){let o;if(r===!0?o=g({},CM):r===!1?o=g({},wM):o=r,cn(t))return sy(this.currentUrlTree,t,o);let i=this.parseUrl(t);return sy(this.currentUrlTree,i,o)}removeEmptyProps(t){return Object.entries(t).reduce((r,[o,i])=>(i!=null&&(r[o]=i),r),{})}scheduleNavigation(t,r,o,i,s){if(this.disposed)return Promise.resolve(!1);let a,c,u;s?(a=s.resolve,c=s.reject,u=s.promise):u=new Promise((d,h)=>{a=d,c=h});let l=this.pendingTasks.add();return pc(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(l))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:o,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:t,extras:i,resolve:a,reject:c,promise:u,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),u.catch(d=>Promise.reject(d))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function IM(e){for(let n=0;n<e.length;n++)if(e[n]==null)throw new C(4008,!1)}var vi=(()=>{class e{router;route;tabIndexAttribute;renderer;el;locationStrategy;reactiveHref=Mt(null);get href(){return $e(this.reactiveHref)}set href(t){this.reactiveHref.set(t)}target;queryParams;fragment;queryParamsHandling;state;info;relativeTo;isAnchorElement;subscription;onChanges=new H;applicationErrorHandler=p(Me);options=p(un,{optional:!0});constructor(t,r,o,i,s,a){this.router=t,this.route=r,this.tabIndexAttribute=o,this.renderer=i,this.el=s,this.locationStrategy=a,this.reactiveHref.set(p(new wa("href"),{optional:!0}));let c=s.nativeElement.tagName?.toLowerCase();this.isAnchorElement=c==="a"||c==="area"||!!(typeof customElements=="object"&&customElements.get(c)?.observedAttributes?.includes?.("href")),this.isAnchorElement?this.setTabIndexIfNotOnNativeEl("0"):this.subscribeToNavigationEventsIfNecessary()}subscribeToNavigationEventsIfNecessary(){if(this.subscription!==void 0||!this.isAnchorElement)return;let t=this.preserveFragment,r=o=>o==="merge"||o==="preserve";t||=r(this.queryParamsHandling),t||=!this.queryParamsHandling&&!r(this.options?.defaultQueryParamsHandling),t&&(this.subscription=this.router.events.subscribe(o=>{o instanceof We&&this.updateHref()}))}preserveFragment=!1;skipLocationChange=!1;replaceUrl=!1;setTabIndexIfNotOnNativeEl(t){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",t)}ngOnChanges(t){this.isAnchorElement&&(this.updateHref(),this.subscribeToNavigationEventsIfNecessary()),this.onChanges.next(this)}routerLinkInput=null;set routerLink(t){t==null?(this.routerLinkInput=null,this.setTabIndexIfNotOnNativeEl(null)):(cn(t)?this.routerLinkInput=t:this.routerLinkInput=Array.isArray(t)?t:[t],this.setTabIndexIfNotOnNativeEl("0"))}onClick(t,r,o,i,s){let a=this.urlTree;if(a===null||this.isAnchorElement&&(t!==0||r||o||i||s||typeof this.target=="string"&&this.target!="_self"))return!0;let c={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(a,c)?.catch(u=>{this.applicationErrorHandler(u)}),!this.isAnchorElement}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){let t=this.urlTree;this.reactiveHref.set(t!==null&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(t))??"":null)}applyAttributeValue(t,r){let o=this.renderer,i=this.el.nativeElement;r!==null?o.setAttribute(i,t,r):o.removeAttribute(i,t)}get urlTree(){return this.routerLinkInput===null?null:cn(this.routerLinkInput)?this.routerLinkInput:this.router.createUrlTree(this.routerLinkInput,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static \u0275fac=function(r){return new(r||e)(v(fe),v(Ce),en("tabindex"),v(tn),v(Q),v(Ae))};static \u0275dir=j({type:e,selectors:[["","routerLink",""]],hostVars:2,hostBindings:function(r,o){r&1&&Pe("click",function(s){return o.onClick(s.button,s.ctrlKey,s.shiftKey,s.altKey,s.metaKey)}),r&2&&Vn("href",o.reactiveHref(),Xl)("target",o.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[2,"preserveFragment","preserveFragment",Bn],skipLocationChange:[2,"skipLocationChange","skipLocationChange",Bn],replaceUrl:[2,"replaceUrl","replaceUrl",Bn],routerLink:"routerLink"},features:[De]})}return e})();var mi=class{},bM=(()=>{class e{preload(t,r){return r().pipe(it(()=>b(null)))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var qy=(()=>{class e{router;injector;preloadingStrategy;loader;subscription;constructor(t,r,o,i){this.router=t,this.injector=r,this.preloadingStrategy=o,this.loader=i}setUpPreloading(){this.subscription=this.router.events.pipe(oe(t=>t instanceof We),st(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(t,r){let o=[];for(let i of r){i.providers&&!i._injector&&(i._injector=Nr(i.providers,t,`Route: ${i.path}`));let s=i._injector??t,a=i._loadedInjector??s;(i.loadChildren&&!i._loadedRoutes&&i.canLoad===void 0||i.loadComponent&&!i._loadedComponent)&&o.push(this.preloadConfig(s,i)),(i.children||i._loadedRoutes)&&o.push(this.processRoutes(a,i.children??i._loadedRoutes))}return G(o).pipe(lr())}preloadConfig(t,r){return this.preloadingStrategy.preload(r,()=>{let o;r.loadChildren&&r.canLoad===void 0?o=this.loader.loadChildren(t,r):o=b(null);let i=o.pipe(Z(s=>s===null?b(void 0):(r._loadedRoutes=s.routes,r._loadedInjector=s.injector,this.processRoutes(s.injector??t,s.routes))));if(r.loadComponent&&!r._loadedComponent){let s=this.loader.loadComponent(r);return G([i,s]).pipe(lr())}else return i})}static \u0275fac=function(r){return new(r||e)(I(fe),I(z),I(mi),I(dc))};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Zy=new D(""),_M=(()=>{class e{urlSerializer;transitions;viewportScroller;zone;options;routerEventsSubscription;scrollEventsSubscription;lastId=0;lastSource=Ur;restoredId=0;store={};constructor(t,r,o,i,s={}){this.urlSerializer=t,this.transitions=r,this.viewportScroller=o,this.zone=i,this.options=s,s.scrollPositionRestoration||="disabled",s.anchorScrolling||="disabled"}init(){this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(t=>{t instanceof yt?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=t.navigationTrigger,this.restoredId=t.restoredState?t.restoredState.navigationId:0):t instanceof We?(this.lastId=t.id,this.scheduleScrollEvent(t,this.urlSerializer.parse(t.urlAfterRedirects).fragment)):t instanceof Dt&&t.code===$r.IgnoredSameUrlNavigation&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(t,this.urlSerializer.parse(t.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(t=>{t instanceof Gr&&(t.position?this.options.scrollPositionRestoration==="top"?this.viewportScroller.scrollToPosition([0,0]):this.options.scrollPositionRestoration==="enabled"&&this.viewportScroller.scrollToPosition(t.position):t.anchor&&this.options.anchorScrolling==="enabled"?this.viewportScroller.scrollToAnchor(t.anchor):this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(t,r){this.zone.runOutsideAngular(()=>X(this,null,function*(){yield new Promise(o=>{setTimeout(o),typeof requestAnimationFrame<"u"&&requestAnimationFrame(o)}),this.zone.run(()=>{this.transitions.events.next(new Gr(t,this.lastSource==="popstate"?this.store[this.restoredId]:null,r))})}))}ngOnDestroy(){this.routerEventsSubscription?.unsubscribe(),this.scrollEventsSubscription?.unsubscribe()}static \u0275fac=function(r){pd()};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})();function TM(e,...n){return bn([{provide:Wn,multi:!0,useValue:e},[],{provide:Ce,useFactory:Yy,deps:[fe]},{provide:jo,multi:!0,useFactory:Qy},n.map(t=>t.\u0275providers)])}function Yy(e){return e.routerState.root}function yi(e,n){return{\u0275kind:e,\u0275providers:n}}function Qy(){let e=p(se);return n=>{let t=e.get(Nt);if(n!==t.components[0])return;let r=e.get(fe),o=e.get(Ky);e.get(Rf)===1&&r.initialNavigation(),e.get(eD,null,{optional:!0})?.setUpPreloading(),e.get(Zy,null,{optional:!0})?.init(),r.resetRootComponentType(t.componentTypes[0]),o.closed||(o.next(),o.complete(),o.unsubscribe())}}var Ky=new D("",{factory:()=>new H}),Rf=new D("",{providedIn:"root",factory:()=>1});function Jy(){let e=[{provide:Rf,useValue:0},ya(()=>{let n=p(se);return n.get(Hd,Promise.resolve()).then(()=>new Promise(r=>{let o=n.get(fe),i=n.get(Ky);pc(o,()=>{r(!0)}),n.get(hc).afterPreactivation=()=>(r(!0),i.closed?b(void 0):i),o.initialNavigation()}))})];return yi(2,e)}function Xy(){let e=[ya(()=>{p(fe).setUpLocationChangeListener()}),{provide:Rf,useValue:2}];return yi(3,e)}var eD=new D("");function tD(e){return yi(0,[{provide:eD,useExisting:qy},{provide:mi,useExisting:e}])}function nD(){return yi(8,[wf,{provide:pi,useExisting:wf}])}function rD(e){Rr("NgRouterViewTransitions");let n=[{provide:Tf,useValue:zy},{provide:Sf,useValue:g({skipNextTransition:!!e?.skipInitialTransition},e)}];return yi(9,n)}var oD=[rt,{provide:kt,useClass:an},fe,Et,{provide:Ce,useFactory:Yy,deps:[fe]},dc,[]],SM=(()=>{class e{constructor(){}static forRoot(t,r){return{ngModule:e,providers:[oD,[],{provide:Wn,multi:!0,useValue:t},[],r?.errorHandler?{provide:Mf,useValue:r.errorHandler}:[],{provide:un,useValue:r||{}},r?.useHash?AM():RM(),MM(),r?.preloadingStrategy?tD(r.preloadingStrategy).\u0275providers:[],r?.initialNavigation?NM(r):[],r?.bindToComponentInputs?nD().\u0275providers:[],r?.enableViewTransitions?rD().\u0275providers:[],xM()]}}static forChild(t){return{ngModule:e,providers:[{provide:Wn,multi:!0,useValue:t}]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=ke({type:e});static \u0275inj=_e({})}return e})();function MM(){return{provide:Zy,useFactory:()=>{let e=p(Nv),n=p(B),t=p(un),r=p(hc),o=p(kt);return t.scrollOffset&&e.setOffset(t.scrollOffset),new _M(o,r,e,n,t)}}}function AM(){return{provide:Ae,useClass:$d}}function RM(){return{provide:Ae,useClass:Sa}}function NM(e){return[e.initialNavigation==="disabled"?Xy().\u0275providers:[],e.initialNavigation==="enabledBlocking"?Jy().\u0275providers:[]]}var Af=new D("");function xM(){return[{provide:Af,useFactory:Qy},{provide:jo,multi:!0,useExisting:Af}]}var pD=(()=>{class e{_renderer;_elementRef;onChange=t=>{};onTouched=()=>{};constructor(t,r){this._renderer=t,this._elementRef=r}setProperty(t,r){this._renderer.setProperty(this._elementRef.nativeElement,t,r)}registerOnTouched(t){this.onTouched=t}registerOnChange(t){this.onChange=t}setDisabledState(t){this.setProperty("disabled",t)}static \u0275fac=function(r){return new(r||e)(v(tn),v(Q))};static \u0275dir=j({type:e})}return e})(),kM=(()=>{class e extends pD{static \u0275fac=(()=>{let t;return function(o){return(t||(t=Xt(e)))(o||e)}})();static \u0275dir=j({type:e,features:[He]})}return e})(),Lf=new D("");var PM={provide:Lf,useExisting:Ye(()=>gD),multi:!0};function FM(){let e=Ge()?Ge().getUserAgent():"";return/android (\d+)/.test(e.toLowerCase())}var LM=new D(""),gD=(()=>{class e extends pD{_compositionMode;_composing=!1;constructor(t,r,o){super(t,r),this._compositionMode=o,this._compositionMode==null&&(this._compositionMode=!FM())}writeValue(t){let r=t??"";this.setProperty("value",r)}_handleInput(t){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(t)}_compositionStart(){this._composing=!0}_compositionEnd(t){this._composing=!1,this._compositionMode&&this.onChange(t)}static \u0275fac=function(r){return new(r||e)(v(tn),v(Q),v(LM,8))};static \u0275dir=j({type:e,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(r,o){r&1&&Pe("input",function(s){return o._handleInput(s.target.value)})("blur",function(){return o.onTouched()})("compositionstart",function(){return o._compositionStart()})("compositionend",function(s){return o._compositionEnd(s.target.value)})},standalone:!1,features:[rn([PM]),He]})}return e})();function jf(e){return e==null||Vf(e)===0}function Vf(e){return e==null?null:Array.isArray(e)||typeof e=="string"?e.length:e instanceof Set?e.size:null}var bi=new D(""),Bf=new D(""),jM=/^(?=.{1,254}$)(?=.{1,64}@)[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+)*@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,iD=class{static min(n){return mD(n)}static max(n){return vD(n)}static required(n){return VM(n)}static requiredTrue(n){return BM(n)}static email(n){return UM(n)}static minLength(n){return HM(n)}static maxLength(n){return $M(n)}static pattern(n){return zM(n)}static nullValidator(n){return mc()}static compose(n){return ID(n)}static composeAsync(n){return _D(n)}};function mD(e){return n=>{if(n.value==null||e==null)return null;let t=parseFloat(n.value);return!isNaN(t)&&t<e?{min:{min:e,actual:n.value}}:null}}function vD(e){return n=>{if(n.value==null||e==null)return null;let t=parseFloat(n.value);return!isNaN(t)&&t>e?{max:{max:e,actual:n.value}}:null}}function VM(e){return jf(e.value)?{required:!0}:null}function BM(e){return e.value===!0?null:{required:!0}}function UM(e){return jf(e.value)||jM.test(e.value)?null:{email:!0}}function HM(e){return n=>{let t=n.value?.length??Vf(n.value);return t===null||t===0?null:t<e?{minlength:{requiredLength:e,actualLength:t}}:null}}function $M(e){return n=>{let t=n.value?.length??Vf(n.value);return t!==null&&t>e?{maxlength:{requiredLength:e,actualLength:t}}:null}}function zM(e){if(!e)return mc;let n,t;return typeof e=="string"?(t="",e.charAt(0)!=="^"&&(t+="^"),t+=e,e.charAt(e.length-1)!=="$"&&(t+="$"),n=new RegExp(t)):(t=e.toString(),n=e),r=>{if(jf(r.value))return null;let o=r.value;return n.test(o)?null:{pattern:{requiredPattern:t,actualValue:o}}}}function mc(e){return null}function yD(e){return e!=null}function DD(e){return nn(e)?G(e):e}function ED(e){let n={};return e.forEach(t=>{n=t!=null?g(g({},n),t):n}),Object.keys(n).length===0?null:n}function CD(e,n){return n.map(t=>t(e))}function GM(e){return!e.validate}function wD(e){return e.map(n=>GM(n)?n:t=>n.validate(t))}function ID(e){if(!e)return null;let n=e.filter(yD);return n.length==0?null:function(t){return ED(CD(t,n))}}function bD(e){return e!=null?ID(wD(e)):null}function _D(e){if(!e)return null;let n=e.filter(yD);return n.length==0?null:function(t){let r=CD(t,n).map(DD);return iu(r).pipe(N(ED))}}function TD(e){return e!=null?_D(wD(e)):null}function sD(e,n){return e===null?[n]:Array.isArray(e)?[...e,n]:[e,n]}function SD(e){return e._rawValidators}function MD(e){return e._rawAsyncValidators}function Nf(e){return e?Array.isArray(e)?e:[e]:[]}function vc(e,n){return Array.isArray(e)?e.includes(n):e===n}function aD(e,n){let t=Nf(n);return Nf(e).forEach(o=>{vc(t,o)||t.push(o)}),t}function cD(e,n){return Nf(n).filter(t=>!vc(e,t))}var yc=class{get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators=[];_rawAsyncValidators=[];_setValidators(n){this._rawValidators=n||[],this._composedValidatorFn=bD(this._rawValidators)}_setAsyncValidators(n){this._rawAsyncValidators=n||[],this._composedAsyncValidatorFn=TD(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_onDestroyCallbacks=[];_registerOnDestroy(n){this._onDestroyCallbacks.push(n)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(n=>n()),this._onDestroyCallbacks=[]}reset(n=void 0){this.control&&this.control.reset(n)}hasError(n,t){return this.control?this.control.hasError(n,t):!1}getError(n,t){return this.control?this.control.getError(n,t):null}},qn=class extends yc{name;get formDirective(){return null}get path(){return null}},Pt=class extends yc{_parent=null;name=null;valueAccessor=null},Dc=class{_cd;constructor(n){this._cd=n}get isTouched(){return this._cd?.control?._touched?.(),!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return this._cd?.control?._pristine?.(),!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return this._cd?.control?._status?.(),!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return this._cd?._submitted?.(),!!this._cd?.submitted}},WM={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},Lz=A(g({},WM),{"[class.ng-submitted]":"isSubmitted"}),jz=(()=>{class e extends Dc{constructor(t){super(t)}static \u0275fac=function(r){return new(r||e)(v(Pt,2))};static \u0275dir=j({type:e,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(r,o){r&2&&Uo("ng-untouched",o.isUntouched)("ng-touched",o.isTouched)("ng-pristine",o.isPristine)("ng-dirty",o.isDirty)("ng-valid",o.isValid)("ng-invalid",o.isInvalid)("ng-pending",o.isPending)},standalone:!1,features:[He]})}return e})(),Vz=(()=>{class e extends Dc{constructor(t){super(t)}static \u0275fac=function(r){return new(r||e)(v(qn,10))};static \u0275dir=j({type:e,selectors:[["","formGroupName",""],["","formArrayName",""],["","ngModelGroup",""],["","formGroup",""],["form",3,"ngNoForm",""],["","ngForm",""]],hostVars:16,hostBindings:function(r,o){r&2&&Uo("ng-untouched",o.isUntouched)("ng-touched",o.isTouched)("ng-pristine",o.isPristine)("ng-dirty",o.isDirty)("ng-valid",o.isValid)("ng-invalid",o.isInvalid)("ng-pending",o.isPending)("ng-submitted",o.isSubmitted)},standalone:!1,features:[He]})}return e})();var Di="VALID",gc="INVALID",Qr="PENDING",Ei="DISABLED",ln=class{},Ec=class extends ln{value;source;constructor(n,t){super(),this.value=n,this.source=t}},Ci=class extends ln{pristine;source;constructor(n,t){super(),this.pristine=n,this.source=t}},wi=class extends ln{touched;source;constructor(n,t){super(),this.touched=n,this.source=t}},Kr=class extends ln{status;source;constructor(n,t){super(),this.status=n,this.source=t}},xf=class extends ln{source;constructor(n){super(),this.source=n}},Of=class extends ln{source;constructor(n){super(),this.source=n}};function Uf(e){return(bc(e)?e.validators:e)||null}function qM(e){return Array.isArray(e)?bD(e):e||null}function Hf(e,n){return(bc(n)?n.asyncValidators:e)||null}function ZM(e){return Array.isArray(e)?TD(e):e||null}function bc(e){return e!=null&&!Array.isArray(e)&&typeof e=="object"}function AD(e,n,t){let r=e.controls;if(!(n?Object.keys(r):r).length)throw new C(1e3,"");if(!r[t])throw new C(1001,"")}function RD(e,n,t){e._forEachChild((r,o)=>{if(t[o]===void 0)throw new C(1002,"")})}var Jr=class{_pendingDirty=!1;_hasOwnPendingAsyncValidator=null;_pendingTouched=!1;_onCollectionChange=()=>{};_updateOn;_parent=null;_asyncValidationSubscription;_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators;_rawAsyncValidators;value;constructor(n,t){this._assignValidators(n),this._assignAsyncValidators(t)}get validator(){return this._composedValidatorFn}set validator(n){this._rawValidators=this._composedValidatorFn=n}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(n){this._rawAsyncValidators=this._composedAsyncValidatorFn=n}get parent(){return this._parent}get status(){return $e(this.statusReactive)}set status(n){$e(()=>this.statusReactive.set(n))}_status=kr(()=>this.statusReactive());statusReactive=Mt(void 0);get valid(){return this.status===Di}get invalid(){return this.status===gc}get pending(){return this.status==Qr}get disabled(){return this.status===Ei}get enabled(){return this.status!==Ei}errors;get pristine(){return $e(this.pristineReactive)}set pristine(n){$e(()=>this.pristineReactive.set(n))}_pristine=kr(()=>this.pristineReactive());pristineReactive=Mt(!0);get dirty(){return!this.pristine}get touched(){return $e(this.touchedReactive)}set touched(n){$e(()=>this.touchedReactive.set(n))}_touched=kr(()=>this.touchedReactive());touchedReactive=Mt(!1);get untouched(){return!this.touched}_events=new H;events=this._events.asObservable();valueChanges;statusChanges;get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(n){this._assignValidators(n)}setAsyncValidators(n){this._assignAsyncValidators(n)}addValidators(n){this.setValidators(aD(n,this._rawValidators))}addAsyncValidators(n){this.setAsyncValidators(aD(n,this._rawAsyncValidators))}removeValidators(n){this.setValidators(cD(n,this._rawValidators))}removeAsyncValidators(n){this.setAsyncValidators(cD(n,this._rawAsyncValidators))}hasValidator(n){return vc(this._rawValidators,n)}hasAsyncValidator(n){return vc(this._rawAsyncValidators,n)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(n={}){let t=this.touched===!1;this.touched=!0;let r=n.sourceControl??this;this._parent&&!n.onlySelf&&this._parent.markAsTouched(A(g({},n),{sourceControl:r})),t&&n.emitEvent!==!1&&this._events.next(new wi(!0,r))}markAllAsDirty(n={}){this.markAsDirty({onlySelf:!0,emitEvent:n.emitEvent,sourceControl:this}),this._forEachChild(t=>t.markAllAsDirty(n))}markAllAsTouched(n={}){this.markAsTouched({onlySelf:!0,emitEvent:n.emitEvent,sourceControl:this}),this._forEachChild(t=>t.markAllAsTouched(n))}markAsUntouched(n={}){let t=this.touched===!0;this.touched=!1,this._pendingTouched=!1;let r=n.sourceControl??this;this._forEachChild(o=>{o.markAsUntouched({onlySelf:!0,emitEvent:n.emitEvent,sourceControl:r})}),this._parent&&!n.onlySelf&&this._parent._updateTouched(n,r),t&&n.emitEvent!==!1&&this._events.next(new wi(!1,r))}markAsDirty(n={}){let t=this.pristine===!0;this.pristine=!1;let r=n.sourceControl??this;this._parent&&!n.onlySelf&&this._parent.markAsDirty(A(g({},n),{sourceControl:r})),t&&n.emitEvent!==!1&&this._events.next(new Ci(!1,r))}markAsPristine(n={}){let t=this.pristine===!1;this.pristine=!0,this._pendingDirty=!1;let r=n.sourceControl??this;this._forEachChild(o=>{o.markAsPristine({onlySelf:!0,emitEvent:n.emitEvent})}),this._parent&&!n.onlySelf&&this._parent._updatePristine(n,r),t&&n.emitEvent!==!1&&this._events.next(new Ci(!0,r))}markAsPending(n={}){this.status=Qr;let t=n.sourceControl??this;n.emitEvent!==!1&&(this._events.next(new Kr(this.status,t)),this.statusChanges.emit(this.status)),this._parent&&!n.onlySelf&&this._parent.markAsPending(A(g({},n),{sourceControl:t}))}disable(n={}){let t=this._parentMarkedDirty(n.onlySelf);this.status=Ei,this.errors=null,this._forEachChild(o=>{o.disable(A(g({},n),{onlySelf:!0}))}),this._updateValue();let r=n.sourceControl??this;n.emitEvent!==!1&&(this._events.next(new Ec(this.value,r)),this._events.next(new Kr(this.status,r)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(A(g({},n),{skipPristineCheck:t}),this),this._onDisabledChange.forEach(o=>o(!0))}enable(n={}){let t=this._parentMarkedDirty(n.onlySelf);this.status=Di,this._forEachChild(r=>{r.enable(A(g({},n),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:n.emitEvent}),this._updateAncestors(A(g({},n),{skipPristineCheck:t}),this),this._onDisabledChange.forEach(r=>r(!1))}_updateAncestors(n,t){this._parent&&!n.onlySelf&&(this._parent.updateValueAndValidity(n),n.skipPristineCheck||this._parent._updatePristine({},t),this._parent._updateTouched({},t))}setParent(n){this._parent=n}getRawValue(){return this.value}updateValueAndValidity(n={}){if(this._setInitialStatus(),this._updateValue(),this.enabled){let r=this._cancelExistingSubscription();this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===Di||this.status===Qr)&&this._runAsyncValidator(r,n.emitEvent)}let t=n.sourceControl??this;n.emitEvent!==!1&&(this._events.next(new Ec(this.value,t)),this._events.next(new Kr(this.status,t)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!n.onlySelf&&this._parent.updateValueAndValidity(A(g({},n),{sourceControl:t}))}_updateTreeValidity(n={emitEvent:!0}){this._forEachChild(t=>t._updateTreeValidity(n)),this.updateValueAndValidity({onlySelf:!0,emitEvent:n.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?Ei:Di}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(n,t){if(this.asyncValidator){this.status=Qr,this._hasOwnPendingAsyncValidator={emitEvent:t!==!1,shouldHaveEmitted:n!==!1};let r=DD(this.asyncValidator(this));this._asyncValidationSubscription=r.subscribe(o=>{this._hasOwnPendingAsyncValidator=null,this.setErrors(o,{emitEvent:t,shouldHaveEmitted:n})})}}_cancelExistingSubscription(){if(this._asyncValidationSubscription){this._asyncValidationSubscription.unsubscribe();let n=(this._hasOwnPendingAsyncValidator?.emitEvent||this._hasOwnPendingAsyncValidator?.shouldHaveEmitted)??!1;return this._hasOwnPendingAsyncValidator=null,n}return!1}setErrors(n,t={}){this.errors=n,this._updateControlsErrors(t.emitEvent!==!1,this,t.shouldHaveEmitted)}get(n){let t=n;return t==null||(Array.isArray(t)||(t=t.split(".")),t.length===0)?null:t.reduce((r,o)=>r&&r._find(o),this)}getError(n,t){let r=t?this.get(t):this;return r&&r.errors?r.errors[n]:null}hasError(n,t){return!!this.getError(n,t)}get root(){let n=this;for(;n._parent;)n=n._parent;return n}_updateControlsErrors(n,t,r){this.status=this._calculateStatus(),n&&this.statusChanges.emit(this.status),(n||r)&&this._events.next(new Kr(this.status,t)),this._parent&&this._parent._updateControlsErrors(n,t,r)}_initObservables(){this.valueChanges=new $,this.statusChanges=new $}_calculateStatus(){return this._allControlsDisabled()?Ei:this.errors?gc:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(Qr)?Qr:this._anyControlsHaveStatus(gc)?gc:Di}_anyControlsHaveStatus(n){return this._anyControls(t=>t.status===n)}_anyControlsDirty(){return this._anyControls(n=>n.dirty)}_anyControlsTouched(){return this._anyControls(n=>n.touched)}_updatePristine(n,t){let r=!this._anyControlsDirty(),o=this.pristine!==r;this.pristine=r,this._parent&&!n.onlySelf&&this._parent._updatePristine(n,t),o&&this._events.next(new Ci(this.pristine,t))}_updateTouched(n={},t){this.touched=this._anyControlsTouched(),this._events.next(new wi(this.touched,t)),this._parent&&!n.onlySelf&&this._parent._updateTouched(n,t)}_onDisabledChange=[];_registerOnCollectionChange(n){this._onCollectionChange=n}_setUpdateStrategy(n){bc(n)&&n.updateOn!=null&&(this._updateOn=n.updateOn)}_parentMarkedDirty(n){let t=this._parent&&this._parent.dirty;return!n&&!!t&&!this._parent._anyControlsDirty()}_find(n){return null}_assignValidators(n){this._rawValidators=Array.isArray(n)?n.slice():n,this._composedValidatorFn=qM(this._rawValidators)}_assignAsyncValidators(n){this._rawAsyncValidators=Array.isArray(n)?n.slice():n,this._composedAsyncValidatorFn=ZM(this._rawAsyncValidators)}},Cc=class extends Jr{constructor(n,t,r){super(Uf(t),Hf(r,t)),this.controls=n,this._initObservables(),this._setUpdateStrategy(t),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}controls;registerControl(n,t){return this.controls[n]?this.controls[n]:(this.controls[n]=t,t.setParent(this),t._registerOnCollectionChange(this._onCollectionChange),t)}addControl(n,t,r={}){this.registerControl(n,t),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}removeControl(n,t={}){this.controls[n]&&this.controls[n]._registerOnCollectionChange(()=>{}),delete this.controls[n],this.updateValueAndValidity({emitEvent:t.emitEvent}),this._onCollectionChange()}setControl(n,t,r={}){this.controls[n]&&this.controls[n]._registerOnCollectionChange(()=>{}),delete this.controls[n],t&&this.registerControl(n,t),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}contains(n){return this.controls.hasOwnProperty(n)&&this.controls[n].enabled}setValue(n,t={}){RD(this,!0,n),Object.keys(n).forEach(r=>{AD(this,!0,r),this.controls[r].setValue(n[r],{onlySelf:!0,emitEvent:t.emitEvent})}),this.updateValueAndValidity(t)}patchValue(n,t={}){n!=null&&(Object.keys(n).forEach(r=>{let o=this.controls[r];o&&o.patchValue(n[r],{onlySelf:!0,emitEvent:t.emitEvent})}),this.updateValueAndValidity(t))}reset(n={},t={}){this._forEachChild((r,o)=>{r.reset(n?n[o]:null,{onlySelf:!0,emitEvent:t.emitEvent})}),this._updatePristine(t,this),this._updateTouched(t,this),this.updateValueAndValidity(t)}getRawValue(){return this._reduceChildren({},(n,t,r)=>(n[r]=t.getRawValue(),n))}_syncPendingControls(){let n=this._reduceChildren(!1,(t,r)=>r._syncPendingControls()?!0:t);return n&&this.updateValueAndValidity({onlySelf:!0}),n}_forEachChild(n){Object.keys(this.controls).forEach(t=>{let r=this.controls[t];r&&n(r,t)})}_setUpControls(){this._forEachChild(n=>{n.setParent(this),n._registerOnCollectionChange(this._onCollectionChange)})}_updateValue(){this.value=this._reduceValue()}_anyControls(n){for(let[t,r]of Object.entries(this.controls))if(this.contains(t)&&n(r))return!0;return!1}_reduceValue(){let n={};return this._reduceChildren(n,(t,r,o)=>((r.enabled||this.disabled)&&(t[o]=r.value),t))}_reduceChildren(n,t){let r=n;return this._forEachChild((o,i)=>{r=t(r,o,i)}),r}_allControlsDisabled(){for(let n of Object.keys(this.controls))if(this.controls[n].enabled)return!1;return Object.keys(this.controls).length>0||this.disabled}_find(n){return this.controls.hasOwnProperty(n)?this.controls[n]:null}};var kf=class extends Cc{};var _c=new D("",{providedIn:"root",factory:()=>Tc}),Tc="always";function ND(e,n){return[...n.path,e]}function Pf(e,n,t=Tc){$f(e,n),n.valueAccessor.writeValue(e.value),(e.disabled||t==="always")&&n.valueAccessor.setDisabledState?.(e.disabled),QM(e,n),JM(e,n),KM(e,n),YM(e,n)}function uD(e,n,t=!0){let r=()=>{};n.valueAccessor&&(n.valueAccessor.registerOnChange(r),n.valueAccessor.registerOnTouched(r)),Ic(e,n),e&&(n._invokeOnDestroyCallbacks(),e._registerOnCollectionChange(()=>{}))}function wc(e,n){e.forEach(t=>{t.registerOnValidatorChange&&t.registerOnValidatorChange(n)})}function YM(e,n){if(n.valueAccessor.setDisabledState){let t=r=>{n.valueAccessor.setDisabledState(r)};e.registerOnDisabledChange(t),n._registerOnDestroy(()=>{e._unregisterOnDisabledChange(t)})}}function $f(e,n){let t=SD(e);n.validator!==null?e.setValidators(sD(t,n.validator)):typeof t=="function"&&e.setValidators([t]);let r=MD(e);n.asyncValidator!==null?e.setAsyncValidators(sD(r,n.asyncValidator)):typeof r=="function"&&e.setAsyncValidators([r]);let o=()=>e.updateValueAndValidity();wc(n._rawValidators,o),wc(n._rawAsyncValidators,o)}function Ic(e,n){let t=!1;if(e!==null){if(n.validator!==null){let o=SD(e);if(Array.isArray(o)&&o.length>0){let i=o.filter(s=>s!==n.validator);i.length!==o.length&&(t=!0,e.setValidators(i))}}if(n.asyncValidator!==null){let o=MD(e);if(Array.isArray(o)&&o.length>0){let i=o.filter(s=>s!==n.asyncValidator);i.length!==o.length&&(t=!0,e.setAsyncValidators(i))}}}let r=()=>{};return wc(n._rawValidators,r),wc(n._rawAsyncValidators,r),t}function QM(e,n){n.valueAccessor.registerOnChange(t=>{e._pendingValue=t,e._pendingChange=!0,e._pendingDirty=!0,e.updateOn==="change"&&xD(e,n)})}function KM(e,n){n.valueAccessor.registerOnTouched(()=>{e._pendingTouched=!0,e.updateOn==="blur"&&e._pendingChange&&xD(e,n),e.updateOn!=="submit"&&e.markAsTouched()})}function xD(e,n){e._pendingDirty&&e.markAsDirty(),e.setValue(e._pendingValue,{emitModelToViewChange:!1}),n.viewToModelUpdate(e._pendingValue),e._pendingChange=!1}function JM(e,n){let t=(r,o)=>{n.valueAccessor.writeValue(r),o&&n.viewToModelUpdate(r)};e.registerOnChange(t),n._registerOnDestroy(()=>{e._unregisterOnChange(t)})}function XM(e,n){e==null,$f(e,n)}function e0(e,n){return Ic(e,n)}function OD(e,n){if(!e.hasOwnProperty("model"))return!1;let t=e.model;return t.isFirstChange()?!0:!Object.is(n,t.currentValue)}function t0(e){return Object.getPrototypeOf(e.constructor)===kM}function n0(e,n){e._syncPendingControls(),n.forEach(t=>{let r=t.control;r.updateOn==="submit"&&r._pendingChange&&(t.viewToModelUpdate(r._pendingValue),r._pendingChange=!1)})}function kD(e,n){if(!n)return null;Array.isArray(n);let t,r,o;return n.forEach(i=>{i.constructor===gD?t=i:t0(i)?r=i:o=i}),o||r||t||null}function r0(e,n){let t=e.indexOf(n);t>-1&&e.splice(t,1)}function lD(e,n){let t=e.indexOf(n);t>-1&&e.splice(t,1)}function dD(e){return typeof e=="object"&&e!==null&&Object.keys(e).length===2&&"value"in e&&"disabled"in e}var Ii=class extends Jr{defaultValue=null;_onChange=[];_pendingValue;_pendingChange=!1;constructor(n=null,t,r){super(Uf(t),Hf(r,t)),this._applyFormState(n),this._setUpdateStrategy(t),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),bc(t)&&(t.nonNullable||t.initialValueIsDefault)&&(dD(n)?this.defaultValue=n.value:this.defaultValue=n)}setValue(n,t={}){this.value=this._pendingValue=n,this._onChange.length&&t.emitModelToViewChange!==!1&&this._onChange.forEach(r=>r(this.value,t.emitViewToModelChange!==!1)),this.updateValueAndValidity(t)}patchValue(n,t={}){this.setValue(n,t)}reset(n=this.defaultValue,t={}){this._applyFormState(n),this.markAsPristine(t),this.markAsUntouched(t),this.setValue(this.value,t),this._pendingChange=!1}_updateValue(){}_anyControls(n){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(n){this._onChange.push(n)}_unregisterOnChange(n){lD(this._onChange,n)}registerOnDisabledChange(n){this._onDisabledChange.push(n)}_unregisterOnDisabledChange(n){lD(this._onDisabledChange,n)}_forEachChild(n){}_syncPendingControls(){return this.updateOn==="submit"&&(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),this._pendingChange)?(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),!0):!1}_applyFormState(n){dD(n)?(this.value=this._pendingValue=n.value,n.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=n}};var o0=e=>e instanceof Ii;var i0={provide:Pt,useExisting:Ye(()=>s0)},fD=Promise.resolve(),s0=(()=>{class e extends Pt{_changeDetectorRef;callSetDisabledState;control=new Ii;static ngAcceptInputType_isDisabled;_registered=!1;viewModel;name="";isDisabled;model;options;update=new $;constructor(t,r,o,i,s,a){super(),this._changeDetectorRef=s,this.callSetDisabledState=a,this._parent=t,this._setValidators(r),this._setAsyncValidators(o),this.valueAccessor=kD(this,i)}ngOnChanges(t){if(this._checkForErrors(),!this._registered||"name"in t){if(this._registered&&(this._checkName(),this.formDirective)){let r=t.name.previousValue;this.formDirective.removeControl({name:r,path:this._getPath(r)})}this._setUpControl()}"isDisabled"in t&&this._updateDisabled(t),OD(t,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(t){this.viewModel=t,this.update.emit(t)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!!(this.options&&this.options.standalone)}_setUpStandalone(){Pf(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._checkName()}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),!this._isStandalone()&&this.name}_updateValue(t){fD.then(()=>{this.control.setValue(t,{emitViewToModelChange:!1}),this._changeDetectorRef?.markForCheck()})}_updateDisabled(t){let r=t.isDisabled.currentValue,o=r!==0&&Bn(r);fD.then(()=>{o&&!this.control.disabled?this.control.disable():!o&&this.control.disabled&&this.control.enable(),this._changeDetectorRef?.markForCheck()})}_getPath(t){return this._parent?ND(t,this._parent):[t]}static \u0275fac=function(r){return new(r||e)(v(qn,9),v(bi,10),v(Bf,10),v(Lf,10),v(ze,8),v(_c,8))};static \u0275dir=j({type:e,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"],options:[0,"ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],standalone:!1,features:[rn([i0]),He,De]})}return e})();var Uz=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275dir=j({type:e,selectors:[["form",3,"ngNoForm","",3,"ngNativeValidate",""]],hostAttrs:["novalidate",""],standalone:!1})}return e})();var PD=new D("");var a0={provide:qn,useExisting:Ye(()=>c0)},c0=(()=>{class e extends qn{callSetDisabledState;get submitted(){return $e(this._submittedReactive)}set submitted(t){this._submittedReactive.set(t)}_submitted=kr(()=>this._submittedReactive());_submittedReactive=Mt(!1);_oldForm;_onCollectionChange=()=>this._updateDomValue();directives=[];form=null;ngSubmit=new $;constructor(t,r,o){super(),this.callSetDisabledState=o,this._setValidators(t),this._setAsyncValidators(r)}ngOnChanges(t){t.hasOwnProperty("form")&&(this._updateValidators(),this._updateDomValue(),this._updateRegistrations(),this._oldForm=this.form)}ngOnDestroy(){this.form&&(Ic(this.form,this),this.form._onCollectionChange===this._onCollectionChange&&this.form._registerOnCollectionChange(()=>{}))}get formDirective(){return this}get control(){return this.form}get path(){return[]}addControl(t){let r=this.form.get(t.path);return Pf(r,t,this.callSetDisabledState),r.updateValueAndValidity({emitEvent:!1}),this.directives.push(t),r}getControl(t){return this.form.get(t.path)}removeControl(t){uD(t.control||null,t,!1),r0(this.directives,t)}addFormGroup(t){this._setUpFormContainer(t)}removeFormGroup(t){this._cleanUpFormContainer(t)}getFormGroup(t){return this.form.get(t.path)}addFormArray(t){this._setUpFormContainer(t)}removeFormArray(t){this._cleanUpFormContainer(t)}getFormArray(t){return this.form.get(t.path)}updateModel(t,r){this.form.get(t.path).setValue(r)}onSubmit(t){return this._submittedReactive.set(!0),n0(this.form,this.directives),this.ngSubmit.emit(t),this.form._events.next(new xf(this.control)),t?.target?.method==="dialog"}onReset(){this.resetForm()}resetForm(t=void 0,r={}){this.form.reset(t,r),this._submittedReactive.set(!1),r?.emitEvent!==!1&&this.form._events.next(new Of(this.form))}_updateDomValue(){this.directives.forEach(t=>{let r=t.control,o=this.form.get(t.path);r!==o&&(uD(r||null,t),o0(o)&&(Pf(o,t,this.callSetDisabledState),t.control=o))}),this.form._updateTreeValidity({emitEvent:!1})}_setUpFormContainer(t){let r=this.form.get(t.path);XM(r,t),r.updateValueAndValidity({emitEvent:!1})}_cleanUpFormContainer(t){if(this.form){let r=this.form.get(t.path);r&&e0(r,t)&&r.updateValueAndValidity({emitEvent:!1})}}_updateRegistrations(){this.form._registerOnCollectionChange(this._onCollectionChange),this._oldForm&&this._oldForm._registerOnCollectionChange(()=>{})}_updateValidators(){$f(this.form,this),this._oldForm&&Ic(this._oldForm,this)}static \u0275fac=function(r){return new(r||e)(v(bi,10),v(Bf,10),v(_c,8))};static \u0275dir=j({type:e,selectors:[["","formGroup",""]],hostBindings:function(r,o){r&1&&Pe("submit",function(s){return o.onSubmit(s)})("reset",function(){return o.onReset()})},inputs:{form:[0,"formGroup","form"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],standalone:!1,features:[rn([a0]),He,De]})}return e})();var u0={provide:Pt,useExisting:Ye(()=>l0)},l0=(()=>{class e extends Pt{_ngModelWarningConfig;_added=!1;viewModel;control;name=null;set isDisabled(t){}model;update=new $;static _ngModelWarningSentOnce=!1;_ngModelWarningSent=!1;constructor(t,r,o,i,s){super(),this._ngModelWarningConfig=s,this._parent=t,this._setValidators(r),this._setAsyncValidators(o),this.valueAccessor=kD(this,i)}ngOnChanges(t){this._added||this._setUpControl(),OD(t,this.viewModel)&&(this.viewModel=this.model,this.formDirective.updateModel(this,this.model))}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}viewToModelUpdate(t){this.viewModel=t,this.update.emit(t)}get path(){return ND(this.name==null?this.name:this.name.toString(),this._parent)}get formDirective(){return this._parent?this._parent.formDirective:null}_setUpControl(){this.control=this.formDirective.addControl(this),this._added=!0}static \u0275fac=function(r){return new(r||e)(v(qn,13),v(bi,10),v(Bf,10),v(Lf,10),v(PD,8))};static \u0275dir=j({type:e,selectors:[["","formControlName",""]],inputs:{name:[0,"formControlName","name"],isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"]},outputs:{update:"ngModelChange"},standalone:!1,features:[rn([u0]),He,De]})}return e})();function FD(e){return typeof e=="number"?e:parseFloat(e)}var LD=(()=>{class e{_validator=mc;_onChange;_enabled;ngOnChanges(t){if(this.inputName in t){let r=this.normalizeInput(t[this.inputName].currentValue);this._enabled=this.enabled(r),this._validator=this._enabled?this.createValidator(r):mc,this._onChange&&this._onChange()}}validate(t){return this._validator(t)}registerOnValidatorChange(t){this._onChange=t}enabled(t){return t!=null}static \u0275fac=function(r){return new(r||e)};static \u0275dir=j({type:e,features:[De]})}return e})(),d0={provide:bi,useExisting:Ye(()=>f0),multi:!0},f0=(()=>{class e extends LD{max;inputName="max";normalizeInput=t=>FD(t);createValidator=t=>vD(t);static \u0275fac=(()=>{let t;return function(o){return(t||(t=Xt(e)))(o||e)}})();static \u0275dir=j({type:e,selectors:[["input","type","number","max","","formControlName",""],["input","type","number","max","","formControl",""],["input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(r,o){r&2&&Vn("max",o._enabled?o.max:null)},inputs:{max:"max"},standalone:!1,features:[rn([d0]),He]})}return e})(),h0={provide:bi,useExisting:Ye(()=>p0),multi:!0},p0=(()=>{class e extends LD{min;inputName="min";normalizeInput=t=>FD(t);createValidator=t=>mD(t);static \u0275fac=(()=>{let t;return function(o){return(t||(t=Xt(e)))(o||e)}})();static \u0275dir=j({type:e,selectors:[["input","type","number","min","","formControlName",""],["input","type","number","min","","formControl",""],["input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(r,o){r&2&&Vn("min",o._enabled?o.min:null)},inputs:{min:"min"},standalone:!1,features:[rn([h0]),He]})}return e})();var jD=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=ke({type:e});static \u0275inj=_e({})}return e})(),Ff=class extends Jr{constructor(n,t,r){super(Uf(t),Hf(r,t)),this.controls=n,this._initObservables(),this._setUpdateStrategy(t),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}controls;at(n){return this.controls[this._adjustIndex(n)]}push(n,t={}){this.controls.push(n),this._registerControl(n),this.updateValueAndValidity({emitEvent:t.emitEvent}),this._onCollectionChange()}insert(n,t,r={}){this.controls.splice(n,0,t),this._registerControl(t),this.updateValueAndValidity({emitEvent:r.emitEvent})}removeAt(n,t={}){let r=this._adjustIndex(n);r<0&&(r=0),this.controls[r]&&this.controls[r]._registerOnCollectionChange(()=>{}),this.controls.splice(r,1),this.updateValueAndValidity({emitEvent:t.emitEvent})}setControl(n,t,r={}){let o=this._adjustIndex(n);o<0&&(o=0),this.controls[o]&&this.controls[o]._registerOnCollectionChange(()=>{}),this.controls.splice(o,1),t&&(this.controls.splice(o,0,t),this._registerControl(t)),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}get length(){return this.controls.length}setValue(n,t={}){RD(this,!1,n),n.forEach((r,o)=>{AD(this,!1,o),this.at(o).setValue(r,{onlySelf:!0,emitEvent:t.emitEvent})}),this.updateValueAndValidity(t)}patchValue(n,t={}){n!=null&&(n.forEach((r,o)=>{this.at(o)&&this.at(o).patchValue(r,{onlySelf:!0,emitEvent:t.emitEvent})}),this.updateValueAndValidity(t))}reset(n=[],t={}){this._forEachChild((r,o)=>{r.reset(n[o],{onlySelf:!0,emitEvent:t.emitEvent})}),this._updatePristine(t,this),this._updateTouched(t,this),this.updateValueAndValidity(t)}getRawValue(){return this.controls.map(n=>n.getRawValue())}clear(n={}){this.controls.length<1||(this._forEachChild(t=>t._registerOnCollectionChange(()=>{})),this.controls.splice(0),this.updateValueAndValidity({emitEvent:n.emitEvent}))}_adjustIndex(n){return n<0?n+this.length:n}_syncPendingControls(){let n=this.controls.reduce((t,r)=>r._syncPendingControls()?!0:t,!1);return n&&this.updateValueAndValidity({onlySelf:!0}),n}_forEachChild(n){this.controls.forEach((t,r)=>{n(t,r)})}_updateValue(){this.value=this.controls.filter(n=>n.enabled||this.disabled).map(n=>n.value)}_anyControls(n){return this.controls.some(t=>t.enabled&&n(t))}_setUpControls(){this._forEachChild(n=>this._registerControl(n))}_allControlsDisabled(){for(let n of this.controls)if(n.enabled)return!1;return this.controls.length>0||this.disabled}_registerControl(n){n.setParent(this),n._registerOnCollectionChange(this._onCollectionChange)}_find(n){return this.at(n)??null}};function hD(e){return!!e&&(e.asyncValidators!==void 0||e.validators!==void 0||e.updateOn!==void 0)}var Hz=(()=>{class e{useNonNullable=!1;get nonNullable(){let t=new e;return t.useNonNullable=!0,t}group(t,r=null){let o=this._reduceControls(t),i={};return hD(r)?i=r:r!==null&&(i.validators=r.validator,i.asyncValidators=r.asyncValidator),new Cc(o,i)}record(t,r=null){let o=this._reduceControls(t);return new kf(o,r)}control(t,r,o){let i={};return this.useNonNullable?(hD(r)?i=r:(i.validators=r,i.asyncValidators=o),new Ii(t,A(g({},i),{nonNullable:!0}))):new Ii(t,r,o)}array(t,r,o){let i=t.map(s=>this._createControl(s));return new Ff(i,r,o)}_reduceControls(t){let r={};return Object.keys(t).forEach(o=>{r[o]=this._createControl(t[o])}),r}_createControl(t){if(t instanceof Ii)return t;if(t instanceof Jr)return t;if(Array.isArray(t)){let r=t[0],o=t.length>1?t[1]:null,i=t.length>2?t[2]:null;return this.control(r,o,i)}else return this.control(t)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var $z=(()=>{class e{static withConfig(t){return{ngModule:e,providers:[{provide:_c,useValue:t.callSetDisabledState??Tc}]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=ke({type:e});static \u0275inj=_e({imports:[jD]})}return e})(),zz=(()=>{class e{static withConfig(t){return{ngModule:e,providers:[{provide:PD,useValue:t.warnOnNgModelWithFormControl??"always"},{provide:_c,useValue:t.callSetDisabledState??Tc}]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=ke({type:e});static \u0275inj=_e({imports:[jD]})}return e})();var qz=(e,n,t,r,o)=>m0(e[1],n[1],t[1],r[1],o).map(i=>g0(e[0],n[0],t[0],r[0],i)),g0=(e,n,t,r,o)=>{let i=3*n*Math.pow(o-1,2),s=-3*t*o+3*t+r*o,a=e*Math.pow(o-1,3);return o*(i+o*s)-a},m0=(e,n,t,r,o)=>(e-=o,n-=o,t-=o,r-=o,y0(r-3*t+3*n-e,3*t-6*n+3*e,3*n-3*e,e).filter(s=>s>=0&&s<=1)),v0=(e,n,t)=>{let r=n*n-4*e*t;return r<0?[]:[(-n+Math.sqrt(r))/(2*e),(-n-Math.sqrt(r))/(2*e)]},y0=(e,n,t,r)=>{if(e===0)return v0(n,t,r);n/=e,t/=e,r/=e;let o=(3*t-n*n)/3,i=(2*n*n*n-9*n*t+27*r)/27;if(o===0)return[Math.pow(-i,.3333333333333333)];if(i===0)return[Math.sqrt(-o),-Math.sqrt(-o)];let s=Math.pow(i/2,2)+Math.pow(o/3,3);if(s===0)return[Math.pow(i/2,.5)-n/3];if(s>0)return[Math.pow(-(i/2)+Math.sqrt(s),.3333333333333333)-Math.pow(i/2+Math.sqrt(s),.3333333333333333)-n/3];let a=Math.sqrt(Math.pow(-(o/3),3)),c=Math.acos(-(i/(2*Math.sqrt(Math.pow(-(o/3),3))))),u=2*Math.pow(a,1/3);return[u*Math.cos(c/3)-n/3,u*Math.cos((c+2*Math.PI)/3)-n/3,u*Math.cos((c+4*Math.PI)/3)-n/3]};var Sc=e=>BD(e),eo=(e,n)=>(typeof e=="string"&&(n=e,e=void 0),Sc(e).includes(n)),BD=(e=window)=>{if(typeof e>"u")return[];e.Ionic=e.Ionic||{};let n=e.Ionic.platforms;return n==null&&(n=e.Ionic.platforms=D0(e),n.forEach(t=>e.document.documentElement.classList.add(`plt-${t}`))),n},D0=e=>{let n=Re.get("platform");return Object.keys(VD).filter(t=>{let r=n?.[t];return typeof r=="function"?r(e):VD[t](e)})},E0=e=>Mc(e)&&!HD(e),zf=e=>!!(Zn(e,/iPad/i)||Zn(e,/Macintosh/i)&&Mc(e)),C0=e=>Zn(e,/iPhone/i),w0=e=>Zn(e,/iPhone|iPod/i)||zf(e),UD=e=>Zn(e,/android|sink/i),I0=e=>UD(e)&&!Zn(e,/mobile/i),b0=e=>{let n=e.innerWidth,t=e.innerHeight,r=Math.min(n,t),o=Math.max(n,t);return r>390&&r<520&&o>620&&o<800},_0=e=>{let n=e.innerWidth,t=e.innerHeight,r=Math.min(n,t),o=Math.max(n,t);return zf(e)||I0(e)||r>460&&r<820&&o>780&&o<1400},Mc=e=>A0(e,"(any-pointer:coarse)"),T0=e=>!Mc(e),HD=e=>$D(e)||zD(e),$D=e=>!!(e.cordova||e.phonegap||e.PhoneGap),zD=e=>{let n=e.Capacitor;return!!(n?.isNative||n?.isNativePlatform&&n.isNativePlatform())},S0=e=>Zn(e,/electron/i),M0=e=>{var n;return!!(!((n=e.matchMedia)===null||n===void 0)&&n.call(e,"(display-mode: standalone)").matches||e.navigator.standalone)},Zn=(e,n)=>n.test(e.navigator.userAgent),A0=(e,n)=>{var t;return(t=e.matchMedia)===null||t===void 0?void 0:t.call(e,n).matches},VD={ipad:zf,iphone:C0,ios:w0,android:UD,phablet:b0,tablet:_0,cordova:$D,capacitor:zD,electron:S0,pwa:M0,mobile:Mc,mobileweb:E0,desktop:T0,hybrid:HD},Xr,Gf=e=>e&&ih(e)||Xr,R0=(e={})=>{if(typeof window>"u")return;let n=window.document,t=window,r=t.Ionic=t.Ionic||{},o=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},sh(t)),{persistConfig:!1}),r.config),ch(t)),e);Re.reset(o),Re.getBoolean("persistConfig")&&ah(t,o),BD(t),r.config=Re,r.mode=Xr=Re.get("mode",n.documentElement.getAttribute("mode")||(eo(t,"ios")?"ios":"md")),Re.set("mode",Xr),n.documentElement.setAttribute("mode",Xr),n.documentElement.classList.add(Xr),Re.getBoolean("_testing")&&Re.set("animated",!1);let i=a=>{var c;return(c=a.tagName)===null||c===void 0?void 0:c.startsWith("ION-")},s=a=>["ios","md"].includes(a);oh(a=>{for(;a;){let c=a.mode||a.getAttribute("mode");if(c){if(s(c))return c;i(a)&&Ri('Invalid ionic mode: "'+c+'", expected: "ios" or "md"')}a=a.parentElement}return Xr})};var Jz=(e,n)=>n.closest(e)!==null,Xz=(e,n)=>typeof e=="string"&&e.length>0?Object.assign({"ion-color":!0,[`ion-color-${e}`]:!0},n):n,N0=e=>e!==void 0?(Array.isArray(e)?e:e.split(" ")).filter(t=>t!=null).map(t=>t.trim()).filter(t=>t!==""):[],eG=e=>{let n={};return N0(e).forEach(t=>n[t]=!0),n},x0=/^[a-z][a-z0-9+\-.]*:/,tG=(e,n,t,r)=>X(null,null,function*(){if(e!=null&&e[0]!=="#"&&!x0.test(e)){let o=document.querySelector("ion-router");if(o)return n?.preventDefault(),o.push(e,t,r)}return!1});var iG=(e,n,t,r,o,i)=>X(null,null,function*(){var s;if(e)return e.attachViewToDom(n,t,o,r);if(!i&&typeof t!="string"&&!(t instanceof HTMLElement))throw new Error("framework delegate is missing");let a=typeof t=="string"?(s=n.ownerDocument)===null||s===void 0?void 0:s.createElement(t):t;return r&&r.forEach(c=>a.classList.add(c)),o&&Object.assign(a,o),n.appendChild(a),yield new Promise(c=>Lt(a,c)),a}),sG=(e,n)=>{if(n){if(e){let t=n.parentElement;return e.removeViewFromDom(t,n)}n.remove()}return Promise.resolve()},aG=()=>{let e,n;return{attachViewToDom:(c,u,...l)=>X(null,[c,u,...l],function*(o,i,s={},a=[]){var d,h;e=o;let f;if(i){let y=typeof i=="string"?(d=e.ownerDocument)===null||d===void 0?void 0:d.createElement(i):i;a.forEach(w=>y.classList.add(w)),Object.assign(y,s),e.appendChild(y),f=y,yield new Promise(w=>Lt(y,w))}else if(e.children.length>0&&(e.tagName==="ION-MODAL"||e.tagName==="ION-POPOVER")&&!(f=e.children[0]).classList.contains("ion-delegate-host")){let w=(h=e.ownerDocument)===null||h===void 0?void 0:h.createElement("div");w.classList.add("ion-delegate-host"),a.forEach(T=>w.classList.add(T)),w.append(...e.children),e.appendChild(w),f=w}let m=document.querySelector("ion-app")||document.body;return n=document.createComment("ionic teleport"),e.parentNode.insertBefore(n,e),m.appendChild(e),f??e}),removeViewFromDom:()=>(e&&n&&(n.parentNode.insertBefore(e,n),n.remove()),Promise.resolve())}};var Ti='[tabindex]:not([tabindex^="-"]):not([hidden]):not([disabled]), input:not([type=hidden]):not([tabindex^="-"]):not([hidden]):not([disabled]), textarea:not([tabindex^="-"]):not([hidden]):not([disabled]), button:not([tabindex^="-"]):not([hidden]):not([disabled]), select:not([tabindex^="-"]):not([hidden]):not([disabled]), ion-checkbox:not([tabindex^="-"]):not([hidden]):not([disabled]), ion-radio:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable[disabled="false"]:not([tabindex^="-"]):not([hidden])',GD=(e,n)=>{let t=e.querySelector(Ti);YD(t,n??e)},WD=(e,n)=>{let t=Array.from(e.querySelectorAll(Ti)),r=t.length>0?t[t.length-1]:null;YD(r,n??e)},YD=(e,n)=>{let t=e,r=e?.shadowRoot;if(r&&(t=r.querySelector(Ti)||e),t){let o=t.closest("ion-radio-group");o?o.setFocus():kc(t)}else n.focus()},Wf=0,O0=0,Ac=new WeakMap,QD=e=>({create(t){return F0(e,t)},dismiss(t,r,o){return B0(document,t,r,e,o)},getTop(){return X(this,null,function*(){return _i(document,e)})}});var k0=QD("ion-modal");var P0=QD("ion-popover");var mG=e=>{typeof document<"u"&&V0(document);let n=Wf++;e.overlayIndex=n},vG=e=>(e.hasAttribute("id")||(e.id=`ion-overlay-${++O0}`),e.id),F0=(e,n)=>typeof window<"u"&&typeof window.customElements<"u"?window.customElements.whenDefined(e).then(()=>{let t=document.createElement(e);return t.classList.add("overlay-hidden"),Object.assign(t,Object.assign(Object.assign({},n),{hasController:!0})),JD(document).appendChild(t),new Promise(r=>Lt(t,r))}):Promise.resolve(),L0=e=>e.classList.contains("overlay-hidden"),qD=(e,n)=>{let t=e,r=e?.shadowRoot;r&&(t=r.querySelector(Ti)||e),t?kc(t):n.focus()},j0=(e,n)=>{let t=_i(n,"ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover"),r=e.target;if(!t||!r||t.classList.contains(q0))return;let o=()=>{if(t===r)t.lastFocus=void 0;else if(r.tagName==="ION-TOAST")qD(t.lastFocus,t);else{let s=fh(t);if(!s.contains(r))return;let a=s.querySelector(".ion-overlay-wrapper");if(!a)return;if(a.contains(r)||r===s.querySelector("ion-backdrop"))t.lastFocus=r;else{let c=t.lastFocus;GD(a,t),c===n.activeElement&&WD(a,t),t.lastFocus=n.activeElement}}},i=()=>{if(t.contains(r))t.lastFocus=r;else if(r.tagName==="ION-TOAST")qD(t.lastFocus,t);else{let s=t.lastFocus;GD(t),s===n.activeElement&&WD(t),t.lastFocus=n.activeElement}};t.shadowRoot?i():o()},V0=e=>{Wf===0&&(Wf=1,e.addEventListener("focus",n=>{j0(n,e)},!0),e.addEventListener("ionBackButton",n=>{let t=_i(e);t?.backdropDismiss&&n.detail.register(ph,()=>{t.dismiss(void 0,ZD)})}),hh()||e.addEventListener("keydown",n=>{if(n.key==="Escape"){let t=_i(e);t?.backdropDismiss&&t.dismiss(void 0,ZD)}}))},B0=(e,n,t,r,o)=>{let i=_i(e,r,o);return i?i.dismiss(n,t):Promise.reject("overlay does not exist")},U0=(e,n)=>(n===void 0&&(n="ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover,ion-toast"),Array.from(e.querySelectorAll(n)).filter(t=>t.overlayIndex>0)),Rc=(e,n)=>U0(e,n).filter(t=>!L0(t)),_i=(e,n,t)=>{let r=Rc(e,n);return t===void 0?r[r.length-1]:r.find(o=>o.id===t)},KD=(e=!1)=>{let t=JD(document).querySelector("ion-router-outlet, ion-nav, #ion-view-container-root");t&&(e?t.setAttribute("aria-hidden","true"):t.removeAttribute("aria-hidden"))},yG=(e,n,t,r,o)=>X(null,null,function*(){var i,s;if(e.presented)return;e.el.tagName!=="ION-TOAST"&&(KD(!0),document.body.classList.add(Bc)),G0(e.el),eE(e.el),e.presented=!0,e.willPresent.emit(),(i=e.willPresentShorthand)===null||i===void 0||i.emit();let a=Gf(e),c=e.enterAnimation?e.enterAnimation:Re.get(n,a==="ios"?t:r);(yield XD(e,c,e.el,o))&&(e.didPresent.emit(),(s=e.didPresentShorthand)===null||s===void 0||s.emit()),e.el.tagName!=="ION-TOAST"&&H0(e.el),e.keyboardClose&&(document.activeElement===null||!e.el.contains(document.activeElement))&&e.el.focus(),e.el.removeAttribute("aria-hidden")}),H0=e=>X(null,null,function*(){let n=document.activeElement;if(!n)return;let t=n?.shadowRoot;t&&(n=t.querySelector(Ti)||n),yield e.onDidDismiss(),(document.activeElement===null||document.activeElement===document.body)&&n.focus()}),DG=(e,n,t,r,o,i,s)=>X(null,null,function*(){var a,c;if(!e.presented)return!1;let l=(Ft!==void 0?Rc(Ft):[]).filter(h=>h.tagName!=="ION-TOAST");l.length===1&&l[0].id===e.el.id&&(KD(!1),document.body.classList.remove(Bc)),e.presented=!1;try{eE(e.el),e.el.style.setProperty("pointer-events","none"),e.willDismiss.emit({data:n,role:t}),(a=e.willDismissShorthand)===null||a===void 0||a.emit({data:n,role:t});let h=Gf(e),f=e.leaveAnimation?e.leaveAnimation:Re.get(r,h==="ios"?o:i);t!==z0&&(yield XD(e,f,e.el,s)),e.didDismiss.emit({data:n,role:t}),(c=e.didDismissShorthand)===null||c===void 0||c.emit({data:n,role:t}),(Ac.get(e)||[]).forEach(y=>y.destroy()),Ac.delete(e),e.el.classList.add("overlay-hidden"),e.el.style.removeProperty("pointer-events"),e.el.lastFocus!==void 0&&(e.el.lastFocus=void 0)}catch(h){uh(`[${e.el.tagName.toLowerCase()}] - `,h)}return e.el.remove(),W0(),!0}),JD=e=>e.querySelector("ion-app")||e.body,XD=(e,n,t,r)=>X(null,null,function*(){t.classList.remove("overlay-hidden");let o=e.el,i=n(o,r);(!e.animated||!Re.getBoolean("animated",!0))&&i.duration(0),e.keyboardClose&&i.beforeAddWrite(()=>{let a=t.ownerDocument.activeElement;a?.matches("input,ion-input, ion-textarea")&&a.blur()});let s=Ac.get(e)||[];return Ac.set(e,[...s,i]),yield i.play(),!0}),EG=(e,n)=>{let t,r=new Promise(o=>t=o);return $0(e,n,o=>{t(o.detail)}),r},$0=(e,n,t)=>{let r=o=>{dh(e,n,r),t(o)};lh(e,n,r)};var ZD="backdrop",z0="gesture",CG=39;var wG=()=>{let e,n=()=>{e&&(e(),e=void 0)};return{addClickListener:(r,o)=>{n();let i=o!==void 0?document.getElementById(o):null;if(!i){Ri(`[${r.tagName.toLowerCase()}] - A trigger element with the ID "${o}" was not found in the DOM. The trigger element must be in the DOM when the "trigger" property is set on an overlay component.`,r);return}e=((a,c)=>{let u=()=>{c.present()};return a.addEventListener("click",u),()=>{a.removeEventListener("click",u)}})(i,r)},removeClickListener:n}},eE=e=>{Ft!==void 0&&eo("android")&&e.setAttribute("aria-hidden","true")},G0=e=>{var n;if(Ft===void 0)return;let t=Rc(Ft);for(let r=t.length-1;r>=0;r--){let o=t[r],i=(n=t[r+1])!==null&&n!==void 0?n:e;(i.hasAttribute("aria-hidden")||i.tagName!=="ION-TOAST")&&o.setAttribute("aria-hidden","true")}},W0=()=>{if(Ft===void 0)return;let e=Rc(Ft);for(let n=e.length-1;n>=0;n--){let t=e[n];if(t.removeAttribute("aria-hidden"),t.tagName!=="ION-TOAST")break}},q0="ion-disable-focus-trap";var Z0=["tabsInner"];var Y0=(()=>{class e{doc;_readyPromise;win;backButton=new H;keyboardDidShow=new H;keyboardDidHide=new H;pause=new H;resume=new H;resize=new H;constructor(t,r){this.doc=t,r.run(()=>{this.win=t.defaultView,this.backButton.subscribeWithPriority=function(i,s){return this.subscribe(a=>a.register(i,c=>r.run(()=>s(c))))},to(this.pause,t,"pause",r),to(this.resume,t,"resume",r),to(this.backButton,t,"ionBackButton",r),to(this.resize,this.win,"resize",r),to(this.keyboardDidShow,this.win,"ionKeyboardDidShow",r),to(this.keyboardDidHide,this.win,"ionKeyboardDidHide",r);let o;this._readyPromise=new Promise(i=>{o=i}),this.win?.cordova?t.addEventListener("deviceready",()=>{o("cordova")},{once:!0}):o("dom")})}is(t){return eo(this.win,t)}platforms(){return Sc(this.win)}ready(){return this._readyPromise}get isRTL(){return this.doc.dir==="rtl"}getQueryParam(t){return Q0(this.win.location.href,t)}isLandscape(){return!this.isPortrait()}isPortrait(){return this.win.matchMedia?.("(orientation: portrait)").matches}testUserAgent(t){let r=this.win.navigator;return!!(r?.userAgent&&r.userAgent.indexOf(t)>=0)}url(){return this.win.location.href}width(){return this.win.innerWidth}height(){return this.win.innerHeight}static \u0275fac=function(r){return new(r||e)(I(q),I(B))};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Q0=(e,n)=>{n=n.replace(/[[\]\\]/g,"\\$&");let r=new RegExp("[\\?&]"+n+"=([^&#]*)").exec(e);return r?decodeURIComponent(r[1].replace(/\+/g," ")):null},to=(e,n,t,r)=>{n&&n.addEventListener(t,o=>{r.run(()=>{let i=o?.detail;e.next(i)})})},Mi=(()=>{class e{location;serializer;router;topOutlet;direction=tE;animated=nE;animationBuilder;guessDirection="forward";guessAnimation;lastNavId=-1;constructor(t,r,o,i){this.location=r,this.serializer=o,this.router=i,i&&i.events.subscribe(s=>{if(s instanceof yt){let a=s.restoredState?s.restoredState.navigationId:s.id;this.guessDirection=this.guessAnimation=a<this.lastNavId?"back":"forward",this.lastNavId=this.guessDirection==="forward"?s.id:a}}),t.backButton.subscribeWithPriority(0,s=>{this.pop(),s()})}navigateForward(t,r={}){return this.setDirection("forward",r.animated,r.animationDirection,r.animation),this.navigate(t,r)}navigateBack(t,r={}){return this.setDirection("back",r.animated,r.animationDirection,r.animation),this.navigate(t,r)}navigateRoot(t,r={}){return this.setDirection("root",r.animated,r.animationDirection,r.animation),this.navigate(t,r)}back(t={animated:!0,animationDirection:"back"}){return this.setDirection("back",t.animated,t.animationDirection,t.animation),this.location.back()}pop(){return X(this,null,function*(){let t=this.topOutlet;for(;t;){if(yield t.pop())return!0;t=t.parentOutlet}return!1})}setDirection(t,r,o,i){this.direction=t,this.animated=K0(t,r,o),this.animationBuilder=i}setTopOutlet(t){this.topOutlet=t}consumeTransition(){let t="root",r,o=this.animationBuilder;return this.direction==="auto"?(t=this.guessDirection,r=this.guessAnimation):(r=this.animated,t=this.direction),this.direction=tE,this.animated=nE,this.animationBuilder=void 0,{direction:t,animation:r,animationBuilder:o}}navigate(t,r){if(Array.isArray(t))return this.router.navigate(t,r);{let o=this.serializer.parse(t.toString());return r.queryParams!==void 0&&(o.queryParams=g({},r.queryParams)),r.fragment!==void 0&&(o.fragment=r.fragment),this.router.navigateByUrl(o,r)}}static \u0275fac=function(r){return new(r||e)(I(Y0),I(rt),I(kt),I(fe,8))};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),K0=(e,n,t)=>{if(n!==!1){if(t!==void 0)return t;if(e==="forward"||e==="back")return e;if(e==="root"&&n===!0)return"forward"}},tE="auto",nE=void 0,sE=(()=>{class e{get(t,r){let o=qf();return o?o.get(t,r):null}getBoolean(t,r){let o=qf();return o?o.getBoolean(t,r):!1}getNumber(t,r){let o=qf();return o?o.getNumber(t,r):0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),J0=new D("USERCONFIG"),qf=()=>{if(typeof window<"u"){let e=window.Ionic;if(e?.config)return e.config}return null},Nc=class{data;constructor(n={}){this.data=n,console.warn("[Ionic Warning]: NavParams has been deprecated in favor of using Angular's input API. Developers should migrate to either the @Input decorator or the Signals-based input API.")}get(n){return this.data[n]}},X0=(()=>{class e{zone=p(B);applicationRef=p(Nt);config=p(J0);create(t,r,o){return new Yf(t,r,this.applicationRef,this.zone,o,this.config.useSetInputAPI??!1)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})(),Yf=class{environmentInjector;injector;applicationRef;zone;elementReferenceKey;enableSignalsSupport;elRefMap=new WeakMap;elEventsMap=new WeakMap;constructor(n,t,r,o,i,s){this.environmentInjector=n,this.injector=t,this.applicationRef=r,this.zone=o,this.elementReferenceKey=i,this.enableSignalsSupport=s}attachViewToDom(n,t,r,o){return this.zone.run(()=>new Promise(i=>{let s=g({},r);this.elementReferenceKey!==void 0&&(s[this.elementReferenceKey]=n);let a=eA(this.zone,this.environmentInjector,this.injector,this.applicationRef,this.elRefMap,this.elEventsMap,n,t,s,o,this.elementReferenceKey,this.enableSignalsSupport);i(a)}))}removeViewFromDom(n,t){return this.zone.run(()=>new Promise(r=>{let o=this.elRefMap.get(t);if(o){o.destroy(),this.elRefMap.delete(t);let i=this.elEventsMap.get(t);i&&(i(),this.elEventsMap.delete(t))}r()}))}},eA=(e,n,t,r,o,i,s,a,c,u,l,d)=>{let h=se.create({providers:nA(c),parent:t}),f=Ev(a,{environmentInjector:n,elementInjector:h}),m=f.instance,y=f.location.nativeElement;if(c)if(l&&m[l]!==void 0&&console.error(`[Ionic Error]: ${l} is a reserved property when using ${s.tagName.toLowerCase()}. Rename or remove the "${l}" property from ${a.name}.`),d===!0&&f.setInput!==void 0){let T=c,{modal:Ct,popover:K}=T,Yn=Oc(T,["modal","popover"]);for(let Ai in Yn)f.setInput(Ai,Yn[Ai]);Ct!==void 0&&Object.assign(m,{modal:Ct}),K!==void 0&&Object.assign(m,{popover:K})}else Object.assign(m,c);if(u)for(let Ct of u)y.classList.add(Ct);let w=aE(e,m,y);return s.appendChild(y),r.attachView(f.hostView),o.set(y,f),i.set(y,w),y},tA=[Pc,Fc,Lc,jc,Vc],aE=(e,n,t)=>e.run(()=>{let r=tA.filter(o=>typeof n[o]=="function").map(o=>{let i=s=>n[o](s.detail);return t.addEventListener(o,i),()=>t.removeEventListener(o,i)});return()=>r.forEach(o=>o())}),rE=new D("NavParamsToken"),nA=e=>[{provide:rE,useValue:e},{provide:Nc,useFactory:rA,deps:[rE]}],rA=e=>new Nc(e),oA=(e,n)=>{let t=e.prototype;n.forEach(r=>{Object.defineProperty(t,r,{get(){return this.el[r]},set(o){this.z.runOutsideAngular(()=>this.el[r]=o)}})})},iA=(e,n)=>{let t=e.prototype;n.forEach(r=>{t[r]=function(){let o=arguments;return this.z.runOutsideAngular(()=>this.el[r].apply(this.el,o))}})},rh=(e,n,t)=>{t.forEach(r=>e[r]=uo(n,r))};function xc(e){return function(t){let{defineCustomElementFn:r,inputs:o,methods:i}=e;return r!==void 0&&r(),o&&oA(t,o),i&&iA(t,i),t}}var sA=["alignment","animated","arrow","keepContentsMounted","backdropDismiss","cssClass","dismissOnSelect","enterAnimation","event","focusTrap","isOpen","keyboardClose","leaveAnimation","mode","showBackdrop","translucent","trigger","triggerAction","reference","size","side"],aA=["present","dismiss","onDidDismiss","onWillDismiss"],t8=(()=>{let e=class Qf{z;template;isCmpOpen=!1;el;constructor(t,r,o){this.z=o,this.el=r.nativeElement,this.el.addEventListener("ionMount",()=>{this.isCmpOpen=!0,t.detectChanges()}),this.el.addEventListener("didDismiss",()=>{this.isCmpOpen=!1,t.detectChanges()}),rh(this,this.el,["ionPopoverDidPresent","ionPopoverWillPresent","ionPopoverWillDismiss","ionPopoverDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||Qf)(v(ze),v(Q),v(B))};static \u0275dir=j({type:Qf,selectors:[["ion-popover"]],contentQueries:function(r,o,i){if(r&1&&Bo(i,et,5),r&2){let s;xr(s=Or())&&(o.template=s.first)}},inputs:{alignment:"alignment",animated:"animated",arrow:"arrow",keepContentsMounted:"keepContentsMounted",backdropDismiss:"backdropDismiss",cssClass:"cssClass",dismissOnSelect:"dismissOnSelect",enterAnimation:"enterAnimation",event:"event",focusTrap:"focusTrap",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",showBackdrop:"showBackdrop",translucent:"translucent",trigger:"trigger",triggerAction:"triggerAction",reference:"reference",size:"size",side:"side"},standalone:!1})};return e=ao([xc({inputs:sA,methods:aA})],e),e})(),cA=["animated","keepContentsMounted","backdropBreakpoint","backdropDismiss","breakpoints","canDismiss","cssClass","enterAnimation","expandToScroll","event","focusTrap","handle","handleBehavior","initialBreakpoint","isOpen","keyboardClose","leaveAnimation","mode","presentingElement","showBackdrop","translucent","trigger"],uA=["present","dismiss","onDidDismiss","onWillDismiss","setCurrentBreakpoint","getCurrentBreakpoint"],n8=(()=>{let e=class Kf{z;template;isCmpOpen=!1;el;constructor(t,r,o){this.z=o,this.el=r.nativeElement,this.el.addEventListener("ionMount",()=>{this.isCmpOpen=!0,t.detectChanges()}),this.el.addEventListener("didDismiss",()=>{this.isCmpOpen=!1,t.detectChanges()}),rh(this,this.el,["ionModalDidPresent","ionModalWillPresent","ionModalWillDismiss","ionModalDidDismiss","ionBreakpointDidChange","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||Kf)(v(ze),v(Q),v(B))};static \u0275dir=j({type:Kf,selectors:[["ion-modal"]],contentQueries:function(r,o,i){if(r&1&&Bo(i,et,5),r&2){let s;xr(s=Or())&&(o.template=s.first)}},inputs:{animated:"animated",keepContentsMounted:"keepContentsMounted",backdropBreakpoint:"backdropBreakpoint",backdropDismiss:"backdropDismiss",breakpoints:"breakpoints",canDismiss:"canDismiss",cssClass:"cssClass",enterAnimation:"enterAnimation",expandToScroll:"expandToScroll",event:"event",focusTrap:"focusTrap",handle:"handle",handleBehavior:"handleBehavior",initialBreakpoint:"initialBreakpoint",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",presentingElement:"presentingElement",showBackdrop:"showBackdrop",translucent:"translucent",trigger:"trigger"},standalone:!1})};return e=ao([xc({inputs:cA,methods:uA})],e),e})(),lA=(e,n,t)=>t==="root"?cE(e,n):t==="forward"?dA(e,n):fA(e,n),cE=(e,n)=>(e=e.filter(t=>t.stackId!==n.stackId),e.push(n),e),dA=(e,n)=>(e.indexOf(n)>=0?e=e.filter(r=>r.stackId!==n.stackId||r.id<=n.id):e.push(n),e),fA=(e,n)=>e.indexOf(n)>=0?e.filter(r=>r.stackId!==n.stackId||r.id<=n.id):cE(e,n),Jf=(e,n)=>{let t=e.createUrlTree(["."],{relativeTo:n});return e.serializeUrl(t)},uE=(e,n)=>n?e.stackId!==n.stackId:!0,hA=(e,n)=>{if(!e)return;let t=lE(n);for(let r=0;r<t.length;r++){if(r>=e.length)return t[r];if(t[r]!==e[r])return}},lE=e=>e.split("/").map(n=>n.trim()).filter(n=>n!==""),dE=e=>{e&&(e.ref.destroy(),e.unlistenEvents())},Xf=class{containerEl;router;navCtrl;zone;location;views=[];runningTask;skipTransition=!1;tabsPrefix;activeView;nextId=0;constructor(n,t,r,o,i,s){this.containerEl=t,this.router=r,this.navCtrl=o,this.zone=i,this.location=s,this.tabsPrefix=n!==void 0?lE(n):void 0}createView(n,t){let r=Jf(this.router,t),o=n?.location?.nativeElement,i=aE(this.zone,n.instance,o);return{id:this.nextId++,stackId:hA(this.tabsPrefix,r),unlistenEvents:i,element:o,ref:n,url:r}}getExistingView(n){let t=Jf(this.router,n),r=this.views.find(o=>o.url===t);return r&&r.ref.changeDetectorRef.reattach(),r}setActive(n){let t=this.navCtrl.consumeTransition(),{direction:r,animation:o,animationBuilder:i}=t,s=this.activeView,a=uE(n,s);a&&(r="back",o=void 0);let c=this.views.slice(),u,l=this.router;l.getCurrentNavigation?u=l.getCurrentNavigation():l.navigations?.value&&(u=l.navigations.value),u?.extras?.replaceUrl&&this.views.length>0&&this.views.splice(-1,1);let d=this.views.includes(n),h=this.insertView(n,r);d||n.ref.changeDetectorRef.detectChanges();let f=n.animationBuilder;return i===void 0&&r==="back"&&!a&&f!==void 0&&(i=f),s&&(s.animationBuilder=i),this.zone.runOutsideAngular(()=>this.wait(()=>(s&&s.ref.changeDetectorRef.detach(),n.ref.changeDetectorRef.reattach(),this.transition(n,s,o,this.canGoBack(1),!1,i).then(()=>pA(n,h,c,this.location,this.zone)).then(()=>({enteringView:n,direction:r,animation:o,tabSwitch:a})))))}canGoBack(n,t=this.getActiveStackId()){return this.getStack(t).length>n}pop(n,t=this.getActiveStackId()){return this.zone.run(()=>{let r=this.getStack(t);if(r.length<=n)return Promise.resolve(!1);let o=r[r.length-n-1],i=o.url,s=o.savedData;if(s){let c=s.get("primary");c?.route?._routerState?.snapshot.url&&(i=c.route._routerState.snapshot.url)}let{animationBuilder:a}=this.navCtrl.consumeTransition();return this.navCtrl.navigateBack(i,A(g({},o.savedExtras),{animation:a})).then(()=>!0)})}startBackTransition(){let n=this.activeView;if(n){let t=this.getStack(n.stackId),r=t[t.length-2],o=r.animationBuilder;return this.wait(()=>this.transition(r,n,"back",this.canGoBack(2),!0,o))}return Promise.resolve()}endBackTransition(n){n?(this.skipTransition=!0,this.pop(1)):this.activeView&&fE(this.activeView,this.views,this.views,this.location,this.zone)}getLastUrl(n){let t=this.getStack(n);return t.length>0?t[t.length-1]:void 0}getRootUrl(n){let t=this.getStack(n);return t.length>0?t[0]:void 0}getActiveStackId(){return this.activeView?this.activeView.stackId:void 0}getActiveView(){return this.activeView}hasRunningTask(){return this.runningTask!==void 0}destroy(){this.containerEl=void 0,this.views.forEach(dE),this.activeView=void 0,this.views=[]}getStack(n){return this.views.filter(t=>t.stackId===n)}insertView(n,t){return this.activeView=n,this.views=lA(this.views,n,t),this.views.slice()}transition(n,t,r,o,i,s){if(this.skipTransition)return this.skipTransition=!1,Promise.resolve(!1);if(t===n)return Promise.resolve(!1);let a=n?n.element:void 0,c=t?t.element:void 0,u=this.containerEl;return a&&a!==c&&(a.classList.add("ion-page"),a.classList.add("ion-page-invisible"),u.commit)?u.commit(a,c,{duration:r===void 0?0:void 0,direction:r,showGoBack:o,progressAnimation:i,animationBuilder:s}):Promise.resolve(!1)}wait(n){return X(this,null,function*(){this.runningTask!==void 0&&(yield this.runningTask,this.runningTask=void 0);let t=this.runningTask=n();return t.finally(()=>this.runningTask=void 0),t})}},pA=(e,n,t,r,o)=>typeof requestAnimationFrame=="function"?new Promise(i=>{requestAnimationFrame(()=>{fE(e,n,t,r,o),i()})}):Promise.resolve(),fE=(e,n,t,r,o)=>{o.run(()=>t.filter(i=>!n.includes(i)).forEach(dE)),n.forEach(i=>{let a=r.path().split("?")[0].split("#")[0];if(i!==e&&i.url!==a){let c=i.element;c.setAttribute("aria-hidden","true"),c.classList.add("ion-page-hidden"),i.ref.changeDetectorRef.detach()}})},gA=(()=>{class e{parentOutlet;nativeEl;activatedView=null;tabsPrefix;_swipeGesture;stackCtrl;proxyMap=new WeakMap;currentActivatedRoute$=new te(null);activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=M;stackWillChange=new $;stackDidChange=new $;activateEvents=new $;deactivateEvents=new $;parentContexts=p(Et);location=p(Ue);environmentInjector=p(z);inputBinder=p(hE,{optional:!0});supportsBindingToComponentInputs=!0;config=p(sE);navCtrl=p(Mi);set animation(t){this.nativeEl.animation=t}set animated(t){this.nativeEl.animated=t}set swipeGesture(t){this._swipeGesture=t,this.nativeEl.swipeHandler=t?{canStart:()=>this.stackCtrl.canGoBack(1)&&!this.stackCtrl.hasRunningTask(),onStart:()=>this.stackCtrl.startBackTransition(),onEnd:r=>this.stackCtrl.endBackTransition(r)}:void 0}constructor(t,r,o,i,s,a,c,u){this.parentOutlet=u,this.nativeEl=i.nativeElement,this.name=t||M,this.tabsPrefix=r==="true"?Jf(s,c):void 0,this.stackCtrl=new Xf(this.tabsPrefix,this.nativeEl,s,this.navCtrl,a,o),this.parentContexts.onChildOutletCreated(this.name,this)}ngOnDestroy(){this.stackCtrl.destroy(),this.inputBinder?.unsubscribeFromRouteData(this)}getContext(){return this.parentContexts.getContext(this.name)}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(!this.activated){let t=this.getContext();t?.route&&this.activateWith(t.route,t.injector)}new Promise(t=>Lt(this.nativeEl,t)).then(()=>{this._swipeGesture===void 0&&(this.swipeGesture=this.config.getBoolean("swipeBackEnabled",this.nativeEl.mode==="ios"))})}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new Error("Outlet is not activated");return this.activated.instance}get activatedRoute(){if(!this.activated)throw new Error("Outlet is not activated");return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){throw new Error("incompatible reuse strategy")}attach(t,r){throw new Error("incompatible reuse strategy")}deactivate(){if(this.activated){if(this.activatedView){let r=this.getContext();this.activatedView.savedData=new Map(r.children.contexts);let o=this.activatedView.savedData.get("primary");if(o&&r.route&&(o.route=g({},r.route)),this.activatedView.savedExtras={},r.route){let i=r.route.snapshot;this.activatedView.savedExtras.queryParams=i.queryParams,this.activatedView.savedExtras.fragment=i.fragment}}let t=this.component;this.activatedView=null,this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(t)}}activateWith(t,r){if(this.isActivated)throw new Error("Cannot activate an already activated outlet");this._activatedRoute=t;let o,i=this.stackCtrl.getExistingView(t);if(i){o=this.activated=i.ref;let a=i.savedData;if(a){let c=this.getContext();c.children.contexts=a}this.updateActivatedRouteProxy(o.instance,t)}else{let a=t._futureSnapshot,c=this.parentContexts.getOrCreateContext(this.name).children,u=new te(null),l=this.createActivatedRouteProxy(u,t),d=new eh(l,c,this.location.injector),h=a.routeConfig.component??a.component;o=this.activated=this.outletContent.createComponent(h,{index:this.outletContent.length,injector:d,environmentInjector:r??this.environmentInjector}),u.next(o.instance),i=this.stackCtrl.createView(this.activated,t),this.proxyMap.set(o.instance,l),this.currentActivatedRoute$.next({component:o.instance,activatedRoute:t})}this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activatedView=i,this.navCtrl.setTopOutlet(this);let s=this.stackCtrl.getActiveView();this.stackWillChange.emit({enteringView:i,tabSwitch:uE(i,s)}),this.stackCtrl.setActive(i).then(a=>{this.activateEvents.emit(o.instance),this.stackDidChange.emit(a)})}canGoBack(t=1,r){return this.stackCtrl.canGoBack(t,r)}pop(t=1,r){return this.stackCtrl.pop(t,r)}getLastUrl(t){let r=this.stackCtrl.getLastUrl(t);return r?r.url:void 0}getLastRouteView(t){return this.stackCtrl.getLastUrl(t)}getRootView(t){return this.stackCtrl.getRootUrl(t)}getActiveStackId(){return this.stackCtrl.getActiveStackId()}createActivatedRouteProxy(t,r){let o=new Ce;return o._futureSnapshot=r._futureSnapshot,o._routerState=r._routerState,o.snapshot=r.snapshot,o.outlet=r.outlet,o.component=r.component,o._paramMap=this.proxyObservable(t,"paramMap"),o._queryParamMap=this.proxyObservable(t,"queryParamMap"),o.url=this.proxyObservable(t,"url"),o.params=this.proxyObservable(t,"params"),o.queryParams=this.proxyObservable(t,"queryParams"),o.fragment=this.proxyObservable(t,"fragment"),o.data=this.proxyObservable(t,"data"),o}proxyObservable(t,r){return t.pipe(oe(o=>!!o),ie(o=>this.currentActivatedRoute$.pipe(oe(i=>i!==null&&i.component===o),ie(i=>i&&i.activatedRoute[r]),su())))}updateActivatedRouteProxy(t,r){let o=this.proxyMap.get(t);if(!o)throw new Error("Could not find activated route proxy for view");o._futureSnapshot=r._futureSnapshot,o._routerState=r._routerState,o.snapshot=r.snapshot,o.outlet=r.outlet,o.component=r.component,this.currentActivatedRoute$.next({component:t,activatedRoute:r})}static \u0275fac=function(r){return new(r||e)(en("name"),en("tabs"),v(rt),v(Q),v(fe),v(B),v(Ce),v(e,12))};static \u0275dir=j({type:e,selectors:[["ion-router-outlet"]],inputs:{animated:"animated",animation:"animation",mode:"mode",swipeGesture:"swipeGesture",name:"name"},outputs:{stackWillChange:"stackWillChange",stackDidChange:"stackDidChange",activateEvents:"activate",deactivateEvents:"deactivate"},exportAs:["outlet"],standalone:!1})}return e})(),eh=class{route;childContexts;parent;constructor(n,t,r){this.route=n,this.childContexts=t,this.parent=r}get(n,t){return n===Ce?this.route:n===Et?this.childContexts:this.parent.get(n,t)}},hE=new D(""),mA=(()=>{class e{outletDataSubscriptions=new Map;bindActivatedRouteToOutletComponent(t){this.unsubscribeFromRouteData(t),this.subscribeToRouteData(t)}unsubscribeFromRouteData(t){this.outletDataSubscriptions.get(t)?.unsubscribe(),this.outletDataSubscriptions.delete(t)}subscribeToRouteData(t){let{activatedRoute:r}=t,o=mn([r.queryParams,r.params,r.data]).pipe(ie(([i,s,a],c)=>(a=g(g(g({},i),s),a),c===0?b(a):Promise.resolve(a)))).subscribe(i=>{if(!t.isActivated||!t.activatedComponentRef||t.activatedRoute!==r||r.component===null){this.unsubscribeFromRouteData(t);return}let s=ba(r.component);if(!s){this.unsubscribeFromRouteData(t);return}for(let{templateName:a}of s.inputs)t.activatedComponentRef.setInput(a,i[a])});this.outletDataSubscriptions.set(t,o)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})(),r8=()=>({provide:hE,useFactory:vA,deps:[fe]});function vA(e){return e?.componentInputBindingEnabled?new mA:null}var yA=["color","defaultHref","disabled","icon","mode","routerAnimation","text","type"],o8=(()=>{let e=class th{routerOutlet;navCtrl;config;r;z;el;constructor(t,r,o,i,s,a){this.routerOutlet=t,this.navCtrl=r,this.config=o,this.r=i,this.z=s,a.detach(),this.el=this.r.nativeElement}onClick(t){let r=this.defaultHref||this.config.get("backButtonDefaultHref");this.routerOutlet?.canGoBack()?(this.navCtrl.setDirection("back",void 0,void 0,this.routerAnimation),this.routerOutlet.pop(),t.preventDefault()):r!=null&&(this.navCtrl.navigateBack(r,{animation:this.routerAnimation}),t.preventDefault())}static \u0275fac=function(r){return new(r||th)(v(gA,8),v(Mi),v(sE),v(Q),v(B),v(ze))};static \u0275dir=j({type:th,hostBindings:function(r,o){r&1&&Pe("click",function(s){return o.onClick(s)})},inputs:{color:"color",defaultHref:"defaultHref",disabled:"disabled",icon:"icon",mode:"mode",routerAnimation:"routerAnimation",text:"text",type:"type"},standalone:!1})};return e=ao([xc({inputs:yA})],e),e})(),i8=(()=>{class e{locationStrategy;navCtrl;elementRef;router;routerLink;routerDirection="forward";routerAnimation;constructor(t,r,o,i,s){this.locationStrategy=t,this.navCtrl=r,this.elementRef=o,this.router=i,this.routerLink=s}ngOnInit(){this.updateTargetUrlAndHref(),this.updateTabindex()}ngOnChanges(){this.updateTargetUrlAndHref()}updateTabindex(){let t=["ION-BACK-BUTTON","ION-BREADCRUMB","ION-BUTTON","ION-CARD","ION-FAB-BUTTON","ION-ITEM","ION-ITEM-OPTION","ION-MENU-BUTTON","ION-SEGMENT-BUTTON","ION-TAB-BUTTON"],r=this.elementRef.nativeElement;t.includes(r.tagName)&&r.getAttribute("tabindex")==="0"&&r.removeAttribute("tabindex")}updateTargetUrlAndHref(){if(this.routerLink?.urlTree){let t=this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));this.elementRef.nativeElement.href=t}}onClick(t){this.navCtrl.setDirection(this.routerDirection,void 0,void 0,this.routerAnimation),t.preventDefault()}static \u0275fac=function(r){return new(r||e)(v(Ae),v(Mi),v(Q),v(fe),v(vi,8))};static \u0275dir=j({type:e,selectors:[["","routerLink","",5,"a",5,"area"]],hostBindings:function(r,o){r&1&&Pe("click",function(s){return o.onClick(s)})},inputs:{routerDirection:"routerDirection",routerAnimation:"routerAnimation"},standalone:!1,features:[De]})}return e})(),s8=(()=>{class e{locationStrategy;navCtrl;elementRef;router;routerLink;routerDirection="forward";routerAnimation;constructor(t,r,o,i,s){this.locationStrategy=t,this.navCtrl=r,this.elementRef=o,this.router=i,this.routerLink=s}ngOnInit(){this.updateTargetUrlAndHref()}ngOnChanges(){this.updateTargetUrlAndHref()}updateTargetUrlAndHref(){if(this.routerLink?.urlTree){let t=this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));this.elementRef.nativeElement.href=t}}onClick(){this.navCtrl.setDirection(this.routerDirection,void 0,void 0,this.routerAnimation)}static \u0275fac=function(r){return new(r||e)(v(Ae),v(Mi),v(Q),v(fe),v(vi,8))};static \u0275dir=j({type:e,selectors:[["a","routerLink",""],["area","routerLink",""]],hostBindings:function(r,o){r&1&&Pe("click",function(){return o.onClick()})},inputs:{routerDirection:"routerDirection",routerAnimation:"routerAnimation"},standalone:!1,features:[De]})}return e})(),DA=["animated","animation","root","rootParams","swipeGesture"],EA=["push","insert","insertPages","pop","popTo","popToRoot","removeIndex","setRoot","setPages","getActive","getByIndex","canGoBack","getPrevious"],a8=(()=>{let e=class nh{z;el;constructor(t,r,o,i,s,a){this.z=s,a.detach(),this.el=t.nativeElement,t.nativeElement.delegate=i.create(r,o),rh(this,this.el,["ionNavDidChange","ionNavWillChange"])}static \u0275fac=function(r){return new(r||nh)(v(Q),v(z),v(se),v(X0),v(B),v(ze))};static \u0275dir=j({type:nh,inputs:{animated:"animated",animation:"animation",root:"root",rootParams:"rootParams",swipeGesture:"swipeGesture"},standalone:!1})};return e=ao([xc({inputs:DA,methods:EA})],e),e})(),c8=(()=>{class e{navCtrl;tabsInner;ionTabsWillChange=new $;ionTabsDidChange=new $;tabBarSlot="bottom";hasTab=!1;selectedTab;leavingTab;constructor(t){this.navCtrl=t}ngAfterViewInit(){let t=this.tabs.length>0?this.tabs.first:void 0;t&&(this.hasTab=!0,this.setActiveTab(t.tab),this.tabSwitch())}ngAfterContentInit(){this.detectSlotChanges()}ngAfterContentChecked(){this.detectSlotChanges()}onStackWillChange({enteringView:t,tabSwitch:r}){let o=t.stackId;r&&o!==void 0&&this.ionTabsWillChange.emit({tab:o})}onStackDidChange({enteringView:t,tabSwitch:r}){let o=t.stackId;r&&o!==void 0&&(this.tabBar&&(this.tabBar.selectedTab=o),this.ionTabsDidChange.emit({tab:o}))}select(t){let r=typeof t=="string",o=r?t:t.detail.tab;if(this.hasTab){this.setActiveTab(o),this.tabSwitch();return}let i=this.outlet.getActiveStackId()===o,s=`${this.outlet.tabsPrefix}/${o}`;if(r||t.stopPropagation(),i){let a=this.outlet.getActiveStackId();if(this.outlet.getLastRouteView(a)?.url===s)return;let u=this.outlet.getRootView(o),l=u&&s===u.url&&u.savedExtras;return this.navCtrl.navigateRoot(s,A(g({},l),{animated:!0,animationDirection:"back"}))}else{let a=this.outlet.getLastRouteView(o),c=a?.url||s,u=a?.savedExtras;return this.navCtrl.navigateRoot(c,A(g({},u),{animated:!0,animationDirection:"back"}))}}setActiveTab(t){let o=this.tabs.find(i=>i.tab===t);if(!o){console.error(`[Ionic Error]: Tab with id: "${t}" does not exist`);return}this.leavingTab=this.selectedTab,this.selectedTab=o,this.ionTabsWillChange.emit({tab:t}),o.el.active=!0}tabSwitch(){let{selectedTab:t,leavingTab:r}=this;this.tabBar&&t&&(this.tabBar.selectedTab=t.tab),r?.tab!==t?.tab&&r?.el&&(r.el.active=!1),t&&this.ionTabsDidChange.emit({tab:t.tab})}getSelected(){return this.hasTab?this.selectedTab?.tab:this.outlet.getActiveStackId()}detectSlotChanges(){this.tabBars.forEach(t=>{let r=t.el.getAttribute("slot");r!==this.tabBarSlot&&(this.tabBarSlot=r,this.relocateTabBar())})}relocateTabBar(){let t=this.tabBar.el;this.tabBarSlot==="top"?this.tabsInner.nativeElement.before(t):this.tabsInner.nativeElement.after(t)}static \u0275fac=function(r){return new(r||e)(v(Mi))};static \u0275dir=j({type:e,selectors:[["ion-tabs"]],viewQuery:function(r,o){if(r&1&&xd(Z0,7,Q),r&2){let i;xr(i=Or())&&(o.tabsInner=i.first)}},hostBindings:function(r,o){r&1&&Pe("ionTabButtonClick",function(s){return o.select(s)})},outputs:{ionTabsWillChange:"ionTabsWillChange",ionTabsDidChange:"ionTabsDidChange"},standalone:!1})}return e})(),CA=e=>typeof __zone_symbol__requestAnimationFrame=="function"?__zone_symbol__requestAnimationFrame(e):typeof requestAnimationFrame=="function"?requestAnimationFrame(e):setTimeout(e),u8=(()=>{class e{injector;elementRef;onChange=()=>{};onTouched=()=>{};lastValue;statusChanges;constructor(t,r){this.injector=t,this.elementRef=r}writeValue(t){this.elementRef.nativeElement.value=this.lastValue=t,Si(this.elementRef)}handleValueChange(t,r){t===this.elementRef.nativeElement&&(r!==this.lastValue&&(this.lastValue=r,this.onChange(r)),Si(this.elementRef))}_handleBlurEvent(t){t===this.elementRef.nativeElement?(this.onTouched(),Si(this.elementRef)):t.closest("ion-radio-group")===this.elementRef.nativeElement&&this.onTouched()}registerOnChange(t){this.onChange=t}registerOnTouched(t){this.onTouched=t}setDisabledState(t){this.elementRef.nativeElement.disabled=t}ngOnDestroy(){this.statusChanges&&this.statusChanges.unsubscribe()}ngAfterViewInit(){let t;try{t=this.injector.get(Pt)}catch{}if(!t)return;t.statusChanges&&(this.statusChanges=t.statusChanges.subscribe(()=>Si(this.elementRef)));let r=t.control;r&&["markAsTouched","markAllAsTouched","markAsUntouched","markAsDirty","markAsPristine"].forEach(i=>{if(typeof r[i]<"u"){let s=r[i].bind(r);r[i]=(...a)=>{s(...a),Si(this.elementRef)}}})}static \u0275fac=function(r){return new(r||e)(v(se),v(Q))};static \u0275dir=j({type:e,hostBindings:function(r,o){r&1&&Pe("ionBlur",function(s){return o._handleBlurEvent(s.target)})},standalone:!1})}return e})(),Si=e=>{CA(()=>{let n=e.nativeElement,t=n.value!=null&&n.value.toString().length>0,r=wA(n);Zf(n,r);let o=n.closest("ion-item");o&&(t?Zf(o,[...r,"item-has-value"]):Zf(o,r))})},wA=e=>{let n=e.classList,t=[];for(let r=0;r<n.length;r++){let o=n.item(r);o!==null&&IA(o,"ng-")&&t.push(`ion-${o.substring(3)}`)}return t},Zf=(e,n)=>{let t=e.classList;t.remove("ion-valid","ion-invalid","ion-touched","ion-untouched","ion-dirty","ion-pristine"),t.add(...n)},IA=(e,n)=>e.substring(0,n.length)===n,oE=class{shouldDetach(n){return!1}shouldAttach(n){return!1}store(n,t){}retrieve(n){return null}shouldReuseRoute(n,t){if(n.routeConfig!==t.routeConfig)return!1;let r=n.params,o=t.params,i=Object.keys(r),s=Object.keys(o);if(i.length!==s.length)return!1;for(let a of i)if(o[a]!==r[a])return!1;return!0}},iE=class{ctrl;constructor(n){this.ctrl=n}create(n){return this.ctrl.create(n||{})}dismiss(n,t,r){return this.ctrl.dismiss(n,t,r)}getTop(){return this.ctrl.getTop()}};export{te as a,ao as b,cr as c,uo as d,oe as e,it as f,ce as g,Ye as h,E as i,_e as j,I as k,p as l,bn as m,z as n,_p as o,Tp as p,se as q,q as r,Xt as s,en as t,Q as u,gw as v,mw as w,jw as x,v as y,Ue as z,yd as A,ke as B,j as C,He as D,Hm as E,B as F,Td as G,Vn as H,Qm as I,Md as J,Ad as K,Da as L,Rd as M,Nd as N,Km as O,Gb as P,Pe as Q,qb as R,Yb as S,Qb as T,Bo as U,xd as V,xr as W,Or as X,ev as Y,Uo as Z,h_ as _,nv as $,Od as aa,rv as ba,iv as ca,v_ as da,sv as ea,rn as fa,w_ as ga,ze as ha,rt as ia,Sv as ja,nT as ka,rT as la,Mv as ma,DT as na,xt as oa,Kv as pa,ry as qa,qT as ra,ZT as sa,We as ta,Ce as ua,Gy as va,fe as wa,bM as xa,TM as ya,tD as za,SM as Aa,qz as Ba,eo as Ca,Gf as Da,R0 as Ea,Jz as Fa,Xz as Ga,eG as Ha,tG as Ia,iG as Ja,sG as Ka,aG as La,GD as Ma,k0 as Na,P0 as Oa,mG as Pa,vG as Qa,yG as Ra,DG as Sa,EG as Ta,ZD as Ua,z0 as Va,CG as Wa,wG as Xa,q0 as Ya,Lf as Za,gD as _a,bi as $a,iD as ab,jz as bb,Vz as cb,s0 as db,Uz as eb,c0 as fb,l0 as gb,f0 as hb,p0 as ib,Hz as jb,$z as kb,zz as lb,Mi as mb,sE as nb,J0 as ob,X0 as pb,xc as qb,t8 as rb,n8 as sb,gA as tb,r8 as ub,o8 as vb,i8 as wb,s8 as xb,a8 as yb,c8 as zb,CA as Ab,u8 as Bb,Si as Cb,oE as Db,iE as Eb};
