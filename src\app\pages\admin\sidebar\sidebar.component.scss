.sidebar-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &.collapsed {
    .sidebar-header .logo-text,
    .section-title,
    .item-title,
    .item-badge,
    .quick-actions,
    .sidebar-footer {
      opacity: 0;
      visibility: hidden;
      transition: opacity 0.2s ease, visibility 0.2s ease;
    }

    .menu-item:hover .tooltip {
      opacity: 1;
      visibility: visible;
    }

    .menu-item .item-content {
      justify-content: center;
    }
  }
}

/* Sidebar Header */
.sidebar-header {
  padding: clamp(16px, 4vw, 24px) clamp(16px, 4vw, 20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  flex-shrink: 0;

  .logo-container {
    display: flex;
    align-items: center;
    gap: clamp(8px, 2vw, 12px);
    min-height: 48px;

    .logo-icon {
      width: clamp(40px, 8vw, 48px);
      height: clamp(40px, 8vw, 48px);
      background: #c8102e;
      border-radius: clamp(8px, 2vw, 12px);
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 12px rgba(200, 16, 46, 0.3);
      flex-shrink: 0;

      ion-icon {
        font-size: clamp(20px, 5vw, 24px);
        color: white;
      }
    }

    .logo-text {
      transition: all 0.3s ease;
      min-width: 0;
      flex: 1;

      h3 {
        margin: 0;
        font-size: clamp(16px, 4vw, 18px);
        font-weight: 700;
        color: #c8102e;
        line-height: 1.2;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      p {
        margin: 0;
        font-size: clamp(10px, 2.5vw, 12px);
        color: #718096;
        font-weight: 500;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

/* Navigation */
.sidebar-nav {
  flex: 1;
  padding: clamp(16px, 4vw, 20px) 0;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;

  .nav-section {
    margin-bottom: clamp(24px, 6vw, 32px);

    .section-title {
      font-size: clamp(10px, 2.5vw, 11px);
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 1px;
      color: #c8102e;
      margin: 0 clamp(16px, 4vw, 20px) clamp(12px, 3vw, 16px);
      transition: all 0.3s ease;
    }
  }

  .menu-items {
    .menu-item {
      position: relative;
      margin: clamp(2px, 1vw, 4px) clamp(8px, 2vw, 12px);
      border-radius: clamp(8px, 2vw, 12px);
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      overflow: hidden;
      touch-action: manipulation;
      -webkit-tap-highlight-color: transparent;

      &:hover,
      &:focus {
        background: rgba(200, 16, 46, 0.1);
        transform: translateX(clamp(2px, 1vw, 4px));

        .item-icon ion-icon {
          color: #c8102e;
          transform: scale(1.1);
        }
      }

      &:active {
        transform: translateX(clamp(1px, 0.5vw, 2px)) scale(0.98);
        background: rgba(200, 16, 46, 0.15);
      }

      &.active {
        background: linear-gradient(135deg, rgba(200, 16, 46, 0.15), rgba(200, 16, 46, 0.1));
        border-left: clamp(3px, 1vw, 4px) solid #c8102e;

        .item-content {
          .item-icon ion-icon {
            color: #c8102e;
          }

          .item-title {
            color: #c8102e;
            font-weight: 600;
          }
        }
      }

      .item-content {
        display: flex;
        align-items: center;
        padding: clamp(10px, 2.5vw, 12px) clamp(12px, 3vw, 16px);
        gap: clamp(8px, 2vw, 12px);
        min-height: 44px; /* Minimum touch target size */

        .item-icon {
          width: clamp(20px, 5vw, 24px);
          height: clamp(20px, 5vw, 24px);
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;

          ion-icon {
            font-size: clamp(18px, 4vw, 20px);
            color: #4a5568;
            transition: all 0.3s ease;
          }
        }

        .item-title {
          flex: 1;
          font-size: clamp(13px, 3vw, 14px);
          font-weight: 500;
          color: #4a5568;
          transition: all 0.3s ease;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          min-width: 0;
        }
      }

      .tooltip {
        position: absolute;
        left: 100%;
        top: 50%;
        transform: translateY(-50%);
        background: #c8102e;
        color: white;
        padding: clamp(6px, 1.5vw, 8px) clamp(8px, 2vw, 12px);
        border-radius: clamp(6px, 1.5vw, 8px);
        font-size: clamp(11px, 2.5vw, 12px);
        font-weight: 500;
        white-space: nowrap;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 1000;
        margin-left: clamp(6px, 1.5vw, 8px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        pointer-events: none;

        &::before {
          content: '';
          position: absolute;
          left: -4px;
          top: 50%;
          transform: translateY(-50%);
          border: 4px solid transparent;
          border-right-color: #c8102e;
        }

        .tooltip-badge {
          background: #667eea;
          color: white;
          font-size: clamp(9px, 2vw, 10px);
          padding: 2px 6px;
          border-radius: clamp(6px, 1.5vw, 8px);
          margin-left: clamp(6px, 1.5vw, 8px);
        }
      }

      /* Hide tooltips on touch devices */
      @media (hover: none) {
        .tooltip {
          display: none;
        }
      }
    }
  }

  /* Quick Actions */
  .quick-actions {
    padding: 0 clamp(16px, 4vw, 20px);

    .action-btn {
      width: 100%;
      padding: clamp(10px, 2.5vw, 12px) clamp(12px, 3vw, 16px);
      border: none;
      border-radius: clamp(8px, 2vw, 10px);
      font-size: clamp(12px, 3vw, 13px);
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: clamp(6px, 1.5vw, 8px);
      margin-bottom: clamp(6px, 1.5vw, 8px);
      min-height: 44px; /* Minimum touch target */
      touch-action: manipulation;
      -webkit-tap-highlight-color: transparent;

      ion-icon {
        font-size: clamp(14px, 3.5vw, 16px);
        flex-shrink: 0;
      }

      &.primary {
        background: #c8102e;
        color: white;
        box-shadow: 0 4px 12px rgba(200, 16, 46, 0.3);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(200, 16, 46, 0.4);
        }

        &:active {
          transform: translateY(0) scale(0.98);
        }
      }

      &.secondary {
        background: rgba(200, 16, 46, 0.1);
        color: #c8102e;
        border: 1px solid rgba(200, 16, 46, 0.2);

        &:hover {
          background: rgba(200, 16, 46, 0.15);
          transform: translateY(-1px);
        }

        &:active {
          transform: translateY(0) scale(0.98);
        }
      }
    }
  }
}

/* Sidebar Footer */
.sidebar-footer {
  padding: clamp(16px, 4vw, 20px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  flex-shrink: 0;

  .footer-content {
    .stats-summary {
      background: rgba(255, 255, 255, 0.9);
      border-radius: clamp(8px, 2vw, 12px);
      padding: clamp(12px, 3vw, 16px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);

      .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: clamp(8px, 2vw, 12px);

        &:last-child {
          margin-bottom: 0;
        }

        .stat-number {
          font-size: clamp(16px, 4vw, 20px);
          font-weight: 700;
          color: #c8102e;
          line-height: 1;
        }

        .stat-label {
          font-size: clamp(9px, 2vw, 11px);
          color: #718096;
          font-weight: 500;
          text-align: center;
          margin-top: 2px;
        }
      }
    }
  }
}

/* Scrollbar Styling */
.sidebar-nav::-webkit-scrollbar {
  width: clamp(3px, 1vw, 4px);
}

.sidebar-nav::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-nav::-webkit-scrollbar-thumb {
  background: rgba(200, 16, 46, 0.3);
  border-radius: 2px;

  &:hover {
    background: rgba(200, 16, 46, 0.5);
  }
}

/* Animation Classes */
.menu-item {
  animation: slideInFromLeft 0.3s ease forwards;
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .sidebar-container {
    .sidebar-header {
      padding: 16px;
    }

    .sidebar-nav {
      padding: 16px 0;
    }

    .sidebar-footer {
      padding: 16px;
    }
  }
}

@media (max-width: 768px) {
  .sidebar-container {
    &.collapsed {
      width: 100%;
    }

    .menu-item {
      margin: 2px 8px;

      .item-content {
        padding: 14px 16px;
        min-height: 48px;
      }
    }

    .quick-actions {
      .action-btn {
        min-height: 48px;
        font-size: 14px;
      }
    }
  }
}

@media (max-width: 480px) {
  .sidebar-container {
    .sidebar-header {
      padding: 12px 16px;

      .logo-container {
        gap: 8px;

        .logo-icon {
          width: 36px;
          height: 36px;
        }

        .logo-text {
          h3 {
            font-size: 16px;
          }

          p {
            font-size: 10px;
          }
        }
      }
    }

    .menu-item {
      .item-content {
        padding: 12px 16px;
        min-height: 44px;
      }
    }
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .menu-item {
    &:hover {
      transform: none;
    }

    &:active {
      background: rgba(200, 16, 46, 0.15);
      transform: scale(0.98);
    }
  }

  .action-btn {
    &:hover {
      transform: none;
    }

    &:active {
      transform: scale(0.98);
    }
  }
}
