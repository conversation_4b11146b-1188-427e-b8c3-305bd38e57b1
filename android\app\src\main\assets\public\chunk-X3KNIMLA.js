import{b as re}from"./chunk-7HYD6CC7.js";import{c as ne,e as oe,f as ie,g as ae}from"./chunk-5VDVK6VQ.js";import{g as In,r as Dn}from"./chunk-I4XHNRTT.js";import{$a as ee,A as r,Ab as hn,B as qt,Bb as A,C,Cb as vn,D as I,E as _,Eb as B,F as l,G as Qt,H as K,I as x,J as V,K as Z,M as $t,N as Kt,O as Y,P as Yt,Q as w,R as z,S as s,T as c,U as H,V as U,W as M,X as S,Za as F,b as p,d as Ht,fa as T,h as X,ha as d,hb as nn,i as N,ia as Ut,ib as on,j as Gt,ka as G,l as j,la as J,ma as Jt,mb as an,n as k,nb as rn,o as q,ob as te,p as Q,pb as E,q as b,r as Wt,rb as cn,s as y,sb as ln,t as $,tb as sn,u as a,ua as en,ub as dn,vb as un,wa as tn,wb as pn,x as L,xb as mn,y as i,yb as fn,z as Xt,zb as gn}from"./chunk-XPS4RP6N.js";import{a as P,b as O,e as Zt}from"./chunk-JHI3MBHO.js";var jn=(t,v)=>Zt(null,null,function*(){if(!(typeof window>"u"))return yield In(),Dn(JSON.parse('[["ion-menu_3",[[33,"ion-menu-button",{"color":[513],"disabled":[4],"menu":[1],"autoHide":[4,"auto-hide"],"type":[1],"visible":[32]},[[16,"ionMenuChange","visibilityChanged"],[16,"ionSplitPaneVisible","visibilityChanged"]]],[33,"ion-menu",{"contentId":[513,"content-id"],"menuId":[513,"menu-id"],"type":[1025],"disabled":[1028],"side":[513],"swipeGesture":[4,"swipe-gesture"],"maxEdgeStart":[2,"max-edge-start"],"isPaneVisible":[32],"isEndSide":[32],"isOpen":[64],"isActive":[64],"open":[64],"close":[64],"toggle":[64],"setOpen":[64]},[[16,"ionSplitPaneVisible","onSplitPaneChanged"],[2,"click","onBackdropClick"]],{"type":["typeChanged"],"disabled":["disabledChanged"],"side":["sideChanged"],"swipeGesture":["swipeGestureChanged"]}],[1,"ion-menu-toggle",{"menu":[1],"autoHide":[4,"auto-hide"],"visible":[32]},[[16,"ionMenuChange","visibilityChanged"],[16,"ionSplitPaneVisible","visibilityChanged"]]]]],["ion-input-password-toggle",[[33,"ion-input-password-toggle",{"color":[513],"showIcon":[1,"show-icon"],"hideIcon":[1,"hide-icon"],"type":[1025]},null,{"type":["onTypeChange"]}]]],["ion-fab_3",[[33,"ion-fab-button",{"color":[513],"activated":[4],"disabled":[4],"download":[1],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"],"target":[1],"show":[4],"translucent":[4],"type":[1],"size":[1],"closeIcon":[1,"close-icon"]}],[1,"ion-fab",{"horizontal":[1],"vertical":[1],"edge":[4],"activated":[1028],"close":[64],"toggle":[64]},null,{"activated":["activatedChanged"]}],[1,"ion-fab-list",{"activated":[4],"side":[1]},null,{"activated":["activatedChanged"]}]]],["ion-refresher_2",[[0,"ion-refresher-content",{"pullingIcon":[1025,"pulling-icon"],"pullingText":[1,"pulling-text"],"refreshingSpinner":[1025,"refreshing-spinner"],"refreshingText":[1,"refreshing-text"]}],[32,"ion-refresher",{"pullMin":[2,"pull-min"],"pullMax":[2,"pull-max"],"closeDuration":[1,"close-duration"],"snapbackDuration":[1,"snapback-duration"],"pullFactor":[2,"pull-factor"],"disabled":[4],"nativeRefresher":[32],"state":[32],"complete":[64],"cancel":[64],"getProgress":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-back-button",[[33,"ion-back-button",{"color":[513],"defaultHref":[1025,"default-href"],"disabled":[516],"icon":[1],"text":[1],"type":[1],"routerAnimation":[16,"router-animation"]}]]],["ion-toast",[[33,"ion-toast",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"color":[513],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"cssClass":[1,"css-class"],"duration":[2],"header":[1],"layout":[1],"message":[1],"keyboardClose":[4,"keyboard-close"],"position":[1],"positionAnchor":[1,"position-anchor"],"buttons":[16],"translucent":[4],"animated":[4],"icon":[1],"htmlAttributes":[16,"html-attributes"],"swipeGesture":[1,"swipe-gesture"],"isOpen":[4,"is-open"],"trigger":[1],"revealContentToScreenReader":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"swipeGesture":["swipeGestureChanged"],"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-card_5",[[33,"ion-card",{"color":[513],"button":[4],"type":[1],"disabled":[4],"download":[1],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"],"target":[1]}],[32,"ion-card-content"],[33,"ion-card-header",{"color":[513],"translucent":[4]}],[33,"ion-card-subtitle",{"color":[513]}],[33,"ion-card-title",{"color":[513]}]]],["ion-item-option_3",[[33,"ion-item-option",{"color":[513],"disabled":[4],"download":[1],"expandable":[4],"href":[1],"rel":[1],"target":[1],"type":[1]}],[32,"ion-item-options",{"side":[1],"fireSwipeEvent":[64]}],[0,"ion-item-sliding",{"disabled":[4],"state":[32],"getOpenAmount":[64],"getSlidingRatio":[64],"open":[64],"close":[64],"closeOpened":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-accordion_2",[[49,"ion-accordion",{"value":[1],"disabled":[4],"readonly":[4],"toggleIcon":[1,"toggle-icon"],"toggleIconSlot":[1,"toggle-icon-slot"],"state":[32],"isNext":[32],"isPrevious":[32]},null,{"value":["valueChanged"]}],[33,"ion-accordion-group",{"animated":[4],"multiple":[4],"value":[1025],"disabled":[4],"readonly":[4],"expand":[1],"requestAccordionToggle":[64],"getAccordions":[64]},[[0,"keydown","onKeydown"]],{"value":["valueChanged"],"disabled":["disabledChanged"],"readonly":["readonlyChanged"]}]]],["ion-infinite-scroll_2",[[32,"ion-infinite-scroll-content",{"loadingSpinner":[1025,"loading-spinner"],"loadingText":[1,"loading-text"]}],[0,"ion-infinite-scroll",{"threshold":[1],"disabled":[4],"position":[1],"isLoading":[32],"complete":[64]},null,{"threshold":["thresholdChanged"],"disabled":["disabledChanged"]}]]],["ion-reorder_2",[[33,"ion-reorder",null,[[2,"click","onClick"]]],[0,"ion-reorder-group",{"disabled":[4],"state":[32],"complete":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-segment_2",[[33,"ion-segment-button",{"contentId":[513,"content-id"],"disabled":[1028],"layout":[1],"type":[1],"value":[8],"checked":[32],"setFocus":[64]},null,{"value":["valueChanged"]}],[33,"ion-segment",{"color":[513],"disabled":[4],"scrollable":[4],"swipeGesture":[4,"swipe-gesture"],"value":[1032],"selectOnFocus":[4,"select-on-focus"],"activated":[32]},[[16,"ionSegmentViewScroll","handleSegmentViewScroll"],[0,"keydown","onKeyDown"]],{"color":["colorChanged"],"swipeGesture":["swipeGestureChanged"],"value":["valueChanged"],"disabled":["disabledChanged"]}]]],["ion-chip",[[33,"ion-chip",{"color":[513],"outline":[4],"disabled":[4]}]]],["ion-input",[[38,"ion-input",{"color":[513],"autocapitalize":[1],"autocomplete":[1],"autocorrect":[1],"autofocus":[4],"clearInput":[4,"clear-input"],"clearInputIcon":[1,"clear-input-icon"],"clearOnEdit":[4,"clear-on-edit"],"counter":[4],"counterFormatter":[16,"counter-formatter"],"debounce":[2],"disabled":[516],"enterkeyhint":[1],"errorText":[1,"error-text"],"fill":[1],"inputmode":[1],"helperText":[1,"helper-text"],"label":[1],"labelPlacement":[1,"label-placement"],"max":[8],"maxlength":[2],"min":[8],"minlength":[2],"multiple":[4],"name":[1],"pattern":[1],"placeholder":[1],"readonly":[516],"required":[4],"shape":[1],"spellcheck":[4],"step":[1],"type":[1],"value":[1032],"hasFocus":[32],"setFocus":[64],"getInputElement":[64]},[[2,"click","onClickCapture"]],{"debounce":["debounceChanged"],"type":["onTypeChange"],"value":["valueChanged"],"dir":["onDirChanged"]}]]],["ion-searchbar",[[34,"ion-searchbar",{"color":[513],"animated":[4],"autocapitalize":[1],"autocomplete":[1],"autocorrect":[1],"cancelButtonIcon":[1,"cancel-button-icon"],"cancelButtonText":[1,"cancel-button-text"],"clearIcon":[1,"clear-icon"],"debounce":[2],"disabled":[4],"inputmode":[1],"enterkeyhint":[1],"maxlength":[2],"minlength":[2],"name":[1],"placeholder":[1],"searchIcon":[1,"search-icon"],"showCancelButton":[1,"show-cancel-button"],"showClearButton":[1,"show-clear-button"],"spellcheck":[4],"type":[1],"value":[1025],"focused":[32],"noAnimate":[32],"setFocus":[64],"getInputElement":[64]},null,{"lang":["onLangChanged"],"dir":["onDirChanged"],"debounce":["debounceChanged"],"value":["valueChanged"],"showCancelButton":["showCancelButtonChanged"]}]]],["ion-toggle",[[33,"ion-toggle",{"color":[513],"name":[1],"checked":[1028],"disabled":[4],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"value":[1],"enableOnOffLabels":[4,"enable-on-off-labels"],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"required":[4],"activated":[32]},null,{"disabled":["disabledChanged"]}]]],["ion-nav_2",[[1,"ion-nav",{"delegate":[16],"swipeGesture":[1028,"swipe-gesture"],"animated":[4],"animation":[16],"rootParams":[16,"root-params"],"root":[1],"push":[64],"insert":[64],"insertPages":[64],"pop":[64],"popTo":[64],"popToRoot":[64],"removeIndex":[64],"setRoot":[64],"setPages":[64],"setRouteId":[64],"getRouteId":[64],"getActive":[64],"getByIndex":[64],"canGoBack":[64],"getPrevious":[64],"getLength":[64]},null,{"swipeGesture":["swipeGestureChanged"],"root":["rootChanged"]}],[0,"ion-nav-link",{"component":[1],"componentProps":[16,"component-props"],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"]}]]],["ion-tab_2",[[1,"ion-tab",{"active":[1028],"delegate":[16],"tab":[1],"component":[1],"setActive":[64]},null,{"active":["changeActive"]}],[1,"ion-tabs",{"useRouter":[1028,"use-router"],"selectedTab":[32],"select":[64],"getTab":[64],"getSelected":[64],"setRouteId":[64],"getRouteId":[64]}]]],["ion-textarea",[[38,"ion-textarea",{"color":[513],"autocapitalize":[1],"autofocus":[4],"clearOnEdit":[4,"clear-on-edit"],"debounce":[2],"disabled":[4],"fill":[1],"inputmode":[1],"enterkeyhint":[1],"maxlength":[2],"minlength":[2],"name":[1],"placeholder":[1],"readonly":[4],"required":[4],"spellcheck":[4],"cols":[514],"rows":[2],"wrap":[1],"autoGrow":[516,"auto-grow"],"value":[1025],"counter":[4],"counterFormatter":[16,"counter-formatter"],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"label":[1],"labelPlacement":[1,"label-placement"],"shape":[1],"hasFocus":[32],"setFocus":[64],"getInputElement":[64]},[[2,"click","onClickCapture"]],{"debounce":["debounceChanged"],"value":["valueChanged"],"dir":["onDirChanged"]}]]],["ion-backdrop",[[33,"ion-backdrop",{"visible":[4],"tappable":[4],"stopPropagation":[4,"stop-propagation"]},[[2,"click","onMouseDown"]]]]],["ion-loading",[[34,"ion-loading",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"message":[1],"cssClass":[1,"css-class"],"duration":[2],"backdropDismiss":[4,"backdrop-dismiss"],"showBackdrop":[4,"show-backdrop"],"spinner":[1025],"translucent":[4],"animated":[4],"htmlAttributes":[16,"html-attributes"],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-breadcrumb_2",[[33,"ion-breadcrumb",{"collapsed":[4],"last":[4],"showCollapsedIndicator":[4,"show-collapsed-indicator"],"color":[1],"active":[4],"disabled":[4],"download":[1],"href":[1],"rel":[1],"separator":[4],"target":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"]}],[33,"ion-breadcrumbs",{"color":[513],"maxItems":[2,"max-items"],"itemsBeforeCollapse":[2,"items-before-collapse"],"itemsAfterCollapse":[2,"items-after-collapse"],"collapsed":[32],"activeChanged":[32]},[[0,"collapsedClick","onCollapsedClick"]],{"maxItems":["maxItemsChanged"],"itemsBeforeCollapse":["maxItemsChanged"],"itemsAfterCollapse":["maxItemsChanged"]}]]],["ion-tab-bar_2",[[33,"ion-tab-button",{"disabled":[4],"download":[1],"href":[1],"rel":[1],"layout":[1025],"selected":[1028],"tab":[1],"target":[1]},[[8,"ionTabBarChanged","onTabBarChanged"]]],[33,"ion-tab-bar",{"color":[513],"selectedTab":[1,"selected-tab"],"translucent":[4],"keyboardVisible":[32]},null,{"selectedTab":["selectedTabChanged"]}]]],["ion-datetime-button",[[33,"ion-datetime-button",{"color":[513],"disabled":[516],"datetime":[1],"datetimePresentation":[32],"dateText":[32],"timeText":[32],"datetimeActive":[32],"selectedButton":[32]}]]],["ion-route_4",[[0,"ion-route",{"url":[1],"component":[1],"componentProps":[16,"component-props"],"beforeLeave":[16,"before-leave"],"beforeEnter":[16,"before-enter"]},null,{"url":["onUpdate"],"component":["onUpdate"],"componentProps":["onComponentProps"]}],[0,"ion-route-redirect",{"from":[1],"to":[1]},null,{"from":["propDidChange"],"to":["propDidChange"]}],[0,"ion-router",{"root":[1],"useHash":[4,"use-hash"],"canTransition":[64],"push":[64],"back":[64],"printDebug":[64],"navChanged":[64]},[[8,"popstate","onPopState"],[4,"ionBackButton","onBackButton"]]],[1,"ion-router-link",{"color":[513],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"],"target":[1]}]]],["ion-avatar_3",[[33,"ion-avatar"],[33,"ion-badge",{"color":[513]}],[1,"ion-thumbnail"]]],["ion-col_3",[[1,"ion-col",{"offset":[1],"offsetXs":[1,"offset-xs"],"offsetSm":[1,"offset-sm"],"offsetMd":[1,"offset-md"],"offsetLg":[1,"offset-lg"],"offsetXl":[1,"offset-xl"],"pull":[1],"pullXs":[1,"pull-xs"],"pullSm":[1,"pull-sm"],"pullMd":[1,"pull-md"],"pullLg":[1,"pull-lg"],"pullXl":[1,"pull-xl"],"push":[1],"pushXs":[1,"push-xs"],"pushSm":[1,"push-sm"],"pushMd":[1,"push-md"],"pushLg":[1,"push-lg"],"pushXl":[1,"push-xl"],"size":[1],"sizeXs":[1,"size-xs"],"sizeSm":[1,"size-sm"],"sizeMd":[1,"size-md"],"sizeLg":[1,"size-lg"],"sizeXl":[1,"size-xl"]},[[9,"resize","onResize"]]],[1,"ion-grid",{"fixed":[4]}],[1,"ion-row"]]],["ion-img",[[1,"ion-img",{"alt":[1],"src":[1],"loadSrc":[32],"loadError":[32]},null,{"src":["srcChanged"]}]]],["ion-input-otp",[[38,"ion-input-otp",{"autocapitalize":[1],"color":[513],"disabled":[516],"fill":[1],"inputmode":[1],"length":[2],"pattern":[1],"readonly":[516],"separators":[1],"shape":[1],"size":[1],"type":[1],"value":[1032],"inputValues":[32],"hasFocus":[32],"setFocus":[64]},null,{"value":["valueChanged"],"separators":["processSeparators"],"length":["processSeparators"]}]]],["ion-progress-bar",[[33,"ion-progress-bar",{"type":[1],"reversed":[4],"value":[2],"buffer":[2],"color":[513]}]]],["ion-range",[[33,"ion-range",{"color":[513],"debounce":[2],"name":[1],"label":[1],"dualKnobs":[4,"dual-knobs"],"min":[2],"max":[2],"pin":[4],"pinFormatter":[16,"pin-formatter"],"snaps":[4],"step":[2],"ticks":[4],"activeBarStart":[1026,"active-bar-start"],"disabled":[4],"value":[1026],"labelPlacement":[1,"label-placement"],"ratioA":[32],"ratioB":[32],"pressedKnob":[32]},null,{"debounce":["debounceChanged"],"min":["minChanged"],"max":["maxChanged"],"step":["stepChanged"],"activeBarStart":["activeBarStartChanged"],"disabled":["disabledChanged"],"value":["valueChanged"]}]]],["ion-segment-content",[[1,"ion-segment-content"]]],["ion-segment-view",[[33,"ion-segment-view",{"disabled":[4],"isManualScroll":[32],"setContent":[64]},[[1,"scroll","handleScroll"],[1,"touchstart","handleScrollStart"],[1,"touchend","handleTouchEnd"]]]]],["ion-split-pane",[[33,"ion-split-pane",{"contentId":[513,"content-id"],"disabled":[4],"when":[8],"visible":[32],"isVisible":[64]},null,{"visible":["visibleChanged"],"disabled":["updateState"],"when":["updateState"]}]]],["ion-text",[[1,"ion-text",{"color":[513]}]]],["ion-select-modal",[[34,"ion-select-modal",{"header":[1],"multiple":[4],"options":[16]}]]],["ion-datetime_3",[[33,"ion-datetime",{"color":[1],"name":[1],"disabled":[4],"formatOptions":[16,"format-options"],"readonly":[4],"isDateEnabled":[16,"is-date-enabled"],"showAdjacentDays":[4,"show-adjacent-days"],"min":[1025],"max":[1025],"presentation":[1],"cancelText":[1,"cancel-text"],"doneText":[1,"done-text"],"clearText":[1,"clear-text"],"yearValues":[8,"year-values"],"monthValues":[8,"month-values"],"dayValues":[8,"day-values"],"hourValues":[8,"hour-values"],"minuteValues":[8,"minute-values"],"locale":[1],"firstDayOfWeek":[2,"first-day-of-week"],"titleSelectedDatesFormatter":[16,"title-selected-dates-formatter"],"multiple":[4],"highlightedDates":[16,"highlighted-dates"],"value":[1025],"showDefaultTitle":[4,"show-default-title"],"showDefaultButtons":[4,"show-default-buttons"],"showClearButton":[4,"show-clear-button"],"showDefaultTimeLabel":[4,"show-default-time-label"],"hourCycle":[1,"hour-cycle"],"size":[1],"preferWheel":[4,"prefer-wheel"],"showMonthAndYear":[32],"activeParts":[32],"workingParts":[32],"isTimePopoverOpen":[32],"forceRenderDate":[32],"confirm":[64],"reset":[64],"cancel":[64]},null,{"formatOptions":["formatOptionsChanged"],"disabled":["disabledChanged"],"min":["minChanged"],"max":["maxChanged"],"presentation":["presentationChanged"],"yearValues":["yearValuesChanged"],"monthValues":["monthValuesChanged"],"dayValues":["dayValuesChanged"],"hourValues":["hourValuesChanged"],"minuteValues":["minuteValuesChanged"],"value":["valueChanged"]}],[34,"ion-picker-legacy",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"buttons":[16],"columns":[16],"cssClass":[1,"css-class"],"duration":[2],"showBackdrop":[4,"show-backdrop"],"backdropDismiss":[4,"backdrop-dismiss"],"animated":[4],"htmlAttributes":[16,"html-attributes"],"isOpen":[4,"is-open"],"trigger":[1],"presented":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64],"getColumn":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}],[32,"ion-picker-legacy-column",{"col":[16]},null,{"col":["colChanged"]}]]],["ion-action-sheet",[[34,"ion-action-sheet",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"buttons":[16],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"header":[1],"subHeader":[1,"sub-header"],"translucent":[4],"animated":[4],"htmlAttributes":[16,"html-attributes"],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-alert",[[34,"ion-alert",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"cssClass":[1,"css-class"],"header":[1],"subHeader":[1,"sub-header"],"message":[1],"buttons":[16],"inputs":[1040],"backdropDismiss":[4,"backdrop-dismiss"],"translucent":[4],"animated":[4],"htmlAttributes":[16,"html-attributes"],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},[[4,"keydown","onKeydown"]],{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"],"buttons":["buttonsChanged"],"inputs":["inputsChanged"]}]]],["ion-modal",[[33,"ion-modal",{"hasController":[4,"has-controller"],"overlayIndex":[2,"overlay-index"],"delegate":[16],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"breakpoints":[16],"expandToScroll":[4,"expand-to-scroll"],"initialBreakpoint":[2,"initial-breakpoint"],"backdropBreakpoint":[2,"backdrop-breakpoint"],"handle":[4],"handleBehavior":[1,"handle-behavior"],"component":[1],"componentProps":[16,"component-props"],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"showBackdrop":[4,"show-backdrop"],"animated":[4],"presentingElement":[16,"presenting-element"],"htmlAttributes":[16,"html-attributes"],"isOpen":[4,"is-open"],"trigger":[1],"keepContentsMounted":[4,"keep-contents-mounted"],"focusTrap":[4,"focus-trap"],"canDismiss":[4,"can-dismiss"],"presented":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64],"setCurrentBreakpoint":[64],"getCurrentBreakpoint":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-picker",[[33,"ion-picker",{"exitInputMode":[64]},[[1,"touchstart","preventTouchStartPropagation"]]]]],["ion-picker-column",[[1,"ion-picker-column",{"disabled":[4],"value":[1032],"color":[513],"numericInput":[4,"numeric-input"],"ariaLabel":[32],"isActive":[32],"scrollActiveItemIntoView":[64],"setValue":[64],"setFocus":[64]},null,{"aria-label":["ariaLabelChanged"],"value":["valueChange"]}]]],["ion-picker-column-option",[[33,"ion-picker-column-option",{"disabled":[4],"value":[8],"color":[513],"ariaLabel":[32]},null,{"aria-label":["onAriaLabelChange"]}]]],["ion-popover",[[33,"ion-popover",{"hasController":[4,"has-controller"],"delegate":[16],"overlayIndex":[2,"overlay-index"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"component":[1],"componentProps":[16,"component-props"],"keyboardClose":[4,"keyboard-close"],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"event":[8],"showBackdrop":[4,"show-backdrop"],"translucent":[4],"animated":[4],"htmlAttributes":[16,"html-attributes"],"triggerAction":[1,"trigger-action"],"trigger":[1],"size":[1],"dismissOnSelect":[4,"dismiss-on-select"],"reference":[1],"side":[1],"alignment":[1025],"arrow":[4],"isOpen":[4,"is-open"],"keyboardEvents":[4,"keyboard-events"],"focusTrap":[4,"focus-trap"],"keepContentsMounted":[4,"keep-contents-mounted"],"presented":[32],"presentFromTrigger":[64],"present":[64],"dismiss":[64],"getParentPopover":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"trigger":["onTriggerChange"],"triggerAction":["onTriggerChange"],"isOpen":["onIsOpenChange"]}]]],["ion-checkbox",[[33,"ion-checkbox",{"color":[513],"name":[1],"checked":[1028],"indeterminate":[1028],"disabled":[4],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"value":[8],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"required":[4],"setFocus":[64]}]]],["ion-item_8",[[33,"ion-item-divider",{"color":[513],"sticky":[4]}],[32,"ion-item-group"],[33,"ion-note",{"color":[513]}],[1,"ion-skeleton-text",{"animated":[4]}],[38,"ion-label",{"color":[513],"position":[1],"noAnimate":[32]},null,{"color":["colorChanged"],"position":["positionChanged"]}],[33,"ion-list-header",{"color":[513],"lines":[1]}],[33,"ion-item",{"color":[513],"button":[4],"detail":[4],"detailIcon":[1,"detail-icon"],"disabled":[516],"download":[1],"href":[1],"rel":[1],"lines":[1],"routerAnimation":[16,"router-animation"],"routerDirection":[1,"router-direction"],"target":[1],"type":[1],"multipleInputs":[32],"focusable":[32]},[[0,"ionColor","labelColorChanged"],[0,"ionStyle","itemStyle"]],{"button":["buttonChanged"]}],[32,"ion-list",{"lines":[1],"inset":[4],"closeSlidingItems":[64]}]]],["ion-app_8",[[0,"ion-app",{"setFocus":[64]}],[36,"ion-footer",{"collapse":[1],"translucent":[4],"keyboardVisible":[32]}],[1,"ion-router-outlet",{"mode":[1025],"delegate":[16],"animated":[4],"animation":[16],"swipeHandler":[16,"swipe-handler"],"commit":[64],"setRouteId":[64],"getRouteId":[64]},null,{"swipeHandler":["swipeHandlerChanged"]}],[1,"ion-content",{"color":[513],"fullscreen":[4],"fixedSlotPlacement":[1,"fixed-slot-placement"],"forceOverscroll":[1028,"force-overscroll"],"scrollX":[4,"scroll-x"],"scrollY":[4,"scroll-y"],"scrollEvents":[4,"scroll-events"],"getScrollElement":[64],"getBackgroundElement":[64],"scrollToTop":[64],"scrollToBottom":[64],"scrollByPoint":[64],"scrollToPoint":[64]},[[9,"resize","onResize"]]],[36,"ion-header",{"collapse":[1],"translucent":[4]}],[33,"ion-title",{"color":[513],"size":[1]},null,{"size":["sizeChanged"]}],[33,"ion-toolbar",{"color":[513]},[[0,"ionStyle","childrenStyle"]]],[38,"ion-buttons",{"collapse":[4]}]]],["ion-select_3",[[33,"ion-select",{"cancelText":[1,"cancel-text"],"color":[513],"compareWith":[1,"compare-with"],"disabled":[4],"fill":[1],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"interface":[1],"interfaceOptions":[8,"interface-options"],"justify":[1],"label":[1],"labelPlacement":[1,"label-placement"],"multiple":[4],"name":[1],"okText":[1,"ok-text"],"placeholder":[1],"selectedText":[1,"selected-text"],"toggleIcon":[1,"toggle-icon"],"expandedIcon":[1,"expanded-icon"],"shape":[1],"value":[1032],"required":[4],"isExpanded":[32],"hasFocus":[32],"open":[64]},null,{"disabled":["styleChanged"],"isExpanded":["styleChanged"],"placeholder":["styleChanged"],"value":["styleChanged"]}],[1,"ion-select-option",{"disabled":[4],"value":[8]}],[34,"ion-select-popover",{"header":[1],"subHeader":[1,"sub-header"],"message":[1],"multiple":[4],"options":[16]}]]],["ion-spinner",[[1,"ion-spinner",{"color":[513],"duration":[2],"name":[1],"paused":[4]}]]],["ion-radio_2",[[33,"ion-radio",{"color":[513],"name":[1],"disabled":[4],"value":[8],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"checked":[32],"buttonTabindex":[32],"setFocus":[64],"setButtonTabindex":[64]},null,{"value":["valueChanged"]}],[36,"ion-radio-group",{"allowEmptySelection":[4,"allow-empty-selection"],"compareWith":[1,"compare-with"],"name":[1],"value":[1032],"helperText":[1,"helper-text"],"errorText":[1,"error-text"],"setFocus":[64]},[[4,"keydown","onKeydown"]],{"value":["valueChanged"]}]]],["ion-ripple-effect",[[1,"ion-ripple-effect",{"type":[1],"addRipple":[64]}]]],["ion-button_2",[[33,"ion-button",{"color":[513],"buttonType":[1025,"button-type"],"disabled":[516],"expand":[513],"fill":[1537],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"],"download":[1],"href":[1],"rel":[1],"shape":[513],"size":[513],"strong":[4],"target":[1],"type":[1],"form":[1],"isCircle":[32]},null,{"disabled":["disabledChanged"],"aria-checked":["onAriaChanged"],"aria-label":["onAriaChanged"]}],[1,"ion-icon",{"mode":[1025],"color":[1],"ios":[1],"md":[1],"flipRtl":[4,"flip-rtl"],"name":[513],"src":[1],"icon":[8],"size":[1],"lazy":[4],"sanitize":[4],"svgContent":[32],"isVisible":[32]},null,{"name":["loadIcon"],"src":["loadIcon"],"icon":["loadIcon"],"ios":["loadIcon"],"md":["loadIcon"]}]]]]'),v)});(function(){if(typeof window<"u"&&window.Reflect!==void 0&&window.customElements!==void 0){var t=HTMLElement;window.HTMLElement=function(){return Reflect.construct(t,[],this.constructor)},HTMLElement.prototype=t.prototype,HTMLElement.prototype.constructor=HTMLElement,Object.setPrototypeOf(HTMLElement,t)}})();var u=["*"],Nn=["outletContent"],Ln=["outlet"],_n=[[["","slot","top"]],"*",[["ion-tab"]]],Vn=["[slot=top]","*","ion-tab"];function Zn(t,v){if(t&1){let n=Yt();V(0,"ion-router-outlet",5,1),w("stackWillChange",function(o){q(n);let f=z();return Q(f.onStackWillChange(o))})("stackDidChange",function(o){q(n);let f=z();return Q(f.onStackDidChange(o))}),Z()}}function Hn(t,v){t&1&&c(0,2,["*ngIf","tabs.length > 0"])}function Gn(t,v){if(t&1&&(V(0,"div",1),Y(1,2),Z()),t&2){let n=z();L(),x("ngTemplateOutlet",n.template)}}function Wn(t,v){if(t&1&&Y(0,1),t&2){let n=z();x("ngTemplateOutlet",n.template)}}var Xn=(()=>{class t extends A{constructor(n,e){super(n,e)}writeValue(n){this.elementRef.nativeElement.checked=this.lastValue=n,vn(this.elementRef)}_handleIonChange(n){this.handleValueChange(n,n.checked)}static \u0275fac=function(e){return new(e||t)(i(b),i(a))};static \u0275dir=C({type:t,selectors:[["ion-checkbox"],["ion-toggle"]],hostBindings:function(e,o){e&1&&w("ionChange",function(h){return o._handleIonChange(h.target)})},standalone:!1,features:[T([{provide:F,useExisting:t,multi:!0}]),I]})}return t})(),qn=(()=>{class t extends A{el;constructor(n,e){super(n,e),this.el=e}handleInputEvent(n){this.handleValueChange(n,n.value)}registerOnChange(n){this.el.nativeElement.tagName==="ION-INPUT"||this.el.nativeElement.tagName==="ION-INPUT-OTP"?super.registerOnChange(e=>{n(e===""?null:parseFloat(e))}):super.registerOnChange(n)}static \u0275fac=function(e){return new(e||t)(i(b),i(a))};static \u0275dir=C({type:t,selectors:[["ion-input","type","number"],["ion-input-otp",3,"type","text"],["ion-range"]],hostBindings:function(e,o){e&1&&w("ionInput",function(h){return o.handleInputEvent(h.target)})},standalone:!1,features:[T([{provide:F,useExisting:t,multi:!0}]),I]})}return t})(),Qn=(()=>{class t extends A{constructor(n,e){super(n,e)}_handleChangeEvent(n){this.handleValueChange(n,n.value)}static \u0275fac=function(e){return new(e||t)(i(b),i(a))};static \u0275dir=C({type:t,selectors:[["ion-select"],["ion-radio-group"],["ion-segment"],["ion-datetime"]],hostBindings:function(e,o){e&1&&w("ionChange",function(h){return o._handleChangeEvent(h.target)})},standalone:!1,features:[T([{provide:F,useExisting:t,multi:!0}]),I]})}return t})(),$n=(()=>{class t extends A{constructor(n,e){super(n,e)}_handleInputEvent(n){this.handleValueChange(n,n.value)}static \u0275fac=function(e){return new(e||t)(i(b),i(a))};static \u0275dir=C({type:t,selectors:[["ion-input",3,"type","number"],["ion-input-otp","type","text"],["ion-textarea"],["ion-searchbar"]],hostBindings:function(e,o){e&1&&w("ionInput",function(h){return o._handleInputEvent(h.target)})},standalone:!1,features:[T([{provide:F,useExisting:t,multi:!0}]),I]})}return t})(),Kn=(t,v)=>{let n=t.prototype;v.forEach(e=>{Object.defineProperty(n,e,{get(){return this.el[e]},set(o){this.z.runOutsideAngular(()=>this.el[e]=o)},configurable:!0})})},Yn=(t,v)=>{let n=t.prototype;v.forEach(e=>{n[e]=function(){let o=arguments;return this.z.runOutsideAngular(()=>this.el[e].apply(this.el,o))}})},g=(t,v,n)=>{n.forEach(e=>t[e]=Ht(v,e))};function m(t){return function(n){let{defineCustomElementFn:e,inputs:o,methods:f}=t;return e!==void 0&&e(),o&&Kn(n,o),f&&Yn(n,f),n}}var Un=(()=>{let t=class ce{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||ce)(i(d),i(a),i(l))};static \u0275cmp=r({type:ce,selectors:[["ion-accordion"]],inputs:{disabled:"disabled",mode:"mode",readonly:"readonly",toggleIcon:"toggleIcon",toggleIconSlot:"toggleIconSlot",value:"value"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["disabled","mode","readonly","toggleIcon","toggleIconSlot","value"]})],t),t})(),Jn=(()=>{let t=class le{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement,g(this,this.el,["ionChange"])}static \u0275fac=function(e){return new(e||le)(i(d),i(a),i(l))};static \u0275cmp=r({type:le,selectors:[["ion-accordion-group"]],inputs:{animated:"animated",disabled:"disabled",expand:"expand",mode:"mode",multiple:"multiple",readonly:"readonly",value:"value"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["animated","disabled","expand","mode","multiple","readonly","value"]})],t),t})(),eo=(()=>{let t=class se{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement,g(this,this.el,["ionActionSheetDidPresent","ionActionSheetWillPresent","ionActionSheetWillDismiss","ionActionSheetDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(e){return new(e||se)(i(d),i(a),i(l))};static \u0275cmp=r({type:se,selectors:[["ion-action-sheet"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",cssClass:"cssClass",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",subHeader:"subHeader",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["animated","backdropDismiss","buttons","cssClass","enterAnimation","header","htmlAttributes","isOpen","keyboardClose","leaveAnimation","mode","subHeader","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],t),t})(),to=(()=>{let t=class de{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement,g(this,this.el,["ionAlertDidPresent","ionAlertWillPresent","ionAlertWillDismiss","ionAlertDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(e){return new(e||de)(i(d),i(a),i(l))};static \u0275cmp=r({type:de,selectors:[["ion-alert"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",cssClass:"cssClass",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",inputs:"inputs",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",subHeader:"subHeader",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["animated","backdropDismiss","buttons","cssClass","enterAnimation","header","htmlAttributes","inputs","isOpen","keyboardClose","leaveAnimation","message","mode","subHeader","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],t),t})(),no=(()=>{let t=class ue{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||ue)(i(d),i(a),i(l))};static \u0275cmp=r({type:ue,selectors:[["ion-app"]],standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({methods:["setFocus"]})],t),t})(),oo=(()=>{let t=class pe{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||pe)(i(d),i(a),i(l))};static \u0275cmp=r({type:pe,selectors:[["ion-avatar"]],standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({})],t),t})(),io=(()=>{let t=class me{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement,g(this,this.el,["ionBackdropTap"])}static \u0275fac=function(e){return new(e||me)(i(d),i(a),i(l))};static \u0275cmp=r({type:me,selectors:[["ion-backdrop"]],inputs:{stopPropagation:"stopPropagation",tappable:"tappable",visible:"visible"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["stopPropagation","tappable","visible"]})],t),t})(),ao=(()=>{let t=class fe{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||fe)(i(d),i(a),i(l))};static \u0275cmp=r({type:fe,selectors:[["ion-badge"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["color","mode"]})],t),t})(),ro=(()=>{let t=class ge{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement,g(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(e){return new(e||ge)(i(d),i(a),i(l))};static \u0275cmp=r({type:ge,selectors:[["ion-breadcrumb"]],inputs:{active:"active",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",separator:"separator",target:"target"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["active","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","separator","target"]})],t),t})(),co=(()=>{let t=class he{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement,g(this,this.el,["ionCollapsedClick"])}static \u0275fac=function(e){return new(e||he)(i(d),i(a),i(l))};static \u0275cmp=r({type:he,selectors:[["ion-breadcrumbs"]],inputs:{color:"color",itemsAfterCollapse:"itemsAfterCollapse",itemsBeforeCollapse:"itemsBeforeCollapse",maxItems:"maxItems",mode:"mode"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["color","itemsAfterCollapse","itemsBeforeCollapse","maxItems","mode"]})],t),t})(),lo=(()=>{let t=class ve{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement,g(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(e){return new(e||ve)(i(d),i(a),i(l))};static \u0275cmp=r({type:ve,selectors:[["ion-button"]],inputs:{buttonType:"buttonType",color:"color",disabled:"disabled",download:"download",expand:"expand",fill:"fill",form:"form",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",shape:"shape",size:"size",strong:"strong",target:"target",type:"type"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["buttonType","color","disabled","download","expand","fill","form","href","mode","rel","routerAnimation","routerDirection","shape","size","strong","target","type"]})],t),t})(),so=(()=>{let t=class Ie{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Ie)(i(d),i(a),i(l))};static \u0275cmp=r({type:Ie,selectors:[["ion-buttons"]],inputs:{collapse:"collapse"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["collapse"]})],t),t})(),uo=(()=>{let t=class De{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||De)(i(d),i(a),i(l))};static \u0275cmp=r({type:De,selectors:[["ion-card"]],inputs:{button:"button",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",target:"target",type:"type"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["button","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","target","type"]})],t),t})(),po=(()=>{let t=class be{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||be)(i(d),i(a),i(l))};static \u0275cmp=r({type:be,selectors:[["ion-card-content"]],inputs:{mode:"mode"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["mode"]})],t),t})(),mo=(()=>{let t=class Ce{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Ce)(i(d),i(a),i(l))};static \u0275cmp=r({type:Ce,selectors:[["ion-card-header"]],inputs:{color:"color",mode:"mode",translucent:"translucent"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["color","mode","translucent"]})],t),t})(),fo=(()=>{let t=class ye{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||ye)(i(d),i(a),i(l))};static \u0275cmp=r({type:ye,selectors:[["ion-card-subtitle"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["color","mode"]})],t),t})(),go=(()=>{let t=class je{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||je)(i(d),i(a),i(l))};static \u0275cmp=r({type:je,selectors:[["ion-card-title"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["color","mode"]})],t),t})(),ho=(()=>{let t=class xe{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement,g(this,this.el,["ionChange","ionFocus","ionBlur"])}static \u0275fac=function(e){return new(e||xe)(i(d),i(a),i(l))};static \u0275cmp=r({type:xe,selectors:[["ion-checkbox"]],inputs:{alignment:"alignment",checked:"checked",color:"color",disabled:"disabled",errorText:"errorText",helperText:"helperText",indeterminate:"indeterminate",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",required:"required",value:"value"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["alignment","checked","color","disabled","errorText","helperText","indeterminate","justify","labelPlacement","mode","name","required","value"]})],t),t})(),vo=(()=>{let t=class Te{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Te)(i(d),i(a),i(l))};static \u0275cmp=r({type:Te,selectors:[["ion-chip"]],inputs:{color:"color",disabled:"disabled",mode:"mode",outline:"outline"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["color","disabled","mode","outline"]})],t),t})(),Io=(()=>{let t=class we{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||we)(i(d),i(a),i(l))};static \u0275cmp=r({type:we,selectors:[["ion-col"]],inputs:{offset:"offset",offsetLg:"offsetLg",offsetMd:"offsetMd",offsetSm:"offsetSm",offsetXl:"offsetXl",offsetXs:"offsetXs",pull:"pull",pullLg:"pullLg",pullMd:"pullMd",pullSm:"pullSm",pullXl:"pullXl",pullXs:"pullXs",push:"push",pushLg:"pushLg",pushMd:"pushMd",pushSm:"pushSm",pushXl:"pushXl",pushXs:"pushXs",size:"size",sizeLg:"sizeLg",sizeMd:"sizeMd",sizeSm:"sizeSm",sizeXl:"sizeXl",sizeXs:"sizeXs"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["offset","offsetLg","offsetMd","offsetSm","offsetXl","offsetXs","pull","pullLg","pullMd","pullSm","pullXl","pullXs","push","pushLg","pushMd","pushSm","pushXl","pushXs","size","sizeLg","sizeMd","sizeSm","sizeXl","sizeXs"]})],t),t})(),Do=(()=>{let t=class Me{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement,g(this,this.el,["ionScrollStart","ionScroll","ionScrollEnd"])}static \u0275fac=function(e){return new(e||Me)(i(d),i(a),i(l))};static \u0275cmp=r({type:Me,selectors:[["ion-content"]],inputs:{color:"color",fixedSlotPlacement:"fixedSlotPlacement",forceOverscroll:"forceOverscroll",fullscreen:"fullscreen",scrollEvents:"scrollEvents",scrollX:"scrollX",scrollY:"scrollY"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["color","fixedSlotPlacement","forceOverscroll","fullscreen","scrollEvents","scrollX","scrollY"],methods:["getScrollElement","scrollToTop","scrollToBottom","scrollByPoint","scrollToPoint"]})],t),t})(),bo=(()=>{let t=class Se{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement,g(this,this.el,["ionCancel","ionChange","ionFocus","ionBlur"])}static \u0275fac=function(e){return new(e||Se)(i(d),i(a),i(l))};static \u0275cmp=r({type:Se,selectors:[["ion-datetime"]],inputs:{cancelText:"cancelText",clearText:"clearText",color:"color",dayValues:"dayValues",disabled:"disabled",doneText:"doneText",firstDayOfWeek:"firstDayOfWeek",formatOptions:"formatOptions",highlightedDates:"highlightedDates",hourCycle:"hourCycle",hourValues:"hourValues",isDateEnabled:"isDateEnabled",locale:"locale",max:"max",min:"min",minuteValues:"minuteValues",mode:"mode",monthValues:"monthValues",multiple:"multiple",name:"name",preferWheel:"preferWheel",presentation:"presentation",readonly:"readonly",showAdjacentDays:"showAdjacentDays",showClearButton:"showClearButton",showDefaultButtons:"showDefaultButtons",showDefaultTimeLabel:"showDefaultTimeLabel",showDefaultTitle:"showDefaultTitle",size:"size",titleSelectedDatesFormatter:"titleSelectedDatesFormatter",value:"value",yearValues:"yearValues"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["cancelText","clearText","color","dayValues","disabled","doneText","firstDayOfWeek","formatOptions","highlightedDates","hourCycle","hourValues","isDateEnabled","locale","max","min","minuteValues","mode","monthValues","multiple","name","preferWheel","presentation","readonly","showAdjacentDays","showClearButton","showDefaultButtons","showDefaultTimeLabel","showDefaultTitle","size","titleSelectedDatesFormatter","value","yearValues"],methods:["confirm","reset","cancel"]})],t),t})(),Co=(()=>{let t=class Ee{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Ee)(i(d),i(a),i(l))};static \u0275cmp=r({type:Ee,selectors:[["ion-datetime-button"]],inputs:{color:"color",datetime:"datetime",disabled:"disabled",mode:"mode"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["color","datetime","disabled","mode"]})],t),t})(),yo=(()=>{let t=class Re{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Re)(i(d),i(a),i(l))};static \u0275cmp=r({type:Re,selectors:[["ion-fab"]],inputs:{activated:"activated",edge:"edge",horizontal:"horizontal",vertical:"vertical"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["activated","edge","horizontal","vertical"],methods:["close"]})],t),t})(),jo=(()=>{let t=class ke{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement,g(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(e){return new(e||ke)(i(d),i(a),i(l))};static \u0275cmp=r({type:ke,selectors:[["ion-fab-button"]],inputs:{activated:"activated",closeIcon:"closeIcon",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",show:"show",size:"size",target:"target",translucent:"translucent",type:"type"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["activated","closeIcon","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","show","size","target","translucent","type"]})],t),t})(),xo=(()=>{let t=class ze{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||ze)(i(d),i(a),i(l))};static \u0275cmp=r({type:ze,selectors:[["ion-fab-list"]],inputs:{activated:"activated",side:"side"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["activated","side"]})],t),t})(),To=(()=>{let t=class Fe{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Fe)(i(d),i(a),i(l))};static \u0275cmp=r({type:Fe,selectors:[["ion-footer"]],inputs:{collapse:"collapse",mode:"mode",translucent:"translucent"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["collapse","mode","translucent"]})],t),t})(),wo=(()=>{let t=class Ae{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Ae)(i(d),i(a),i(l))};static \u0275cmp=r({type:Ae,selectors:[["ion-grid"]],inputs:{fixed:"fixed"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["fixed"]})],t),t})(),Mo=(()=>{let t=class Be{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Be)(i(d),i(a),i(l))};static \u0275cmp=r({type:Be,selectors:[["ion-header"]],inputs:{collapse:"collapse",mode:"mode",translucent:"translucent"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["collapse","mode","translucent"]})],t),t})(),So=(()=>{let t=class Pe{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Pe)(i(d),i(a),i(l))};static \u0275cmp=r({type:Pe,selectors:[["ion-icon"]],inputs:{color:"color",flipRtl:"flipRtl",icon:"icon",ios:"ios",lazy:"lazy",md:"md",mode:"mode",name:"name",sanitize:"sanitize",size:"size",src:"src"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["color","flipRtl","icon","ios","lazy","md","mode","name","sanitize","size","src"]})],t),t})(),Eo=(()=>{let t=class Oe{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement,g(this,this.el,["ionImgWillLoad","ionImgDidLoad","ionError"])}static \u0275fac=function(e){return new(e||Oe)(i(d),i(a),i(l))};static \u0275cmp=r({type:Oe,selectors:[["ion-img"]],inputs:{alt:"alt",src:"src"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["alt","src"]})],t),t})(),Ro=(()=>{let t=class Ne{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement,g(this,this.el,["ionInfinite"])}static \u0275fac=function(e){return new(e||Ne)(i(d),i(a),i(l))};static \u0275cmp=r({type:Ne,selectors:[["ion-infinite-scroll"]],inputs:{disabled:"disabled",position:"position",threshold:"threshold"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["disabled","position","threshold"],methods:["complete"]})],t),t})(),ko=(()=>{let t=class Le{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Le)(i(d),i(a),i(l))};static \u0275cmp=r({type:Le,selectors:[["ion-infinite-scroll-content"]],inputs:{loadingSpinner:"loadingSpinner",loadingText:"loadingText"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["loadingSpinner","loadingText"]})],t),t})(),zo=(()=>{let t=class _e{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement,g(this,this.el,["ionInput","ionChange","ionBlur","ionFocus"])}static \u0275fac=function(e){return new(e||_e)(i(d),i(a),i(l))};static \u0275cmp=r({type:_e,selectors:[["ion-input"]],inputs:{autocapitalize:"autocapitalize",autocomplete:"autocomplete",autocorrect:"autocorrect",autofocus:"autofocus",clearInput:"clearInput",clearInputIcon:"clearInputIcon",clearOnEdit:"clearOnEdit",color:"color",counter:"counter",counterFormatter:"counterFormatter",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",errorText:"errorText",fill:"fill",helperText:"helperText",inputmode:"inputmode",label:"label",labelPlacement:"labelPlacement",max:"max",maxlength:"maxlength",min:"min",minlength:"minlength",mode:"mode",multiple:"multiple",name:"name",pattern:"pattern",placeholder:"placeholder",readonly:"readonly",required:"required",shape:"shape",spellcheck:"spellcheck",step:"step",type:"type",value:"value"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["autocapitalize","autocomplete","autocorrect","autofocus","clearInput","clearInputIcon","clearOnEdit","color","counter","counterFormatter","debounce","disabled","enterkeyhint","errorText","fill","helperText","inputmode","label","labelPlacement","max","maxlength","min","minlength","mode","multiple","name","pattern","placeholder","readonly","required","shape","spellcheck","step","type","value"],methods:["setFocus","getInputElement"]})],t),t})(),Fo=(()=>{let t=class Ve{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement,g(this,this.el,["ionInput","ionChange","ionComplete","ionBlur","ionFocus"])}static \u0275fac=function(e){return new(e||Ve)(i(d),i(a),i(l))};static \u0275cmp=r({type:Ve,selectors:[["ion-input-otp"]],inputs:{autocapitalize:"autocapitalize",color:"color",disabled:"disabled",fill:"fill",inputmode:"inputmode",length:"length",pattern:"pattern",readonly:"readonly",separators:"separators",shape:"shape",size:"size",type:"type",value:"value"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["autocapitalize","color","disabled","fill","inputmode","length","pattern","readonly","separators","shape","size","type","value"],methods:["setFocus"]})],t),t})(),Ao=(()=>{let t=class Ze{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Ze)(i(d),i(a),i(l))};static \u0275cmp=r({type:Ze,selectors:[["ion-input-password-toggle"]],inputs:{color:"color",hideIcon:"hideIcon",mode:"mode",showIcon:"showIcon"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["color","hideIcon","mode","showIcon"]})],t),t})(),Bo=(()=>{let t=class He{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||He)(i(d),i(a),i(l))};static \u0275cmp=r({type:He,selectors:[["ion-item"]],inputs:{button:"button",color:"color",detail:"detail",detailIcon:"detailIcon",disabled:"disabled",download:"download",href:"href",lines:"lines",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",target:"target",type:"type"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["button","color","detail","detailIcon","disabled","download","href","lines","mode","rel","routerAnimation","routerDirection","target","type"]})],t),t})(),Po=(()=>{let t=class Ge{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Ge)(i(d),i(a),i(l))};static \u0275cmp=r({type:Ge,selectors:[["ion-item-divider"]],inputs:{color:"color",mode:"mode",sticky:"sticky"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["color","mode","sticky"]})],t),t})(),Oo=(()=>{let t=class We{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||We)(i(d),i(a),i(l))};static \u0275cmp=r({type:We,selectors:[["ion-item-group"]],standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({})],t),t})(),No=(()=>{let t=class Xe{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Xe)(i(d),i(a),i(l))};static \u0275cmp=r({type:Xe,selectors:[["ion-item-option"]],inputs:{color:"color",disabled:"disabled",download:"download",expandable:"expandable",href:"href",mode:"mode",rel:"rel",target:"target",type:"type"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["color","disabled","download","expandable","href","mode","rel","target","type"]})],t),t})(),Lo=(()=>{let t=class qe{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement,g(this,this.el,["ionSwipe"])}static \u0275fac=function(e){return new(e||qe)(i(d),i(a),i(l))};static \u0275cmp=r({type:qe,selectors:[["ion-item-options"]],inputs:{side:"side"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["side"]})],t),t})(),_o=(()=>{let t=class Qe{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement,g(this,this.el,["ionDrag"])}static \u0275fac=function(e){return new(e||Qe)(i(d),i(a),i(l))};static \u0275cmp=r({type:Qe,selectors:[["ion-item-sliding"]],inputs:{disabled:"disabled"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["disabled"],methods:["getOpenAmount","getSlidingRatio","open","close","closeOpened"]})],t),t})(),Vo=(()=>{let t=class $e{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||$e)(i(d),i(a),i(l))};static \u0275cmp=r({type:$e,selectors:[["ion-label"]],inputs:{color:"color",mode:"mode",position:"position"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["color","mode","position"]})],t),t})(),Zo=(()=>{let t=class Ke{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Ke)(i(d),i(a),i(l))};static \u0275cmp=r({type:Ke,selectors:[["ion-list"]],inputs:{inset:"inset",lines:"lines",mode:"mode"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["inset","lines","mode"],methods:["closeSlidingItems"]})],t),t})(),Ho=(()=>{let t=class Ye{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Ye)(i(d),i(a),i(l))};static \u0275cmp=r({type:Ye,selectors:[["ion-list-header"]],inputs:{color:"color",lines:"lines",mode:"mode"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["color","lines","mode"]})],t),t})(),Go=(()=>{let t=class Ue{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement,g(this,this.el,["ionLoadingDidPresent","ionLoadingWillPresent","ionLoadingWillDismiss","ionLoadingDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(e){return new(e||Ue)(i(d),i(a),i(l))};static \u0275cmp=r({type:Ue,selectors:[["ion-loading"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",showBackdrop:"showBackdrop",spinner:"spinner",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["animated","backdropDismiss","cssClass","duration","enterAnimation","htmlAttributes","isOpen","keyboardClose","leaveAnimation","message","mode","showBackdrop","spinner","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],t),t})(),Wo=(()=>{let t=class Je{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement,g(this,this.el,["ionWillOpen","ionWillClose","ionDidOpen","ionDidClose"])}static \u0275fac=function(e){return new(e||Je)(i(d),i(a),i(l))};static \u0275cmp=r({type:Je,selectors:[["ion-menu"]],inputs:{contentId:"contentId",disabled:"disabled",maxEdgeStart:"maxEdgeStart",menuId:"menuId",side:"side",swipeGesture:"swipeGesture",type:"type"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["contentId","disabled","maxEdgeStart","menuId","side","swipeGesture","type"],methods:["isOpen","isActive","open","close","toggle","setOpen"]})],t),t})(),Xo=(()=>{let t=class et{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||et)(i(d),i(a),i(l))};static \u0275cmp=r({type:et,selectors:[["ion-menu-button"]],inputs:{autoHide:"autoHide",color:"color",disabled:"disabled",menu:"menu",mode:"mode",type:"type"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["autoHide","color","disabled","menu","mode","type"]})],t),t})(),qo=(()=>{let t=class tt{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||tt)(i(d),i(a),i(l))};static \u0275cmp=r({type:tt,selectors:[["ion-menu-toggle"]],inputs:{autoHide:"autoHide",menu:"menu"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["autoHide","menu"]})],t),t})(),Qo=(()=>{let t=class nt{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||nt)(i(d),i(a),i(l))};static \u0275cmp=r({type:nt,selectors:[["ion-nav-link"]],inputs:{component:"component",componentProps:"componentProps",routerAnimation:"routerAnimation",routerDirection:"routerDirection"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["component","componentProps","routerAnimation","routerDirection"]})],t),t})(),$o=(()=>{let t=class ot{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||ot)(i(d),i(a),i(l))};static \u0275cmp=r({type:ot,selectors:[["ion-note"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["color","mode"]})],t),t})(),Ko=(()=>{let t=class it{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||it)(i(d),i(a),i(l))};static \u0275cmp=r({type:it,selectors:[["ion-picker"]],inputs:{mode:"mode"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["mode"]})],t),t})(),Yo=(()=>{let t=class at{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement,g(this,this.el,["ionChange"])}static \u0275fac=function(e){return new(e||at)(i(d),i(a),i(l))};static \u0275cmp=r({type:at,selectors:[["ion-picker-column"]],inputs:{color:"color",disabled:"disabled",mode:"mode",value:"value"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["color","disabled","mode","value"],methods:["setFocus"]})],t),t})(),Uo=(()=>{let t=class rt{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||rt)(i(d),i(a),i(l))};static \u0275cmp=r({type:rt,selectors:[["ion-picker-column-option"]],inputs:{color:"color",disabled:"disabled",value:"value"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["color","disabled","value"]})],t),t})(),Jo=(()=>{let t=class ct{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement,g(this,this.el,["ionPickerDidPresent","ionPickerWillPresent","ionPickerWillDismiss","ionPickerDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(e){return new(e||ct)(i(d),i(a),i(l))};static \u0275cmp=r({type:ct,selectors:[["ion-picker-legacy"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",columns:"columns",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",showBackdrop:"showBackdrop",trigger:"trigger"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["animated","backdropDismiss","buttons","columns","cssClass","duration","enterAnimation","htmlAttributes","isOpen","keyboardClose","leaveAnimation","mode","showBackdrop","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss","getColumn"]})],t),t})(),ei=(()=>{let t=class lt{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||lt)(i(d),i(a),i(l))};static \u0275cmp=r({type:lt,selectors:[["ion-progress-bar"]],inputs:{buffer:"buffer",color:"color",mode:"mode",reversed:"reversed",type:"type",value:"value"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["buffer","color","mode","reversed","type","value"]})],t),t})(),ti=(()=>{let t=class st{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement,g(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(e){return new(e||st)(i(d),i(a),i(l))};static \u0275cmp=r({type:st,selectors:[["ion-radio"]],inputs:{alignment:"alignment",color:"color",disabled:"disabled",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",value:"value"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["alignment","color","disabled","justify","labelPlacement","mode","name","value"]})],t),t})(),ni=(()=>{let t=class dt{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement,g(this,this.el,["ionChange"])}static \u0275fac=function(e){return new(e||dt)(i(d),i(a),i(l))};static \u0275cmp=r({type:dt,selectors:[["ion-radio-group"]],inputs:{allowEmptySelection:"allowEmptySelection",compareWith:"compareWith",errorText:"errorText",helperText:"helperText",name:"name",value:"value"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["allowEmptySelection","compareWith","errorText","helperText","name","value"]})],t),t})(),oi=(()=>{let t=class ut{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement,g(this,this.el,["ionChange","ionInput","ionFocus","ionBlur","ionKnobMoveStart","ionKnobMoveEnd"])}static \u0275fac=function(e){return new(e||ut)(i(d),i(a),i(l))};static \u0275cmp=r({type:ut,selectors:[["ion-range"]],inputs:{activeBarStart:"activeBarStart",color:"color",debounce:"debounce",disabled:"disabled",dualKnobs:"dualKnobs",label:"label",labelPlacement:"labelPlacement",max:"max",min:"min",mode:"mode",name:"name",pin:"pin",pinFormatter:"pinFormatter",snaps:"snaps",step:"step",ticks:"ticks",value:"value"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["activeBarStart","color","debounce","disabled","dualKnobs","label","labelPlacement","max","min","mode","name","pin","pinFormatter","snaps","step","ticks","value"]})],t),t})(),ii=(()=>{let t=class pt{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement,g(this,this.el,["ionRefresh","ionPull","ionStart"])}static \u0275fac=function(e){return new(e||pt)(i(d),i(a),i(l))};static \u0275cmp=r({type:pt,selectors:[["ion-refresher"]],inputs:{closeDuration:"closeDuration",disabled:"disabled",mode:"mode",pullFactor:"pullFactor",pullMax:"pullMax",pullMin:"pullMin",snapbackDuration:"snapbackDuration"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["closeDuration","disabled","mode","pullFactor","pullMax","pullMin","snapbackDuration"],methods:["complete","cancel","getProgress"]})],t),t})(),ai=(()=>{let t=class mt{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||mt)(i(d),i(a),i(l))};static \u0275cmp=r({type:mt,selectors:[["ion-refresher-content"]],inputs:{pullingIcon:"pullingIcon",pullingText:"pullingText",refreshingSpinner:"refreshingSpinner",refreshingText:"refreshingText"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["pullingIcon","pullingText","refreshingSpinner","refreshingText"]})],t),t})(),ri=(()=>{let t=class ft{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||ft)(i(d),i(a),i(l))};static \u0275cmp=r({type:ft,selectors:[["ion-reorder"]],standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({})],t),t})(),ci=(()=>{let t=class gt{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement,g(this,this.el,["ionItemReorder"])}static \u0275fac=function(e){return new(e||gt)(i(d),i(a),i(l))};static \u0275cmp=r({type:gt,selectors:[["ion-reorder-group"]],inputs:{disabled:"disabled"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["disabled"],methods:["complete"]})],t),t})(),li=(()=>{let t=class ht{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||ht)(i(d),i(a),i(l))};static \u0275cmp=r({type:ht,selectors:[["ion-ripple-effect"]],inputs:{type:"type"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["type"],methods:["addRipple"]})],t),t})(),si=(()=>{let t=class vt{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||vt)(i(d),i(a),i(l))};static \u0275cmp=r({type:vt,selectors:[["ion-row"]],standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({})],t),t})(),di=(()=>{let t=class It{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement,g(this,this.el,["ionInput","ionChange","ionCancel","ionClear","ionBlur","ionFocus"])}static \u0275fac=function(e){return new(e||It)(i(d),i(a),i(l))};static \u0275cmp=r({type:It,selectors:[["ion-searchbar"]],inputs:{animated:"animated",autocapitalize:"autocapitalize",autocomplete:"autocomplete",autocorrect:"autocorrect",cancelButtonIcon:"cancelButtonIcon",cancelButtonText:"cancelButtonText",clearIcon:"clearIcon",color:"color",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",inputmode:"inputmode",maxlength:"maxlength",minlength:"minlength",mode:"mode",name:"name",placeholder:"placeholder",searchIcon:"searchIcon",showCancelButton:"showCancelButton",showClearButton:"showClearButton",spellcheck:"spellcheck",type:"type",value:"value"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["animated","autocapitalize","autocomplete","autocorrect","cancelButtonIcon","cancelButtonText","clearIcon","color","debounce","disabled","enterkeyhint","inputmode","maxlength","minlength","mode","name","placeholder","searchIcon","showCancelButton","showClearButton","spellcheck","type","value"],methods:["setFocus","getInputElement"]})],t),t})(),ui=(()=>{let t=class Dt{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement,g(this,this.el,["ionChange"])}static \u0275fac=function(e){return new(e||Dt)(i(d),i(a),i(l))};static \u0275cmp=r({type:Dt,selectors:[["ion-segment"]],inputs:{color:"color",disabled:"disabled",mode:"mode",scrollable:"scrollable",selectOnFocus:"selectOnFocus",swipeGesture:"swipeGesture",value:"value"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["color","disabled","mode","scrollable","selectOnFocus","swipeGesture","value"]})],t),t})(),pi=(()=>{let t=class bt{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||bt)(i(d),i(a),i(l))};static \u0275cmp=r({type:bt,selectors:[["ion-segment-button"]],inputs:{contentId:"contentId",disabled:"disabled",layout:"layout",mode:"mode",type:"type",value:"value"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["contentId","disabled","layout","mode","type","value"]})],t),t})(),mi=(()=>{let t=class Ct{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Ct)(i(d),i(a),i(l))};static \u0275cmp=r({type:Ct,selectors:[["ion-segment-content"]],standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({})],t),t})(),fi=(()=>{let t=class yt{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement,g(this,this.el,["ionSegmentViewScroll"])}static \u0275fac=function(e){return new(e||yt)(i(d),i(a),i(l))};static \u0275cmp=r({type:yt,selectors:[["ion-segment-view"]],inputs:{disabled:"disabled"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["disabled"]})],t),t})(),gi=(()=>{let t=class jt{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement,g(this,this.el,["ionChange","ionCancel","ionDismiss","ionFocus","ionBlur"])}static \u0275fac=function(e){return new(e||jt)(i(d),i(a),i(l))};static \u0275cmp=r({type:jt,selectors:[["ion-select"]],inputs:{cancelText:"cancelText",color:"color",compareWith:"compareWith",disabled:"disabled",errorText:"errorText",expandedIcon:"expandedIcon",fill:"fill",helperText:"helperText",interface:"interface",interfaceOptions:"interfaceOptions",justify:"justify",label:"label",labelPlacement:"labelPlacement",mode:"mode",multiple:"multiple",name:"name",okText:"okText",placeholder:"placeholder",required:"required",selectedText:"selectedText",shape:"shape",toggleIcon:"toggleIcon",value:"value"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["cancelText","color","compareWith","disabled","errorText","expandedIcon","fill","helperText","interface","interfaceOptions","justify","label","labelPlacement","mode","multiple","name","okText","placeholder","required","selectedText","shape","toggleIcon","value"],methods:["open"]})],t),t})(),hi=(()=>{let t=class xt{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||xt)(i(d),i(a),i(l))};static \u0275cmp=r({type:xt,selectors:[["ion-select-modal"]],inputs:{header:"header",multiple:"multiple",options:"options"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["header","multiple","options"]})],t),t})(),vi=(()=>{let t=class Tt{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Tt)(i(d),i(a),i(l))};static \u0275cmp=r({type:Tt,selectors:[["ion-select-option"]],inputs:{disabled:"disabled",value:"value"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["disabled","value"]})],t),t})(),Ii=(()=>{let t=class wt{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||wt)(i(d),i(a),i(l))};static \u0275cmp=r({type:wt,selectors:[["ion-skeleton-text"]],inputs:{animated:"animated"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["animated"]})],t),t})(),Di=(()=>{let t=class Mt{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Mt)(i(d),i(a),i(l))};static \u0275cmp=r({type:Mt,selectors:[["ion-spinner"]],inputs:{color:"color",duration:"duration",name:"name",paused:"paused"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["color","duration","name","paused"]})],t),t})(),bi=(()=>{let t=class St{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement,g(this,this.el,["ionSplitPaneVisible"])}static \u0275fac=function(e){return new(e||St)(i(d),i(a),i(l))};static \u0275cmp=r({type:St,selectors:[["ion-split-pane"]],inputs:{contentId:"contentId",disabled:"disabled",when:"when"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["contentId","disabled","when"]})],t),t})(),xn=(()=>{let t=class Et{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Et)(i(d),i(a),i(l))};static \u0275cmp=r({type:Et,selectors:[["ion-tab"]],inputs:{component:"component",tab:"tab"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["component","tab"],methods:["setActive"]})],t),t})(),Rt=(()=>{let t=class kt{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||kt)(i(d),i(a),i(l))};static \u0275cmp=r({type:kt,selectors:[["ion-tab-bar"]],inputs:{color:"color",mode:"mode",selectedTab:"selectedTab",translucent:"translucent"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["color","mode","selectedTab","translucent"]})],t),t})(),Ci=(()=>{let t=class zt{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||zt)(i(d),i(a),i(l))};static \u0275cmp=r({type:zt,selectors:[["ion-tab-button"]],inputs:{disabled:"disabled",download:"download",href:"href",layout:"layout",mode:"mode",rel:"rel",selected:"selected",tab:"tab",target:"target"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["disabled","download","href","layout","mode","rel","selected","tab","target"]})],t),t})(),yi=(()=>{let t=class Ft{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Ft)(i(d),i(a),i(l))};static \u0275cmp=r({type:Ft,selectors:[["ion-text"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["color","mode"]})],t),t})(),ji=(()=>{let t=class At{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement,g(this,this.el,["ionChange","ionInput","ionBlur","ionFocus"])}static \u0275fac=function(e){return new(e||At)(i(d),i(a),i(l))};static \u0275cmp=r({type:At,selectors:[["ion-textarea"]],inputs:{autoGrow:"autoGrow",autocapitalize:"autocapitalize",autofocus:"autofocus",clearOnEdit:"clearOnEdit",color:"color",cols:"cols",counter:"counter",counterFormatter:"counterFormatter",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",errorText:"errorText",fill:"fill",helperText:"helperText",inputmode:"inputmode",label:"label",labelPlacement:"labelPlacement",maxlength:"maxlength",minlength:"minlength",mode:"mode",name:"name",placeholder:"placeholder",readonly:"readonly",required:"required",rows:"rows",shape:"shape",spellcheck:"spellcheck",value:"value",wrap:"wrap"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["autoGrow","autocapitalize","autofocus","clearOnEdit","color","cols","counter","counterFormatter","debounce","disabled","enterkeyhint","errorText","fill","helperText","inputmode","label","labelPlacement","maxlength","minlength","mode","name","placeholder","readonly","required","rows","shape","spellcheck","value","wrap"],methods:["setFocus","getInputElement"]})],t),t})(),xi=(()=>{let t=class Bt{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Bt)(i(d),i(a),i(l))};static \u0275cmp=r({type:Bt,selectors:[["ion-thumbnail"]],standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({})],t),t})(),Ti=(()=>{let t=class Pt{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Pt)(i(d),i(a),i(l))};static \u0275cmp=r({type:Pt,selectors:[["ion-title"]],inputs:{color:"color",size:"size"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["color","size"]})],t),t})(),wi=(()=>{let t=class Ot{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement,g(this,this.el,["ionToastDidPresent","ionToastWillPresent","ionToastWillDismiss","ionToastDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(e){return new(e||Ot)(i(d),i(a),i(l))};static \u0275cmp=r({type:Ot,selectors:[["ion-toast"]],inputs:{animated:"animated",buttons:"buttons",color:"color",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",icon:"icon",isOpen:"isOpen",keyboardClose:"keyboardClose",layout:"layout",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",position:"position",positionAnchor:"positionAnchor",swipeGesture:"swipeGesture",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["animated","buttons","color","cssClass","duration","enterAnimation","header","htmlAttributes","icon","isOpen","keyboardClose","layout","leaveAnimation","message","mode","position","positionAnchor","swipeGesture","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],t),t})(),Mi=(()=>{let t=class Nt{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement,g(this,this.el,["ionChange","ionFocus","ionBlur"])}static \u0275fac=function(e){return new(e||Nt)(i(d),i(a),i(l))};static \u0275cmp=r({type:Nt,selectors:[["ion-toggle"]],inputs:{alignment:"alignment",checked:"checked",color:"color",disabled:"disabled",enableOnOffLabels:"enableOnOffLabels",errorText:"errorText",helperText:"helperText",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",required:"required",value:"value"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["alignment","checked","color","disabled","enableOnOffLabels","errorText","helperText","justify","labelPlacement","mode","name","required","value"]})],t),t})(),Si=(()=>{let t=class Lt{z;el;constructor(n,e,o){this.z=o,n.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Lt)(i(d),i(a),i(l))};static \u0275cmp=r({type:Lt,selectors:[["ion-toolbar"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})};return t=p([m({inputs:["color","mode"]})],t),t})(),W=(()=>{class t extends sn{parentOutlet;outletContent;constructor(n,e,o,f,h,D,R,Vt){super(n,e,o,f,h,D,R,Vt),this.parentOutlet=Vt}static \u0275fac=function(e){return new(e||t)($("name"),$("tabs"),i(Ut),i(a),i(tn),i(l),i(en),i(t,12))};static \u0275cmp=r({type:t,selectors:[["ion-router-outlet"]],viewQuery:function(e,o){if(e&1&&U(Nn,7,Xt),e&2){let f;M(f=S())&&(o.outletContent=f.first)}},standalone:!1,features:[I],ngContentSelectors:u,decls:3,vars:0,consts:[["outletContent",""]],template:function(e,o){e&1&&(s(),$t(0,null,0),c(2),Kt())},encapsulation:2})}return t})(),Ei=(()=>{class t extends gn{outlet;tabBar;tabBars;tabs;static \u0275fac=(()=>{let n;return function(o){return(n||(n=y(t)))(o||t)}})();static \u0275cmp=r({type:t,selectors:[["ion-tabs"]],contentQueries:function(e,o,f){if(e&1&&(H(f,Rt,5),H(f,Rt,4),H(f,xn,4)),e&2){let h;M(h=S())&&(o.tabBar=h.first),M(h=S())&&(o.tabBars=h),M(h=S())&&(o.tabs=h)}},viewQuery:function(e,o){if(e&1&&U(Ln,5,W),e&2){let f;M(f=S())&&(o.outlet=f.first)}},standalone:!1,features:[I],ngContentSelectors:Vn,decls:6,vars:2,consts:[["tabsInner",""],["outlet",""],[1,"tabs-inner"],["tabs","true",3,"stackWillChange","stackDidChange",4,"ngIf"],[4,"ngIf"],["tabs","true",3,"stackWillChange","stackDidChange"]],template:function(e,o){e&1&&(s(_n),c(0),V(1,"div",2,0),_(3,Zn,2,0,"ion-router-outlet",3)(4,Hn,1,0,"ng-content",4),Z(),c(5,1)),e&2&&(L(3),x("ngIf",o.tabs.length===0),L(),x("ngIf",o.tabs.length>0))},dependencies:[G,W],styles:["[_nghost-%COMP%]{display:flex;position:absolute;inset:0;flex-direction:column;width:100%;height:100%;contain:layout size style}.tabs-inner[_ngcontent-%COMP%]{position:relative;flex:1;contain:layout size style}"]})}return t})(),Ri=(()=>{class t extends un{constructor(n,e,o,f,h,D){super(n,e,o,f,h,D)}static \u0275fac=function(e){return new(e||t)(i(W,8),i(an),i(rn),i(a),i(l),i(d))};static \u0275cmp=r({type:t,selectors:[["ion-back-button"]],standalone:!1,features:[I],ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})}return t})(),ki=(()=>{class t extends fn{constructor(n,e,o,f,h,D){super(n,e,o,f,h,D)}static \u0275fac=function(e){return new(e||t)(i(a),i(k),i(b),i(E),i(l),i(d))};static \u0275cmp=r({type:t,selectors:[["ion-nav"]],standalone:!1,features:[I],ngContentSelectors:u,decls:1,vars:0,template:function(e,o){e&1&&(s(),c(0))},encapsulation:2,changeDetection:0})}return t})(),zi=(()=>{class t extends pn{static \u0275fac=(()=>{let n;return function(o){return(n||(n=y(t)))(o||t)}})();static \u0275dir=C({type:t,selectors:[["","routerLink","",5,"a",5,"area"]],standalone:!1,features:[I]})}return t})(),Fi=(()=>{class t extends mn{static \u0275fac=(()=>{let n;return function(o){return(n||(n=y(t)))(o||t)}})();static \u0275dir=C({type:t,selectors:[["a","routerLink",""],["area","routerLink",""]],standalone:!1,features:[I]})}return t})(),Ai=(()=>{class t extends ln{static \u0275fac=(()=>{let n;return function(o){return(n||(n=y(t)))(o||t)}})();static \u0275cmp=r({type:t,selectors:[["ion-modal"]],standalone:!1,features:[I],decls:1,vars:1,consts:[["class","ion-delegate-host ion-page",4,"ngIf"],[1,"ion-delegate-host","ion-page"],[3,"ngTemplateOutlet"]],template:function(e,o){e&1&&_(0,Gn,2,1,"div",0),e&2&&x("ngIf",o.isCmpOpen||o.keepContentsMounted)},dependencies:[G,J],encapsulation:2,changeDetection:0})}return t})(),Bi=(()=>{class t extends cn{static \u0275fac=(()=>{let n;return function(o){return(n||(n=y(t)))(o||t)}})();static \u0275cmp=r({type:t,selectors:[["ion-popover"]],standalone:!1,features:[I],decls:1,vars:1,consts:[[3,"ngTemplateOutlet",4,"ngIf"],[3,"ngTemplateOutlet"]],template:function(e,o){e&1&&_(0,Wn,1,1,"ng-container",0),e&2&&x("ngIf",o.isCmpOpen||o.keepContentsMounted)},dependencies:[G,J],encapsulation:2,changeDetection:0})}return t})(),Pi={provide:ee,useExisting:X(()=>Tn),multi:!0},Tn=(()=>{class t extends nn{static \u0275fac=(()=>{let n;return function(o){return(n||(n=y(t)))(o||t)}})();static \u0275dir=C({type:t,selectors:[["ion-input","type","number","max","","formControlName",""],["ion-input","type","number","max","","formControl",""],["ion-input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(e,o){e&2&&K("max",o._enabled?o.max:null)},standalone:!1,features:[T([Pi]),I]})}return t})(),Oi={provide:ee,useExisting:X(()=>wn),multi:!0},wn=(()=>{class t extends on{static \u0275fac=(()=>{let n;return function(o){return(n||(n=y(t)))(o||t)}})();static \u0275dir=C({type:t,selectors:[["ion-input","type","number","min","","formControlName",""],["ion-input","type","number","min","","formControl",""],["ion-input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(e,o){e&2&&K("min",o._enabled?o.min:null)},standalone:!1,features:[T([Oi]),I]})}return t})(),qa=(()=>{class t extends B{constructor(){super(ne)}static \u0275fac=function(e){return new(e||t)};static \u0275prov=N({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();var Ni=(()=>{class t extends B{angularDelegate=j(E);injector=j(b);environmentInjector=j(k);constructor(){super(oe)}create(n){return super.create(O(P({},n),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"modal")}))}static \u0275fac=function(e){return new(e||t)};static \u0275prov=N({token:t,factory:t.\u0275fac})}return t})();var _t=class extends B{angularDelegate=j(E);injector=j(b);environmentInjector=j(k);constructor(){super(ie)}create(v){return super.create(O(P({},v),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"popover")}))}},Qa=(()=>{class t extends B{constructor(){super(ae)}static \u0275fac=function(e){return new(e||t)};static \u0275prov=N({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),Li=(t,v,n)=>()=>{let e=v.defaultView;if(e&&typeof window<"u"){re(O(P({},t),{_zoneGate:f=>n.run(f)}));let o="__zone_symbol__addEventListener"in v.body?"__zone_symbol__addEventListener":"addEventListener";return jn(e,{exclude:["ion-tabs"],syncQueue:!0,raf:hn,jmp:f=>n.runOutsideAngular(f),ael(f,h,D,R){f[o](h,D,R)},rel(f,h,D,R){f.removeEventListener(h,D,R)}})}},_i=[Un,Jn,eo,to,no,oo,io,ao,ro,co,lo,so,uo,po,mo,fo,go,ho,vo,Io,Do,bo,Co,yo,jo,xo,To,wo,Mo,So,Eo,Ro,ko,zo,Fo,Ao,Bo,Po,Oo,No,Lo,_o,Vo,Zo,Ho,Go,Wo,Xo,qo,Qo,$o,Ko,Yo,Uo,Jo,ei,ti,ni,oi,ii,ai,ri,ci,li,si,di,ui,pi,mi,fi,gi,hi,vi,Ii,Di,bi,xn,Rt,Ci,yi,ji,xi,Ti,wi,Mi,Si],$a=[..._i,Ai,Bi,Xn,qn,Qn,$n,Ei,W,Ri,ki,zi,Fi,wn,Tn],Ka=(()=>{class t{static forRoot(n={}){return{ngModule:t,providers:[{provide:te,useValue:n},{provide:Qt,useFactory:Li,multi:!0,deps:[te,Wt,l]},E,dn()]}}static \u0275fac=function(e){return new(e||t)};static \u0275mod=qt({type:t});static \u0275inj=Gt({providers:[Ni,_t],imports:[Jt]})}return t})();export{$n as a,lo as b,Do as c,So as d,zo as e,W as f,qa as g,Ni as h,Qa as i,Ka as j};
