var o=Object.defineProperty,p=Object.defineProperties;var q=Object.getOwnPropertyDescriptors;var f=Object.getOwnPropertySymbols;var k=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable;var j=(b,c,a)=>c in b?o(b,c,{enumerable:!0,configurable:!0,writable:!0,value:a}):b[c]=a,r=(b,c)=>{for(var a in c||={})k.call(c,a)&&j(b,a,c[a]);if(f)for(var a of f(c))l.call(c,a)&&j(b,a,c[a]);return b},s=(b,c)=>p(b,q(c));var t=b=>c=>{var a=b[c];if(a)return a();throw new Error("Module not found in bundle: "+c)};var u=(b,c)=>{var a={};for(var d in b)k.call(b,d)&&c.indexOf(d)<0&&(a[d]=b[d]);if(b!=null&&f)for(var d of f(b))c.indexOf(d)<0&&l.call(b,d)&&(a[d]=b[d]);return a};var v=(b,c,a)=>new Promise((d,i)=>{var m=e=>{try{g(a.next(e))}catch(h){i(h)}},n=e=>{try{g(a.throw(e))}catch(h){i(h)}},g=e=>e.done?d(e.value):Promise.resolve(e.value).then(m,n);g((a=a.apply(b,c)).next())});export{r as a,s as b,t as c,u as d,v as e};
