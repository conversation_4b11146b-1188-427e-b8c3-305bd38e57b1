.admin-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background:   (135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

@media (max-width: 992px) {
  .admin-layout {
    /* Garde la structure flex existante mais optimise pour mobile */
  }
}

/* Top Navbar Styles */
.admin-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 70px;
  background: #c8102e;
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: all 0.3s ease;

  .navbar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding: 0 24px;
    max-width: 100%;
  }

  .navbar-left {
    display: flex;
    align-items: center;
    gap: 16px;

    .menu-toggle {
      background: none;
      border: none;
      padding: 8px;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      color: #4a5568;

      &:hover {
        background: rgba(102, 126, 234, 0.1);
        color: #667eea;
        transform: scale(1.05);
      }

      ion-icon {
        font-size: 24px;
      }
    }

    .app-title {
      font-size: 24px;
      font-weight: 700;
      background: white;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin: 0;
    }
  }

  .navbar-right {
    display: flex;
    align-items: center;
    gap: 20px;
    position: relative;

 

      

    

    .user-profile {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      padding: 8px 12px;
      border-radius: 12px;
      transition: all 0.3s ease;
      background: rgba(255, 255, 255, 0.8);
      border: 1px solid #c8102e;

      &:hover {
        background: rgba(229, 230, 233, 0.925);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(255, 255, 255, 0.904);
       
      }

      .avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid #272931;
      }

      .username {
        font-weight: 600;
        color: #4a5568;
      }

      ion-icon {
        color: #a0aec0;
        transition: transform 0.3s ease;
      }

      &.active ion-icon {
        transform: rotate(180deg);
      }
    }

    .user-menu {
      position: absolute;
      top: 100%;
      right: 0;
      background: white;
      border-radius: 12px;
      box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
      border: 1px solid rgba(0, 0, 0, 0.1);
      min-width: 200px;
      opacity: 0;
      visibility: hidden;
      transform: translateY(-10px);
      transition: all 0.3s ease;
      z-index: 1001;

      &.show {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
      }

      .menu-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px 16px;
        text-decoration: none;
        color: #4a5568;
        transition: all 0.3s ease;
        border-radius: 8px;
        margin: 4px;

        &:hover {
          background: rgba(102, 126, 234, 0.1);
          color: #667eea;
        }

        &.logout {
          color: #e53e3e;

          &:hover {
            background: rgba(229, 62, 62, 0.1);
            color: #e53e3e;
          }
        }

        ion-icon {
          font-size: 18px;
        }
      }

      .menu-divider {
        margin: 8px 0;
        border: none;
        border-top: 1px solid rgba(0, 0, 0, 0.1);
      }
    }
  }
}

/* Sidebar Styles */
.admin-sidebar {
  position: fixed;
  top: 70px;
  left: 0;
  width: 280px;
  height: calc(100vh - 70px);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  z-index: 999;
  overflow-y: auto;

  &.collapsed {
    width: 80px;
  }

  /* Custom scrollbar */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(102, 126, 234, 0.3);
    border-radius: 3px;

    &:hover {
      background: rgba(102, 126, 234, 0.5);
    }
  }
}

/* Main Content Styles */
.admin-main {
  margin-top: 70px;
  margin-left: 280px;
  min-height: calc(100vh - 70px);
  transition: all 0.3s ease;
  background: transparent;

  &.sidebar-collapsed {
    margin-left: 80px;
  }

  .content-wrapper {
    padding: 24px;
    min-height: 100%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px 0 0 0;
    margin: 0;
  }
}

/* Animations */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-navbar {
    .navbar-right {
      .search-container {
        display: none;
      }

      .username {
        display: none;
      }
    }
  }

  .admin-sidebar {
    width: 280px;
    transform: translateX(-100%);

    &.collapsed {
      transform: translateX(0);
      width: 100%;
    }
  }

  .admin-main {
    margin-left: 0;

    &.sidebar-collapsed {
      margin-left: 0;
    }
  }
}

@media (max-width: 480px) {
  .admin-navbar {
    .navbar-content {
      padding: 0 16px;
    }

    .navbar-left {
      .app-title {
        font-size: 18px;
      }
    }

    .navbar-right {
      gap: 12px;
    }
  }

  .admin-main {
    .content-wrapper {
      padding: 16px;
      border-radius: 16px 0 0 0;
    }
  }
}