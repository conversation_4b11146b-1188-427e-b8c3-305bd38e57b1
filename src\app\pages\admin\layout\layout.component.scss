.admin-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
  position: relative;
}

/* Mobile overlay for sidebar */
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 998;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;

  &.show {
    opacity: 1;
    visibility: visible;
  }
}

/* Top Navbar Styles */
.admin-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 70px;
  background: #c8102e;
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: all 0.3s ease;

  .navbar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding: 0 clamp(16px, 4vw, 24px);
    max-width: 100%;
  }

  .navbar-left {
    display: flex;
    align-items: center;
    gap: clamp(12px, 3vw, 16px);
    flex: 1;
    min-width: 0; /* Allows text truncation */

    .menu-toggle {
      background: none;
      border: none;
      padding: 8px;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 40px;
      min-height: 40px;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: scale(1.05);
      }

      &:active {
        transform: scale(0.95);
      }

      ion-icon {
        font-size: clamp(20px, 5vw, 24px);
      }
    }

    .app-title {
      font-size: clamp(18px, 4vw, 24px);
      font-weight: 700;
      background: white;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin: 0;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .navbar-right {
    display: flex;
    align-items: center;
    gap: clamp(12px, 3vw, 20px);
    position: relative;
    flex-shrink: 0;

    .user-profile {
      display: flex;
      align-items: center;
      gap: clamp(6px, 2vw, 8px);
      cursor: pointer;
      padding: clamp(6px, 2vw, 8px) clamp(8px, 2vw, 12px);
      border-radius: 12px;
      transition: all 0.3s ease;
      background: rgba(255, 255, 255, 0.9);
      border: 1px solid rgba(255, 255, 255, 0.3);
      backdrop-filter: blur(10px);
      min-width: 0; /* Allows content to shrink */

      &:hover {
        background: rgba(255, 255, 255, 0.95);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      &:active {
        transform: translateY(0);
      }

      .avatar {
        width: clamp(28px, 6vw, 32px);
        height: clamp(28px, 6vw, 32px);
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid #c8102e;
        flex-shrink: 0;
      }

      .username {
        font-weight: 600;
        color: #4a5568;
        font-size: clamp(12px, 3vw, 14px);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        min-width: 0;
      }

      ion-icon {
        color: #a0aec0;
        transition: transform 0.3s ease;
        font-size: clamp(16px, 4vw, 18px);
        flex-shrink: 0;
      }

      &.active ion-icon {
        transform: rotate(180deg);
      }
    }

    .user-menu {
      position: absolute;
      top: 100%;
      right: 0;
      background: white;
      border-radius: 12px;
      box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
      border: 1px solid rgba(0, 0, 0, 0.1);
      min-width: 200px;
      opacity: 0;
      visibility: hidden;
      transform: translateY(-10px);
      transition: all 0.3s ease;
      z-index: 1001;

      &.show {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
      }

      .menu-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px 16px;
        text-decoration: none;
        color: #4a5568;
        transition: all 0.3s ease;
        border-radius: 8px;
        margin: 4px;

        &:hover {
          background: rgba(102, 126, 234, 0.1);
          color: #667eea;
        }

        &.logout {
          color: #e53e3e;

          &:hover {
            background: rgba(229, 62, 62, 0.1);
            color: #e53e3e;
          }
        }

        ion-icon {
          font-size: 18px;
        }
      }

      .menu-divider {
        margin: 8px 0;
        border: none;
        border-top: 1px solid rgba(0, 0, 0, 0.1);
      }
    }
  }
}

/* Sidebar Styles */
.admin-sidebar {
  position: fixed;
  top: 70px;
  left: 0;
  width: 280px;
  height: calc(100vh - 70px);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 999;
  overflow-y: auto;
  overflow-x: hidden;

  &.collapsed {
    width: 80px;
  }

  /* Custom scrollbar */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(200, 16, 46, 0.3);
    border-radius: 3px;

    &:hover {
      background: rgba(200, 16, 46, 0.5);
    }
  }

  /* Smooth scrolling for touch devices */
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* Main Content Styles */
.admin-main {
  margin-top: 70px;
  margin-left: 280px;
  min-height: calc(100vh - 70px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: transparent;
  width: calc(100vw - 280px);
  overflow-x: hidden;

  &.sidebar-collapsed {
    margin-left: 80px;
    width: calc(100vw - 80px);
  }

  .content-wrapper {
    padding: clamp(16px, 4vw, 24px);
    min-height: 100%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: clamp(16px, 4vw, 20px) 0 0 0;
    margin: 0;
    max-width: 100%;
    box-sizing: border-box;
  }
}

/* Animations */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive Design - Mobile First Approach */

/* Tablet and small desktop */
@media (max-width: 1024px) {
  .admin-sidebar {
    width: 260px;

    &.collapsed {
      width: 70px;
    }
  }

  .admin-main {
    margin-left: 260px;
    width: calc(100vw - 260px);

    &.sidebar-collapsed {
      margin-left: 70px;
      width: calc(100vw - 70px);
    }
  }
}

/* Mobile and tablet */
@media (max-width: 768px) {
  .admin-navbar {
    height: 60px;

    .navbar-content {
      padding: 0 16px;
    }

    .navbar-right {
      .username {
        display: none;
      }
    }
  }

  .admin-sidebar {
    top: 60px;
    height: calc(100vh - 60px);
    width: 100%;
    max-width: 320px;
    transform: translateX(-100%);
    z-index: 1001;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);

    &.collapsed {
      transform: translateX(0);
    }
  }

  .admin-main {
    margin-top: 60px;
    margin-left: 0;
    width: 100%;

    &.sidebar-collapsed {
      margin-left: 0;
    }

    .content-wrapper {
      border-radius: 16px 16px 0 0;
    }
  }

  /* Show mobile overlay when sidebar is open */
  .mobile-overlay.show {
    display: block;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .admin-navbar {
    height: 56px;

    .navbar-left {
      gap: 8px;

      .menu-toggle {
        padding: 6px;
        min-width: 36px;
        min-height: 36px;
      }
    }

    .navbar-right {
      gap: 8px;

      .user-profile {
        padding: 4px 8px;
        gap: 4px;

        .avatar {
          width: 24px;
          height: 24px;
        }
      }
    }
  }

  .admin-sidebar {
    top: 56px;
    height: calc(100vh - 56px);
    max-width: 280px;
  }

  .admin-main {
    margin-top: 56px;

    .content-wrapper {
      padding: 12px;
      border-radius: 12px 12px 0 0;
    }
  }
}

/* Extra small devices */
@media (max-width: 360px) {
  .admin-navbar {
    .navbar-left {
      .app-title {
        font-size: 16px;
      }
    }

    .navbar-right {
      .user-profile {
        .username {
          display: none;
        }
      }
    }
  }

  .admin-sidebar {
    max-width: 100%;
  }
}

/* Landscape orientation on mobile */
@media (max-height: 500px) and (orientation: landscape) {
  .admin-navbar {
    height: 50px;
  }

  .admin-sidebar {
    top: 50px;
    height: calc(100vh - 50px);
  }

  .admin-main {
    margin-top: 50px;
  }
}