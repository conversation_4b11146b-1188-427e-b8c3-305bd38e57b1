import{i as s,k as i,pa as a}from"./chunk-XPS4RP6N.js";var c=(()=>{let e=class e{constructor(t){this.http=t,this.baseUrl="http://localhost:8080/api/auth"}register(t){return this.http.post(`${this.baseUrl}/register`,t)}login(t){return this.http.post(`${this.baseUrl}/login`,t)}saveSession(t,r){localStorage.setItem("sessionToken",t),localStorage.setItem("userRole",r)}getToken(){return localStorage.getItem("sessionToken")}getUserRole(){return localStorage.getItem("userRole")}isLoggedIn(){return!!this.getToken()}logout(){localStorage.removeItem("sessionToken"),localStorage.removeItem("userRole")}};e.\u0275fac=function(r){return new(r||e)(i(a))},e.\u0275prov=s({token:e,factory:e.\u0275fac,providedIn:"root"});let o=e;return o})();export{c as a};
