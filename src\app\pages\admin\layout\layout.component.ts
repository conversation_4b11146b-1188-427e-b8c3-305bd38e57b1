import { Component, OnInit, HostListener } from '@angular/core';
import { Router } from '@angular/router';
import { SidebarComponent } from '../sidebar/sidebar.component';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

@Component({
  selector: 'app-layout',
  templateUrl: './layout.component.html',
  styleUrls: ['./layout.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    IonicModule,
    SidebarComponent
  ]
})
export class LayoutComponent implements OnInit {
  sidebarCollapsed = false;
  showUserMenu = false;
  isMobile = false;

  constructor(private router: Router) {}

  ngOnInit() {
    // Check screen size on init
    this.checkScreenSize();
  }

  toggleSidebar() {
    if (this.isMobile) {
      // On mobile, toggle between open/closed
      this.sidebarCollapsed = !this.sidebarCollapsed;
    } else {
      // On desktop, toggle between expanded/collapsed
      this.sidebarCollapsed = !this.sidebarCollapsed;
    }
  }

  closeSidebar() {
    if (this.isMobile) {
      this.sidebarCollapsed = false;
    }
  }

  toggleUserMenu() {
    this.showUserMenu = !this.showUserMenu;
  }

  logout() {
    localStorage.clear();
    sessionStorage.clear();
    this.router.navigate(['/login']);
  }

  @HostListener('window:resize', ['$event'])
  onResize(_event: any) {
    this.checkScreenSize();
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: any) {
    // Close user menu when clicking outside
    if (!event.target.closest('.user-profile') && !event.target.closest('.user-menu')) {
      this.showUserMenu = false;
    }
  }

  private checkScreenSize() {
    const isMobileNow = window.innerWidth < 768;

    if (isMobileNow !== this.isMobile) {
      this.isMobile = isMobileNow;

      if (this.isMobile) {
        // On mobile, start with sidebar closed
        this.sidebarCollapsed = false;
      } else {
        // On desktop, start with sidebar open
        this.sidebarCollapsed = false;
      }
    }
  }
}
