import { Injectable, NgZone } from '@angular/core';
import { Client, IMessage } from '@stomp/stompjs';
import SockJS from 'sockjs-client';
import { BehaviorSubject } from 'rxjs';
// 👉 Ajout si tu veux utiliser l'environment
// import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class WebSocketService {
  private stompClient: Client;

  // 👥 Clients actifs
  private activeClientsSubject = new BehaviorSubject<number>(0);
  public activeClients$ = this.activeClientsSubject.asObservable();

  // 🚕 Chauffeurs actifs
  private activeChauffeursSubject = new BehaviorSubject<number>(0);
  public activeChauffeurs$ = this.activeChauffeursSubject.asObservable();

  // 🔄 Rafraîchissement des clients
  private refreshClientsSubject = new BehaviorSubject<void>(undefined);
  public refreshClients$ = this.refreshClientsSubject.asObservable();

  // 🔄 Rafraîchissement des chauffeurs
  private refreshChauffeursSubject = new BehaviorSubject<void>(undefined);
  public refreshChauffeurs$ = this.refreshChauffeursSubject.asObservable();


  constructor(private zone: NgZone) {
    this.stompClient = new Client({
      // ✅ tu peux remplacer l’URL hardcodée par environment.wsUrl
      webSocketFactory: () => new SockJS('http://10.136.50.110:8080/ws'),
      debug: (str) => console.log(str),
      reconnectDelay: 500,
    });

    this.stompClient.onConnect = () => {
      console.log('🟢 WebSocket connecté');

      this.subscribeToTopic('/topic/clients', (body) => {
        console.log('📩 Message reçu sur /topic/clients:', body);

        if (body.type === 'activeCount') {
          this.zone.run(() => this.activeClientsSubject.next(body.data));
        }

        if (body.type === 'refresh') {
          console.log('🔁 Refresh client reçu');
          this.zone.run(() => this.refreshClientsSubject.next());
        }
      });

      this.subscribeToTopic('/topic/chauffeurs', (body) => {
        console.log('📩 Message reçu sur /topic/chauffeurs:', body);

        if (body.type === 'activeCount') {
          this.zone.run(() => this.activeChauffeursSubject.next(body.data));
        }

        if (body.type === 'refresh') {
          console.log('🔁 Refresh chauffeur reçu');
          this.zone.run(() => this.refreshChauffeursSubject.next());
        }
      });
    };

    this.stompClient.onStompError = (frame) => {
      console.error('❌ STOMP error', frame);
    };
  }

  connect(): void {
    this.stompClient.activate();
  }

  disconnect(): void {
    this.stompClient.deactivate();
  }


   

  // 🔁 Méthode générique pour gérer les souscriptions
  private subscribeToTopic(topic: string, handler: (body: any) => void): void {
    this.stompClient.subscribe(topic, (message: IMessage) => {
      const body = JSON.parse(message.body);
      handler(body);
    });
  }
}
