import{a as E}from"./chunk-QUJFQN2Y.js";import{a as B}from"./chunk-423VJ54E.js";import{a as L,b as P,c as y,d as _,e as g,h as R}from"./chunk-AWU72VOH.js";import{n as l,s as V}from"./chunk-3AW3VJFF.js";import{a as b,b as O,f as I,i as k,m as C,n as q,o as A,p as S}from"./chunk-I4XHNRTT.js";import{e as v}from"./chunk-JHI3MBHO.js";var N=1,j=2,T=3,w=class{constructor(e,t){this.component=e,this.params=t,this.state=N}init(e){return v(this,null,function*(){if(this.state=j,!this.element){let t=this.component;this.element=yield B(this.delegate,e,t,["ion-page","ion-page-invisible"],this.params)}})}_destroy(){l(this.state!==T,"view state must be ATTACHED");let e=this.element;e&&(this.delegate?this.delegate.removeViewFromDom(e.parentElement,e):e.remove()),this.nav=void 0,this.state=T}},x=(c,e,t)=>!c||c.component!==e?!1:V(c.params,t),G=(c,e)=>c?c instanceof w?c:new w(c,e):null,D=c=>c.map(e=>e instanceof w?e:"component"in e?G(e.component,e.componentProps===null?void 0:e.componentProps):G(e,void 0)).filter(e=>e!==null),W=":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:layout size style;z-index:0}",Q=(()=>{let c=class{constructor(e){k(this,e),this.ionNavWillLoad=S(this,"ionNavWillLoad",7),this.ionNavWillChange=S(this,"ionNavWillChange",3),this.ionNavDidChange=S(this,"ionNavDidChange",3),this.transInstr=[],this.gestureOrAnimationInProgress=!1,this.useRouter=!1,this.isTransitioning=!1,this.destroyed=!1,this.views=[],this.didLoad=!1,this.animated=!0}swipeGestureChanged(){this.gesture&&this.gesture.enable(this.swipeGesture===!0)}rootChanged(){this.root!==void 0&&this.didLoad!==!1&&(this.useRouter||this.root!==void 0&&this.setRoot(this.root,this.rootParams))}componentWillLoad(){if(this.useRouter=document.querySelector("ion-router")!==null&&this.el.closest("[no-router]")===null,this.swipeGesture===void 0){let e=I(this);this.swipeGesture=b.getBoolean("swipeBackEnabled",e==="ios")}this.ionNavWillLoad.emit()}componentDidLoad(){return v(this,null,function*(){this.didLoad=!0,this.rootChanged(),this.gesture=(yield import("./chunk-RXWAWD3U.js")).createSwipeBackGesture(this.el,this.canStart.bind(this),this.onStart.bind(this),this.onMove.bind(this),this.onEnd.bind(this)),this.swipeGestureChanged()})}connectedCallback(){this.destroyed=!1}disconnectedCallback(){for(let e of this.views)g(e.element,y),e._destroy();this.gesture&&(this.gesture.destroy(),this.gesture=void 0),this.transInstr.length=0,this.views.length=0,this.destroyed=!0}push(e,t,s,n){return this.insert(-1,e,t,s,n)}insert(e,t,s,n,i){return this.insertPages(e,[{component:t,componentProps:s}],n,i)}insertPages(e,t,s,n){return this.queueTrns({insertStart:e,insertViews:t,opts:s},n)}pop(e,t){return this.removeIndex(-1,1,e,t)}popTo(e,t,s){let n={removeStart:-1,removeCount:-1,opts:t};return typeof e=="object"&&e.component?(n.removeView=e,n.removeStart=1):typeof e=="number"&&(n.removeStart=e+1),this.queueTrns(n,s)}popToRoot(e,t){return this.removeIndex(1,-1,e,t)}removeIndex(e,t=1,s,n){return this.queueTrns({removeStart:e,removeCount:t,opts:s},n)}setRoot(e,t,s,n){return this.setPages([{component:e,componentProps:t}],s,n)}setPages(e,t,s){return t??(t={}),t.animated!==!0&&(t.animated=!1),this.queueTrns({insertStart:0,insertViews:e,removeStart:0,removeCount:-1,opts:t},s)}setRouteId(e,t,s,n){let i=this.getActiveSync();if(x(i,e,t))return Promise.resolve({changed:!1,element:i.element});let r,o=new Promise(h=>r=h),a,d={updateURL:!1,viewIsReady:h=>{let u,p=new Promise(f=>u=f);return r({changed:!0,element:h,markVisible:()=>v(this,null,function*(){u(),yield a})}),p}};if(s==="root")a=this.setRoot(e,t,d);else{let h=this.views.find(u=>x(u,e,t));h?a=this.popTo(h,Object.assign(Object.assign({},d),{direction:"back",animationBuilder:n})):s==="forward"?a=this.push(e,t,Object.assign(Object.assign({},d),{animationBuilder:n})):s==="back"&&(a=this.setRoot(e,t,Object.assign(Object.assign({},d),{direction:"back",animated:!0,animationBuilder:n})))}return o}getRouteId(){return v(this,null,function*(){let e=this.getActiveSync();if(e)return{id:e.element.tagName,params:e.params,element:e.element}})}getActive(){return v(this,null,function*(){return this.getActiveSync()})}getByIndex(e){return v(this,null,function*(){return this.views[e]})}canGoBack(e){return v(this,null,function*(){return this.canGoBackSync(e)})}getPrevious(e){return v(this,null,function*(){return this.getPreviousSync(e)})}getLength(){return v(this,null,function*(){return Promise.resolve(this.views.length)})}getActiveSync(){return this.views[this.views.length-1]}canGoBackSync(e=this.getActiveSync()){return!!(e&&this.getPreviousSync(e))}getPreviousSync(e=this.getActiveSync()){if(!e)return;let t=this.views,s=t.indexOf(e);return s>0?t[s-1]:void 0}queueTrns(e,t){return v(this,null,function*(){var s,n;if(this.isTransitioning&&(!((s=e.opts)===null||s===void 0)&&s.skipIfBusy))return!1;let i=new Promise((r,o)=>{e.resolve=r,e.reject=o});if(e.done=t,e.opts&&e.opts.updateURL!==!1&&this.useRouter){let r=document.querySelector("ion-router");if(r){let o=yield r.canTransition();if(o===!1)return!1;if(typeof o=="string")return r.push(o,e.opts.direction||"back"),!1}}return((n=e.insertViews)===null||n===void 0?void 0:n.length)===0&&(e.insertViews=void 0),this.transInstr.push(e),this.nextTrns(),i})}success(e,t){if(this.destroyed){this.fireError("nav controller was destroyed",t);return}if(t.done&&t.done(e.hasCompleted,e.requiresTransition,e.enteringView,e.leavingView,e.direction),t.resolve(e.hasCompleted),t.opts.updateURL!==!1&&this.useRouter){let s=document.querySelector("ion-router");if(s){let n=e.direction==="back"?"back":"forward";s.navChanged(n)}}}failed(e,t){if(this.destroyed){this.fireError("nav controller was destroyed",t);return}this.transInstr.length=0,this.fireError(e,t)}fireError(e,t){t.done&&t.done(!1,!1,e),t.reject&&!this.destroyed?t.reject(e):t.resolve(!1)}nextTrns(){if(this.isTransitioning)return!1;let e=this.transInstr.shift();return e?(this.runTransition(e),!0):!1}runTransition(e){return v(this,null,function*(){try{this.ionNavWillChange.emit(),this.isTransitioning=!0,this.prepareTI(e);let t=this.getActiveSync(),s=this.getEnteringView(e,t);if(!t&&!s)throw new Error("no views in the stack to be removed");s&&s.state===N&&(yield s.init(this.el)),this.postViewInit(s,t,e);let n=(e.enteringRequiresTransition||e.leavingRequiresTransition)&&s!==t;n&&e.opts&&t&&(e.opts.direction==="back"&&(e.opts.animationBuilder=e.opts.animationBuilder||s?.animationBuilder),t.animationBuilder=e.opts.animationBuilder);let i;n?i=yield this.transition(s,t,e):i={hasCompleted:!0,requiresTransition:!1},this.success(i,e),this.ionNavDidChange.emit()}catch(t){this.failed(t,e)}this.isTransitioning=!1,this.nextTrns()})}prepareTI(e){var t,s,n;let i=this.views.length;if((t=e.opts)!==null&&t!==void 0||(e.opts={}),(s=(n=e.opts).delegate)!==null&&s!==void 0||(n.delegate=this.delegate),e.removeView!==void 0){l(e.removeStart!==void 0,"removeView needs removeStart"),l(e.removeCount!==void 0,"removeView needs removeCount");let a=this.views.indexOf(e.removeView);if(a<0)throw new Error("removeView was not found");e.removeStart+=a}e.removeStart!==void 0&&(e.removeStart<0&&(e.removeStart=i-1),e.removeCount<0&&(e.removeCount=i-e.removeStart),e.leavingRequiresTransition=e.removeCount>0&&e.removeStart+e.removeCount===i),e.insertViews&&((e.insertStart<0||e.insertStart>i)&&(e.insertStart=i),e.enteringRequiresTransition=e.insertStart===i);let r=e.insertViews;if(!r)return;l(r.length>0,"length can not be zero");let o=D(r);if(o.length===0)throw new Error("invalid views to insert");for(let a of o){a.delegate=e.opts.delegate;let d=a.nav;if(d&&d!==this)throw new Error("inserted view was already inserted");if(a.state===T)throw new Error("inserted view was already destroyed")}e.insertViews=o}getEnteringView(e,t){let s=e.insertViews;if(s!==void 0)return s[s.length-1];let n=e.removeStart;if(n!==void 0){let i=this.views,r=n+e.removeCount;for(let o=i.length-1;o>=0;o--){let a=i[o];if((o<n||o>=r)&&a!==t)return a}}}postViewInit(e,t,s){var n,i,r;l(t||e,"Both leavingView and enteringView are null"),l(s.resolve,"resolve must be valid"),l(s.reject,"reject must be valid");let o=s.opts,{insertViews:a,removeStart:d,removeCount:h}=s,u;if(d!==void 0&&h!==void 0){l(d>=0,"removeStart can not be negative"),l(h>=0,"removeCount can not be negative"),u=[];for(let f=d;f<d+h;f++){let m=this.views[f];m!==void 0&&m!==e&&m!==t&&u.push(m)}(n=o.direction)!==null&&n!==void 0||(o.direction="back")}let p=this.views.length+((i=a?.length)!==null&&i!==void 0?i:0)-(h??0);if(l(p>=0,"final balance can not be negative"),p===0)throw O("[ion-nav] - You can't remove all the pages in the navigation stack. nav.pop() is probably called too many times.",this,this.el),new Error("navigation stack needs at least one root page");if(a){let f=s.insertStart;for(let m of a)this.insertViewAt(m,f),f++;s.enteringRequiresTransition&&((r=o.direction)!==null&&r!==void 0||(o.direction="forward"))}if(u&&u.length>0){for(let f of u)g(f.element,L),g(f.element,P),g(f.element,y);for(let f of u)this.destroyView(f)}}transition(e,t,s){return v(this,null,function*(){let n=s.opts,i=n.progressAnimation?u=>{u!==void 0&&!this.gestureOrAnimationInProgress?(this.gestureOrAnimationInProgress=!0,u.onFinish(()=>{this.gestureOrAnimationInProgress=!1},{oneTimeCallback:!0}),u.progressEnd(0,0,0)):this.sbAni=u}:void 0,r=I(this),o=e.element,a=t&&t.element,d=Object.assign(Object.assign({mode:r,showGoBack:this.canGoBackSync(e),baseEl:this.el,progressCallback:i,animated:this.animated&&b.getBoolean("animated",!0),enteringEl:o,leavingEl:a},n),{animationBuilder:n.animationBuilder||this.animation||b.get("navAnimation")}),{hasCompleted:h}=yield _(d);return this.transitionFinish(h,e,t,n)})}transitionFinish(e,t,s,n){let i=e?t:s;return i&&this.unmountInactiveViews(i),{hasCompleted:e,requiresTransition:!0,enteringView:t,leavingView:s,direction:n.direction}}insertViewAt(e,t){let s=this.views,n=s.indexOf(e);n>-1?(l(e.nav===this,"view is not part of the nav"),s.splice(n,1),s.splice(t,0,e)):(l(!e.nav,"nav is used"),e.nav=this,s.splice(t,0,e))}removeView(e){l(e.state===j||e.state===T,"view state should be loaded or destroyed");let t=this.views,s=t.indexOf(e);l(s>-1,"view must be part of the stack"),s>=0&&t.splice(s,1)}destroyView(e){e._destroy(),this.removeView(e)}unmountInactiveViews(e){if(this.destroyed)return;let t=this.views,s=t.indexOf(e);for(let n=t.length-1;n>=0;n--){let i=t[n],r=i.element;r&&(n>s?(g(r,y),this.destroyView(i)):n<s&&R(r,!0))}}canStart(){return!this.gestureOrAnimationInProgress&&!!this.swipeGesture&&!this.isTransitioning&&this.transInstr.length===0&&this.canGoBackSync()}onStart(){this.gestureOrAnimationInProgress=!0,this.pop({direction:"back",progressAnimation:!0})}onMove(e){this.sbAni&&this.sbAni.progressStep(e)}onEnd(e,t,s){if(this.sbAni){this.sbAni.onFinish(()=>{this.gestureOrAnimationInProgress=!1},{oneTimeCallback:!0});let n=e?-.001:.001;e?n+=E([0,0],[.32,.72],[0,1],[1,1],t)[0]:(this.sbAni.easing("cubic-bezier(1, 0, 0.68, 0.28)"),n+=E([0,0],[1,0],[.68,.28],[1,1],t)[0]),this.sbAni.progressEnd(e?1:0,n,s)}else this.gestureOrAnimationInProgress=!1}render(){return C("slot",{key:"8067c9835d255daec61f33dba200fd3a6ff839a0"})}get el(){return A(this)}static get watchers(){return{swipeGesture:["swipeGestureChanged"],root:["rootChanged"]}}};return c.style=W,c})(),F=(c,e,t,s,n)=>{let i=c.closest("ion-nav");if(i){if(e==="forward"){if(t!==void 0)return i.push(t,s,{skipIfBusy:!0,animationBuilder:n})}else if(e==="root"){if(t!==void 0)return i.setRoot(t,s,{skipIfBusy:!0,animationBuilder:n})}else if(e==="back")return i.pop({skipIfBusy:!0,animationBuilder:n})}return Promise.resolve(!1)},J=class{constructor(c){k(this,c),this.routerDirection="forward",this.onClick=()=>F(this.el,this.routerDirection,this.component,this.componentProps,this.routerAnimation)}render(){return C(q,{key:"6dbb1ad4f351e9215375aac11ab9b53762e07a08",onClick:this.onClick})}get el(){return A(this)}};export{Q as ion_nav,J as ion_nav_link};
