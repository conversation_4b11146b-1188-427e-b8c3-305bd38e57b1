import{c as T,d as U,g as A,h as D,i as V,j as N}from"./chunk-X3KNIMLA.js";import"./chunk-5ANC4TAO.js";import"./chunk-QUJFQN2Y.js";import"./chunk-7HYD6CC7.js";import"./chunk-5VDVK6VQ.js";import"./chunk-423VJ54E.js";import"./chunk-7FY2OE2O.js";import"./chunk-RC522WYB.js";import"./chunk-LMF7SRCC.js";import"./chunk-AWU72VOH.js";import"./chunk-W5XPJHSR.js";import"./chunk-3AW3VJFF.js";import"./chunk-MTHZ7MWU.js";import"./chunk-WI5MSH4N.js";import"./chunk-UT7HCVIF.js";import"./chunk-CKP3SGE2.js";import"./chunk-I4XHNRTT.js";import{$ as C,A as u,E as h,I as m,J as o,K as c,L as O,P as w,Q as P,R as y,_ as r,_a as E,aa as v,bb as F,ca as k,da as z,db as L,ea as S,i as p,ja as j,k as b,kb as Q,ma as I,o as f,p as x,pa as Y,x as g,y as d}from"./chunk-XPS4RP6N.js";import"./chunk-YJ7VB3TT.js";import"./chunk-QDU6VP7L.js";import"./chunk-ICSGBKZQ.js";import"./chunk-YSN7XVTD.js";import"./chunk-R5HL6L5F.js";import"./chunk-M2X7KQLB.js";import"./chunk-REYR55MP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-42C7ZIID.js";import{e as s}from"./chunk-JHI3MBHO.js";var $=(()=>{let t=class t{constructor(n){this.http=n,this.apiUrl="http://localhost:8080/api/v1/stations"}getStations(){return this.http.get(this.apiUrl)}addStation(n){return this.http.post(this.apiUrl,n)}deleteStation(n){return this.http.delete(`${this.apiUrl}/${n}`,{responseType:"text"})}};t.\u0275fac=function(e){return new(e||t)(b(Y))},t.\u0275prov=p({token:t,factory:t.\u0275fac,providedIn:"root"});let i=t;return i})();function B(i,t){if(i&1){let l=w();o(0,"tr")(1,"td"),r(2),c(),o(3,"td"),r(4),c(),o(5,"td"),r(6),c(),o(7,"td")(8,"div",15)(9,"button",16),P("click",function(){let e=f(l).$implicit,a=y();return x(a.deleteStation(e))}),O(10,"ion-icon",17),c()()()()}if(i&2){let l=t.$implicit;g(2),C(l.nom),g(2),C(l.ville),g(2),C(l.adresse)}}var nn=(()=>{let t=class t{constructor(n,e,a,_){this.stationService=n,this.alertController=e,this.toastController=a,this.modalController=_,this.searchQuery="",this.filteredStations=[],this.stations=[]}ngOnInit(){this.onSearch(),this.loadStation()}applyFilters(){this.filteredStations=this.stations.filter(n=>!this.searchQuery||n.nom.toLowerCase().includes(this.searchQuery.toLowerCase())||n.ville.toLowerCase().includes(this.searchQuery.toLowerCase())||n.adresse.toLowerCase().includes(this.searchQuery.toLowerCase()))}onSearch(){this.applyFilters()}loadStation(){this.stationService.getStations().subscribe({next:n=>{this.stations=n,this.filteredStations=[...this.stations]},error:n=>{console.error("Error loading stations:",n)}})}deleteStation(n){return s(this,null,function*(){yield(yield this.alertController.create({header:"Confirmer la suppression",message:`\xCAtes-vous s\xFBr de vouloir supprimer la station "${n.nom}" situ\xE9e \xE0 ${n.ville} ?`,buttons:[{text:"Annuler",role:"cancel"},{text:"Supprimer",role:"destructive",handler:()=>{this.performDeleteStation(n)}}]})).present()})}performDeleteStation(n){return s(this,null,function*(){this.stationService.deleteStation(n.id).subscribe({next:()=>s(this,null,function*(){this.stations=this.stations.filter(a=>a.id!==n.id),this.applyFilters(),yield(yield this.toastController.create({message:"Station supprim\xE9e avec succ\xE8s.",duration:2e3,color:"success"})).present()}),error:e=>s(this,null,function*(){console.error("Erreur lors de la suppression de la station :",e),yield(yield this.toastController.create({message:"Erreur lors de la suppression.",duration:2e3,color:"danger"})).present()})})})}};t.\u0275fac=function(e){return new(e||t)(d($),d(A),d(V),d(D))},t.\u0275cmp=u({type:t,selectors:[["app-stations"]],decls:30,vars:3,consts:[["fullscreen",""],[1,"page-header"],[1,"header-content"],[1,"title-section"],[1,"page-title"],[1,"page-subtitle"],[1,"filters-section"],[1,"search-container"],["name","search-outline"],["type","text","placeholder","Rechercher par nom, ville ou Adresse.",1,"search-input",3,"ngModelChange","input","ngModel"],[1,"table-container"],[1,"table-header"],[1,"table-wrapper"],[1,"clients-table"],[4,"ngFor","ngForOf"],[1,"action-buttons"],["title","Supprimer",1,"action-btn-small","delete",3,"click"],["name","trash-outline"]],template:function(e,a){e&1&&(o(0,"ion-content",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"h1",4),r(5,"Gestion des Stations"),c(),o(6,"p",5),r(7,"G\xE9rez et suivez tous vos Stations"),c()()()(),o(8,"div",6)(9,"div",7),O(10,"ion-icon",8),o(11,"input",9),S("ngModelChange",function(M){return z(a.searchQuery,M)||(a.searchQuery=M),M}),P("input",function(){return a.onSearch()}),c()()(),o(12,"div",10)(13,"div",11)(14,"h3"),r(15),c()(),o(16,"div",12)(17,"table",13)(18,"thead")(19,"tr")(20,"th"),r(21,"Nom"),c(),o(22,"th"),r(23,"Ville"),c(),o(24,"th"),r(25,"Adresse"),c(),o(26,"th"),r(27,"Actions"),c()()(),o(28,"tbody"),h(29,B,11,3,"tr",14),c()()()()()),e&2&&(g(11),k("ngModel",a.searchQuery),g(4),v("Liste des Stations (",a.filteredStations.length,")"),g(14),m("ngForOf",a.filteredStations))},dependencies:[N,T,U,I,j,Q,E,F,L],styles:['.clients-container[_ngcontent-%COMP%], .chauffeurs-container[_ngcontent-%COMP%]{padding:0;background:transparent;min-height:100vh}.page-header[_ngcontent-%COMP%]{margin-bottom:32px}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;gap:24px}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .title-section[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%]{font-size:32px;font-weight:800;color:#c8102e;margin:0 0 8px;text-shadow:0 2px 4px rgba(0,0,0,.1)}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .title-section[_ngcontent-%COMP%]   .page-subtitle[_ngcontent-%COMP%]{font-size:16px;color:#fffc;margin:0;font-weight:400}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]{display:flex;gap:12px}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{padding:12px 20px;border:none;border-radius:12px;font-size:14px;font-weight:600;cursor:pointer;transition:all .3s ease;display:flex;align-items:center;gap:8px}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn.primary[_ngcontent-%COMP%]{background:#fff3;color:#fff;border:1px solid rgba(255,255,255,.3);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn.primary[_ngcontent-%COMP%]:hover{background:#ffffff4d;transform:translateY(-2px);box-shadow:0 8px 25px #00000026}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn.secondary[_ngcontent-%COMP%]{background:#ffffff1a;color:#fff;border:1px solid rgba(255,255,255,.2)}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn.secondary[_ngcontent-%COMP%]:hover{background:#fff3;transform:translateY(-1px)}.stats-row[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:20px;margin-bottom:32px}.stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]{background:#fffffff2;-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border-radius:16px;padding:20px;display:flex;align-items:center;gap:16px;box-shadow:0 8px 32px #0000001a;border:1px solid rgba(255,255,255,.2);transition:all .3s ease}.stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 12px 40px #00000026}.stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]{width:48px;height:48px;border-radius:12px;display:flex;align-items:center;justify-content:center;box-shadow:0 4px 12px #0000001a}.stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;color:#fff}.stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-icon.clients[_ngcontent-%COMP%]{background:linear-gradient(135deg,#c8102e,#a00d26)}.stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-icon.active[_ngcontent-%COMP%]{background:linear-gradient(135deg,#43e97b,#38f9d7)}.stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-icon.new[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f093fb,#f5576c)}.stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-icon.rating[_ngcontent-%COMP%]{background:linear-gradient(135deg,#fbbf24,#f59e0b)}.stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:24px;font-weight:700;color:#2d3748;margin:0 0 4px;line-height:1}.stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:14px;color:#718096;margin:0;font-weight:500}.filters-section[_ngcontent-%COMP%]{background:#fffffff2;-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border-radius:16px;padding:24px;margin-bottom:24px;box-shadow:0 8px 32px #0000001a;border:1px solid rgba(255,255,255,.2)}.filters-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]{display:flex;align-items:center;background:#fffc;border-radius:12px;padding:12px 16px;border:1px solid rgba(200,16,46,.2);margin-bottom:20px;transition:all .3s ease}.filters-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]:focus-within{border-color:#c8102e;box-shadow:0 0 0 3px #c8102e1a}.filters-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#a0aec0;margin-right:12px;font-size:20px}.filters-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%]{border:none;outline:none;background:transparent;flex:1;color:#4a5568;font-size:14px}.filters-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%]::placeholder{color:#a0aec0}.filters-section[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]{display:flex;gap:16px;align-items:center;flex-wrap:wrap}.filters-section[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filter-select[_ngcontent-%COMP%]{padding:10px 16px;border:1px solid rgba(200,16,46,.2);border-radius:8px;background:#fff;color:#4a5568;font-size:14px;cursor:pointer;transition:all .3s ease}.filters-section[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filter-select[_ngcontent-%COMP%]:focus{outline:none;border-color:#c8102e;box-shadow:0 0 0 3px #c8102e1a}.filters-section[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filter-btn[_ngcontent-%COMP%]{padding:10px 16px;background:#c8102e1a;border:1px solid rgba(200,16,46,.2);border-radius:8px;color:#c8102e;font-size:14px;font-weight:600;cursor:pointer;transition:all .3s ease;display:flex;align-items:center;gap:8px}.filters-section[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filter-btn[_ngcontent-%COMP%]:hover{background:#c8102e26;transform:translateY(-1px)}.filters-section[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filter-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.table-container[_ngcontent-%COMP%]{background:#fffffff2;-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border-radius:16px;box-shadow:0 8px 32px #0000001a;border:1px solid rgba(255,255,255,.2);overflow:hidden}.table-container[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:24px;border-bottom:1px solid rgba(0,0,0,.05)}.table-container[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:18px;font-weight:700;color:#2d3748;margin:0}.table-container[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .table-actions[_ngcontent-%COMP%]   .table-btn[_ngcontent-%COMP%]{padding:8px;background:#c8102e1a;border:1px solid rgba(200,16,46,.2);border-radius:8px;color:#c8102e;cursor:pointer;transition:all .3s ease}.table-container[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .table-actions[_ngcontent-%COMP%]   .table-btn[_ngcontent-%COMP%]:hover{background:#c8102e26;transform:translateY(-1px)}.table-container[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .table-actions[_ngcontent-%COMP%]   .table-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]{overflow-x:auto}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]{width:100%;border-collapse:collapse}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]{background:#c8102e0d}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{padding:16px;text-align:left;font-weight:600;color:#4a5568;font-size:14px;border-bottom:1px solid rgba(0,0,0,.05);white-space:nowrap}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%]{cursor:pointer;-webkit-user-select:none;user-select:none;transition:all .3s ease}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%]:hover{background:#c8102e1a}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-left:4px;font-size:12px;opacity:.6}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]{transition:all .3s ease}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover{background:#c8102e05}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr.selected[_ngcontent-%COMP%]{background:#c8102e0d}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{padding:16px;border-bottom:1px solid rgba(0,0,0,.05);vertical-align:middle}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .client-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .client-info[_ngcontent-%COMP%]   .client-avatar[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;object-fit:cover;border:2px solid #c8102e}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .client-info[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:#2d3748;margin:0 0 2px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .client-info[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:12px;color:#718096;margin:0}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .contact-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:13px;color:#4a5568;margin:0 0 2px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .contact-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child{margin:0}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .trips-count[_ngcontent-%COMP%]{background:#c8102e1a;color:#c8102e;padding:4px 8px;border-radius:6px;font-size:12px;font-weight:600}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .rating[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .rating[_ngcontent-%COMP%]   .star[_ngcontent-%COMP%]{font-size:14px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .rating[_ngcontent-%COMP%]   .star.filled[_ngcontent-%COMP%]{color:#fbbf24}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .rating[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:13px;font-weight:600;color:#4a5568}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .no-rating[_ngcontent-%COMP%]{color:#a0aec0;font-style:italic}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%]{padding:4px 12px;border-radius:20px;font-size:12px;font-weight:600;text-transform:capitalize}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .status-badge.actif[_ngcontent-%COMP%]{background:#38b2ac1a;color:#38b2ac}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .status-badge.inactif[_ngcontent-%COMP%]{background:#a0aec01a;color:#a0aec0}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .status-badge.suspendu[_ngcontent-%COMP%]{background:#f565651a;color:#f56565}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]{display:flex;gap:8px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn-small[_ngcontent-%COMP%]{width:32px;height:32px;border:none;border-radius:6px;cursor:pointer;display:flex;align-items:center;justify-content:center;transition:all .3s ease}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn-small[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn-small.view[_ngcontent-%COMP%]{background:#c8102e1a;color:#c8102e}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn-small.view[_ngcontent-%COMP%]:hover{background:#c8102e33;transform:translateY(-1px)}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn-small.edit[_ngcontent-%COMP%]{background:#38b2ac1a;color:#38b2ac}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn-small.edit[_ngcontent-%COMP%]:hover{background:#38b2ac33;transform:translateY(-1px)}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn-small.delete[_ngcontent-%COMP%]{background:#f565651a;color:#f56565}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn-small.delete[_ngcontent-%COMP%]:hover{background:#f5656533;transform:translateY(-1px)}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(300px,1fr));gap:20px;padding:24px}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]{background:#fff;border-radius:16px;overflow:hidden;box-shadow:0 4px 16px #00000014;border:1px solid rgba(0,0,0,.05);transition:all .3s ease}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 8px 32px #0000001f}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]{position:relative;padding:20px;background:linear-gradient(135deg,#c8102e,#a00d26);text-align:center}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .client-photo[_ngcontent-%COMP%]{width:80px;height:80px;border-radius:50%;object-fit:cover;border:4px solid white;box-shadow:0 4px 16px #0000001a}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .client-status[_ngcontent-%COMP%]{position:absolute;top:16px;right:16px}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .client-status[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%]{padding:4px 12px;border-radius:20px;font-size:12px;font-weight:600;text-transform:capitalize;background:#fff3;color:#fff;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]{padding:20px}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:18px;font-weight:700;color:#2d3748;margin:0 0 12px;text-align:center}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-email[_ngcontent-%COMP%], .table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-phone[_ngcontent-%COMP%], .table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-city[_ngcontent-%COMP%]{font-size:14px;color:#718096;margin:0 0 8px;display:flex;align-items:center;justify-content:center;gap:8px}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-email[_ngcontent-%COMP%]:before, .table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-phone[_ngcontent-%COMP%]:before, .table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-city[_ngcontent-%COMP%]:before{content:"";width:4px;height:4px;background:#cbd5e0;border-radius:50%}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-stats[_ngcontent-%COMP%]{display:flex;justify-content:space-around;margin-top:16px;padding-top:16px;border-top:1px solid rgba(0,0,0,.05)}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-stats[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]{text-align:center}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-stats[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%]{display:block;font-size:20px;font-weight:700;color:#c8102e;margin-bottom:4px}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-stats[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%]{font-size:12px;color:#a0aec0;font-weight:500}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]{display:flex;justify-content:center;gap:12px;padding:16px 20px 20px}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-btn[_ngcontent-%COMP%]{width:36px;height:36px;border:none;border-radius:8px;cursor:pointer;display:flex;align-items:center;justify-content:center;transition:all .3s ease}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-btn.view[_ngcontent-%COMP%]{background:#c8102e1a;color:#c8102e}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-btn.view[_ngcontent-%COMP%]:hover{background:#c8102e33;transform:translateY(-2px)}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-btn.edit[_ngcontent-%COMP%]{background:#38b2ac1a;color:#38b2ac}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-btn.edit[_ngcontent-%COMP%]:hover{background:#38b2ac33;transform:translateY(-2px)}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-btn.delete[_ngcontent-%COMP%]{background:#f565651a;color:#f56565}.table-container[_ngcontent-%COMP%]   .grid-view[_ngcontent-%COMP%]   .client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-btn.delete[_ngcontent-%COMP%]:hover{background:#f5656533;transform:translateY(-2px)}.table-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;gap:8px;padding:24px;border-top:1px solid rgba(0,0,0,.05)}.table-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-btn[_ngcontent-%COMP%]{width:40px;height:40px;border:1px solid rgba(200,16,46,.2);background:#fff;border-radius:8px;cursor:pointer;display:flex;align-items:center;justify-content:center;transition:all .3s ease;color:#c8102e}.table-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-btn[_ngcontent-%COMP%]:hover:not(:disabled){background:#c8102e1a;transform:translateY(-1px)}.table-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-btn[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed}.table-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.table-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-numbers[_ngcontent-%COMP%]{display:flex;gap:4px}.table-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-numbers[_ngcontent-%COMP%]   .page-number[_ngcontent-%COMP%]{width:40px;height:40px;border:1px solid rgba(200,16,46,.2);background:#fff;border-radius:8px;cursor:pointer;display:flex;align-items:center;justify-content:center;transition:all .3s ease;color:#c8102e;font-weight:600;font-size:14px}.table-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-numbers[_ngcontent-%COMP%]   .page-number[_ngcontent-%COMP%]:hover{background:#c8102e1a;transform:translateY(-1px)}.table-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-numbers[_ngcontent-%COMP%]   .page-number.active[_ngcontent-%COMP%]{background:#c8102e;color:#fff;border-color:#c8102e}@media (max-width: 1200px){.stats-row[_ngcontent-%COMP%]{grid-template-columns:repeat(2,1fr)}.grid-view[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fill,minmax(280px,1fr))}}@media (max-width: 768px){.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch;gap:16px}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .title-section[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%]{font-size:24px}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .title-section[_ngcontent-%COMP%]   .page-subtitle[_ngcontent-%COMP%]{font-size:14px}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]{justify-content:center}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{flex:1;justify-content:center}.stats-row[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:16px}.filters-section[_ngcontent-%COMP%]{padding:16px}.filters-section[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch}.filters-section[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filter-select[_ngcontent-%COMP%], .filters-section[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filter-btn[_ngcontent-%COMP%]{width:100%}.table-container[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]{padding:16px;flex-direction:column;gap:12px;align-items:stretch}.table-container[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .table-actions[_ngcontent-%COMP%]{display:flex;justify-content:center}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]{font-size:12px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{padding:8px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .client-info[_ngcontent-%COMP%]   .client-avatar[_ngcontent-%COMP%]{width:32px;height:32px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .client-info[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:12px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .client-info[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:10px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn-small[_ngcontent-%COMP%]{width:28px;height:28px}.table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]   .clients-table[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn-small[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:14px}.grid-view[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:16px;padding:16px}.pagination[_ngcontent-%COMP%]{padding:16px;flex-wrap:wrap}.pagination[_ngcontent-%COMP%]   .page-numbers[_ngcontent-%COMP%]{order:-1;width:100%;justify-content:center;margin-bottom:12px}}@media (max-width: 480px){.clients-container[_ngcontent-%COMP%], .chauffeurs-container[_ngcontent-%COMP%]{padding:0}.filters-section[_ngcontent-%COMP%]{margin:0 0 16px;border-radius:12px}.table-container[_ngcontent-%COMP%]{border-radius:12px}.client-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]{padding:16px}.client-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .client-photo[_ngcontent-%COMP%]{width:60px;height:60px}.client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]{padding:16px}.client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:16px}.client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-email[_ngcontent-%COMP%], .client-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .client-phone[_ngcontent-%COMP%]{font-size:12px}.client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]{padding:12px 16px 16px}.client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-btn[_ngcontent-%COMP%]{width:32px;height:32px}.client-card[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.page-numbers[_ngcontent-%COMP%]   .page-number[_ngcontent-%COMP%]{width:36px;height:36px;font-size:12px}.page-btn[_ngcontent-%COMP%]{width:36px;height:36px}.page-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}.stat-card[_ngcontent-%COMP%], .client-card[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .6s ease forwards}.clients-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .4s ease forwards}.loading[_ngcontent-%COMP%]{opacity:.6;pointer-events:none}.table-wrapper[_ngcontent-%COMP%]::-webkit-scrollbar{height:8px}.table-wrapper[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#0000000d;border-radius:4px}.table-wrapper[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#c8102e4d;border-radius:4px}.table-wrapper[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#c8102e80}.stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-icon.chauffeurs[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2)}.stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-icon.pending[_ngcontent-%COMP%]{background:linear-gradient(135deg,#fbbf24,#f59e0b)}.vehicle-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:#2d3748;margin:0 0 4px}.vehicle-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:12px;color:#718096;margin:0 0 2px}.vehicle-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-of-type{margin-bottom:8px}.vehicle-info[_ngcontent-%COMP%]   .capacity-info[_ngcontent-%COMP%]{font-size:11px;color:var(--ion-color-primary);background:var(--ion-color-primary-tint);padding:2px 6px;border-radius:8px;font-weight:500;display:inline-block}.vehicle-info[_ngcontent-%COMP%]   .no-vehicle[_ngcontent-%COMP%]{font-size:11px;color:var(--ion-color-warning);font-style:italic;display:block;margin-top:4px}.vehicle-info[_ngcontent-%COMP%]   .places-badge[_ngcontent-%COMP%]{background:#667eea1a;color:#667eea;padding:2px 8px;border-radius:6px;font-size:11px;font-weight:600}.document-badge[_ngcontent-%COMP%]{padding:4px 12px;border-radius:20px;font-size:12px;font-weight:600;text-transform:capitalize}.document-badge.verified[_ngcontent-%COMP%]{background:#38b2ac1a;color:#38b2ac}.document-badge.pending[_ngcontent-%COMP%]{background:#f565651a;color:#f56565}.chauffeur-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.chauffeur-info[_ngcontent-%COMP%]   .chauffeur-avatar[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;object-fit:cover;border:2px solid #667eea}.chauffeur-info[_ngcontent-%COMP%]   .chauffeur-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:#2d3748;margin:0 0 2px}.chauffeur-info[_ngcontent-%COMP%]   .chauffeur-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:11px;color:#718096;margin:0 0 1px}.chauffeur-info[_ngcontent-%COMP%]   .chauffeur-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child{margin:0}.action-btn-small.validate[_ngcontent-%COMP%]{background:#38b2ac1a;color:#38b2ac}.action-btn-small.validate[_ngcontent-%COMP%]:hover{background:#38b2ac33;transform:translateY(-1px)}.card-btn.validate[_ngcontent-%COMP%]{background:#38b2ac1a;color:#38b2ac}.card-btn.validate[_ngcontent-%COMP%]:hover{background:#38b2ac33;transform:translateY(-2px)}.chauffeur-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .chauffeur-photo[_ngcontent-%COMP%]{width:80px;height:80px;border-radius:50%;object-fit:cover;border:4px solid white;box-shadow:0 4px 16px #0000001a}.chauffeur-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .chauffeur-status[_ngcontent-%COMP%]{position:absolute;top:16px;right:16px;display:flex;flex-direction:column;gap:8px}.chauffeur-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .chauffeur-status[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%], .chauffeur-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .chauffeur-status[_ngcontent-%COMP%]   .document-badge[_ngcontent-%COMP%]{padding:4px 12px;border-radius:20px;font-size:12px;font-weight:600;text-transform:capitalize;background:#fff3;color:#fff;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);text-align:center}.chauffeur-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .vehicle-summary[_ngcontent-%COMP%]{background:#667eea0d;border-radius:8px;padding:12px;margin:16px 0}.chauffeur-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .vehicle-summary[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:#667eea;margin:0 0 4px}.chauffeur-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .vehicle-summary[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:12px;color:#718096;margin:0 0 8px}.chauffeur-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .vehicle-summary[_ngcontent-%COMP%]   .places-info[_ngcontent-%COMP%]{background:#667eea1a;color:#667eea;padding:4px 8px;border-radius:6px;font-size:11px;font-weight:600}.chauffeur-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .chauffeur-email[_ngcontent-%COMP%], .chauffeur-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .chauffeur-phone[_ngcontent-%COMP%], .chauffeur-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .chauffeur-city[_ngcontent-%COMP%]{font-size:14px;color:#718096;margin:0 0 8px;display:flex;align-items:center;justify-content:center;gap:8px}.chauffeur-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .chauffeur-email[_ngcontent-%COMP%]:before, .chauffeur-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .chauffeur-phone[_ngcontent-%COMP%]:before, .chauffeur-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .chauffeur-city[_ngcontent-%COMP%]:before{content:"";width:4px;height:4px;background:#cbd5e0;border-radius:50%}.chauffeur-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .chauffeur-stats[_ngcontent-%COMP%]{display:flex;justify-content:space-around;margin-top:16px;padding-top:16px;border-top:1px solid rgba(0,0,0,.05)}.chauffeur-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .chauffeur-stats[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]{text-align:center}.chauffeur-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .chauffeur-stats[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%]{display:block;font-size:20px;font-weight:700;color:#667eea;margin-bottom:4px}.chauffeur-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .chauffeur-stats[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%]{font-size:12px;color:#a0aec0;font-weight:500}.status-badge.en_attente_validation[_ngcontent-%COMP%]{background:#fbbf241a;color:#f59e0b}.chauffeurs-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(4){min-width:180px}.chauffeurs-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(9){min-width:120px}.chauffeurs-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(11){min-width:140px}']});let i=t;return i})();export{nn as StationsComponent};
