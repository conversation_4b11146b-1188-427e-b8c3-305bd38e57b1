import{f as r,g as i}from"./chunk-3RL6K3Q3.js";import{b as s}from"./chunk-3AW3VJFF.js";import{j as a,k as c}from"./chunk-I4XHNRTT.js";import{e as n}from"./chunk-JHI3MBHO.js";var h=()=>{let e=window;e.addEventListener("statusTap",()=>{a(()=>{let m=e.innerWidth,d=e.innerHeight,o=document.elementFromPoint(m/2,d/2);if(!o)return;let t=r(o);t&&new Promise(f=>s(t,f)).then(()=>{c(()=>n(null,null,function*(){t.style.setProperty("--overflow","hidden"),yield i(t,300),t.style.removeProperty("--overflow")}))})})})};export{h as startStatusTap};
