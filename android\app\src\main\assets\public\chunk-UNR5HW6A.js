import{a as at}from"./chunk-QUJFQN2Y.js";import{h as At,i as Dt,k as Ct,l as Et,m as st,p as Bt,q as V,r as Mt,t as Tt,u as X}from"./chunk-5VDVK6VQ.js";import{a as wt}from"./chunk-EAO2GMQP.js";import{a as xt,b as vt,c as St}from"./chunk-423VJ54E.js";import{c as Pt}from"./chunk-7FY2OE2O.js";import{f as Lt,g as Ot}from"./chunk-AWU72VOH.js";import{a as u}from"./chunk-W5XPJHSR.js";import{c as W,e as Yt,f as tt,i as It,j as rt,k as Rt}from"./chunk-3RL6K3Q3.js";import{a as dt}from"./chunk-ILQCMFU7.js";import"./chunk-XCHF2ADM.js";import{a as kt}from"./chunk-FPOZYJOD.js";import{c as bt,d as yt,h as Y,i as z,m as j}from"./chunk-3AW3VJFF.js";import{a as it}from"./chunk-MTHZ7MWU.js";import"./chunk-WI5MSH4N.js";import"./chunk-UT7HCVIF.js";import{a as F}from"./chunk-CKP3SGE2.js";import{a as lt,b as et,f as U,i as _t,k as ct,m as N,n as Ht,o as Nt,p as I}from"./chunk-I4XHNRTT.js";import{e as P}from"./chunk-JHI3MBHO.js";var Z=function(t){return t.Dark="DARK",t.Light="LIGHT",t.Default="DEFAULT",t}(Z||{}),mt={getEngine(){let t=kt();if(t?.isPluginAvailable("StatusBar"))return t.Plugins.StatusBar},setStyle(t){let e=this.getEngine();e&&e.setStyle(t)},getStyle:function(){return P(this,null,function*(){let t=this.getEngine();if(!t)return Z.Default;let{style:e}=yield t.getInfo();return e})}},pt=(t,e)=>{if(e===1)return 0;let n=1/(1-e),o=-(e*n);return t*n+o},Gt=()=>{!F||F.innerWidth>=768||mt.setStyle({style:Z.Dark})},ht=(t=Z.Default)=>{!F||F.innerWidth>=768||mt.setStyle({style:t})},Kt=(t,e)=>P(null,null,function*(){typeof t.canDismiss!="function"||!(yield t.canDismiss(void 0,V))||(e.isRunning()?e.onFinish(()=>{t.dismiss(void 0,"handler")},{oneTimeCallback:!0}):t.dismiss(void 0,"handler"))}),ft=t=>.00255275*2.71828**(-14.9619*t)-1.00255*2.71828**(-.0380968*t)+1,nt={MIN_PRESENTING_SCALE:.915},Jt=(t,e,n,o)=>{let i=t.offsetHeight,a=!1,s=!1,d=null,b=null,D=.2,w=!0,S=0,m=()=>d&&W(d)?d.scrollY:!0,L=it({el:t,gestureName:"modalSwipeToClose",gesturePriority:Mt,direction:"y",threshold:10,canStart:x=>{let g=x.event.target;return g===null||!g.closest?!0:(d=tt(g),d?(W(d)?b=Y(d).querySelector(".inner-scroll"):b=d,!!!d.querySelector("ion-refresher")&&b.scrollTop===0):g.closest("ion-footer")===null)},onStart:x=>{let{deltaY:g}=x;w=m(),s=t.canDismiss!==void 0&&t.canDismiss!==!0,g>0&&d&&rt(d),e.progressStart(!0,a?1:0)},onMove:x=>{let{deltaY:g}=x;g>0&&d&&rt(d);let v=x.deltaY/i,C=v>=0&&s,O=C?D:.9999,q=C?ft(v/O):v,M=j(1e-4,q,O);e.progressStep(M),M>=.5&&S<.5?ht(n):M<.5&&S>=.5&&Gt(),S=M},onEnd:x=>{let g=x.velocityY,v=x.deltaY/i,C=v>=0&&s,O=C?D:.9999,q=C?ft(v/O):v,M=j(1e-4,q,O),_=(x.deltaY+g*1e3)/i,A=!C&&_>=.5,B=A?-.001:.001;A?(e.easing("cubic-bezier(0.32, 0.72, 0, 1)"),B+=at([0,0],[.32,.72],[0,1],[1,1],M)[0]):(e.easing("cubic-bezier(1, 0, 0.68, 0.28)"),B+=at([0,0],[1,0],[.68,.28],[1,1],M)[0]);let J=$t(A?v*i:(1-M)*i,g);a=A,L.enable(!1),d&&Rt(d,w),e.onFinish(()=>{A||L.enable(!0)}).progressEnd(A?1:0,B,J),C&&M>O/4?Kt(t,e):A&&o()}});return L},$t=(t,e)=>j(400,t/Math.abs(e*1.1),500),zt=t=>{let{currentBreakpoint:e,backdropBreakpoint:n,expandToScroll:o}=t,r=n===void 0||n<e,i=r?`calc(var(--backdrop-opacity) * ${e})`:"0",a=u("backdropAnimation").fromTo("opacity",0,i);r&&a.beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]);let s=u("wrapperAnimation").keyframes([{offset:0,opacity:1,transform:"translateY(100%)"},{offset:1,opacity:1,transform:`translateY(${100-e*100}%)`}]),d=o?void 0:u("contentAnimation").keyframes([{offset:0,opacity:1,maxHeight:`${(1-e)*100}%`},{offset:1,opacity:1,maxHeight:`${e*100}%`}]);return{wrapperAnimation:s,backdropAnimation:a,contentAnimation:d}},jt=t=>{let{currentBreakpoint:e,backdropBreakpoint:n}=t,o=`calc(var(--backdrop-opacity) * ${pt(e,n)})`,r=[{offset:0,opacity:o},{offset:1,opacity:0}],i=[{offset:0,opacity:o},{offset:n,opacity:0},{offset:1,opacity:0}],a=u("backdropAnimation").keyframes(n!==0?i:r);return{wrapperAnimation:u("wrapperAnimation").keyframes([{offset:0,opacity:1,transform:`translateY(${100-e*100}%)`},{offset:1,opacity:1,transform:"translateY(100%)"}]),backdropAnimation:a}},Qt=()=>{let t=u().fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),e=u().fromTo("transform","translateY(100vh)","translateY(0vh)");return{backdropAnimation:t,wrapperAnimation:e,contentAnimation:void 0}},Wt=(t,e)=>{let{presentingEl:n,currentBreakpoint:o,expandToScroll:r}=e,i=Y(t),{wrapperAnimation:a,backdropAnimation:s,contentAnimation:d}=o!==void 0?zt(e):Qt();s.addElement(i.querySelector("ion-backdrop")),a.addElement(i.querySelectorAll(".modal-wrapper, .modal-shadow")).beforeStyles({opacity:1}),!r&&d?.addElement(t.querySelector(".ion-page"));let b=u("entering-base").addElement(t).easing("cubic-bezier(0.32,0.72,0,1)").duration(500).addAnimation([a]);if(d&&b.addAnimation(d),n){let D=window.innerWidth<768,w=n.tagName==="ION-MODAL"&&n.presentingElement!==void 0,S=Y(n),m=u().beforeStyles({transform:"translateY(0)","transform-origin":"top center",overflow:"hidden"}),y=document.body;if(D){let E=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px",p=w?"-10px":E,R=nt.MIN_PRESENTING_SCALE,L=`translateY(${p}) scale(${R})`;m.afterStyles({transform:L}).beforeAddWrite(()=>y.style.setProperty("background-color","black")).addElement(n).keyframes([{offset:0,filter:"contrast(1)",transform:"translateY(0px) scale(1)",borderRadius:"0px"},{offset:1,filter:"contrast(0.85)",transform:L,borderRadius:"10px 10px 0 0"}]),b.addAnimation(m)}else if(b.addAnimation(s),!w)a.fromTo("opacity","0","1");else{let p=`translateY(-10px) scale(${w?nt.MIN_PRESENTING_SCALE:1})`;m.afterStyles({transform:p}).addElement(S.querySelector(".modal-wrapper")).keyframes([{offset:0,filter:"contrast(1)",transform:"translateY(0) scale(1)"},{offset:1,filter:"contrast(0.85)",transform:p}]);let R=u().afterStyles({transform:p}).addElement(S.querySelector(".modal-shadow")).keyframes([{offset:0,opacity:"1",transform:"translateY(0) scale(1)"},{offset:1,opacity:"0",transform:p}]);b.addAnimation([m,R])}}else b.addAnimation(s);return b},Xt=()=>{let t=u().fromTo("opacity","var(--backdrop-opacity)",0),e=u().fromTo("transform","translateY(0vh)","translateY(100vh)");return{backdropAnimation:t,wrapperAnimation:e}},qt=(t,e,n=500)=>{let{presentingEl:o,currentBreakpoint:r}=e,i=Y(t),{wrapperAnimation:a,backdropAnimation:s}=r!==void 0?jt(e):Xt();s.addElement(i.querySelector("ion-backdrop")),a.addElement(i.querySelectorAll(".modal-wrapper, .modal-shadow")).beforeStyles({opacity:1});let d=u("leaving-base").addElement(t).easing("cubic-bezier(0.32,0.72,0,1)").duration(n).addAnimation(a);if(o){let b=window.innerWidth<768,D=o.tagName==="ION-MODAL"&&o.presentingElement!==void 0,w=Y(o),S=u().beforeClearStyles(["transform"]).afterClearStyles(["transform"]).onFinish(y=>{if(y!==1)return;o.style.setProperty("overflow",""),Array.from(m.querySelectorAll("ion-modal:not(.overlay-hidden)")).filter(p=>p.presentingElement!==void 0).length<=1&&m.style.setProperty("background-color","")}),m=document.body;if(b){let y=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px",E=D?"-10px":y,p=nt.MIN_PRESENTING_SCALE,R=`translateY(${E}) scale(${p})`;S.addElement(o).keyframes([{offset:0,filter:"contrast(0.85)",transform:R,borderRadius:"10px 10px 0 0"},{offset:1,filter:"contrast(1)",transform:"translateY(0px) scale(1)",borderRadius:"0px"}]),d.addAnimation(S)}else if(d.addAnimation(s),!D)a.fromTo("opacity","1","0");else{let E=`translateY(-10px) scale(${D?nt.MIN_PRESENTING_SCALE:1})`;S.addElement(w.querySelector(".modal-wrapper")).afterStyles({transform:"translate3d(0, 0, 0)"}).keyframes([{offset:0,filter:"contrast(0.85)",transform:E},{offset:1,filter:"contrast(1)",transform:"translateY(0) scale(1)"}]);let p=u().addElement(w.querySelector(".modal-shadow")).afterStyles({transform:"translateY(0) scale(1)"}).keyframes([{offset:0,opacity:"0",transform:E},{offset:1,opacity:"1",transform:"translateY(0) scale(1)"}]);d.addAnimation([S,p])}}else d.addAnimation(s);return d},te=()=>{let t=u().fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),e=u().keyframes([{offset:0,opacity:.01,transform:"translateY(40px)"},{offset:1,opacity:1,transform:"translateY(0px)"}]);return{backdropAnimation:t,wrapperAnimation:e,contentAnimation:void 0}},ee=(t,e)=>{let{currentBreakpoint:n,expandToScroll:o}=e,r=Y(t),{wrapperAnimation:i,backdropAnimation:a,contentAnimation:s}=n!==void 0?zt(e):te();a.addElement(r.querySelector("ion-backdrop")),i.addElement(r.querySelector(".modal-wrapper")),!o&&s?.addElement(t.querySelector(".ion-page"));let d=u().addElement(t).easing("cubic-bezier(0.36,0.66,0.04,1)").duration(280).addAnimation([a,i]);return s&&d.addAnimation(s),d},ne=()=>{let t=u().fromTo("opacity","var(--backdrop-opacity)",0),e=u().keyframes([{offset:0,opacity:.99,transform:"translateY(0px)"},{offset:1,opacity:0,transform:"translateY(40px)"}]);return{backdropAnimation:t,wrapperAnimation:e}},oe=(t,e)=>{let{currentBreakpoint:n}=e,o=Y(t),{wrapperAnimation:r,backdropAnimation:i}=n!==void 0?jt(e):ne();return i.addElement(o.querySelector("ion-backdrop")),r.addElement(o.querySelector(".modal-wrapper")),u().easing("cubic-bezier(0.47,0,0.745,0.715)").duration(200).addAnimation([i,r])},ie=(t,e,n,o,r,i,a=[],s,d,b,D)=>{let w=[{offset:0,opacity:"var(--backdrop-opacity)"},{offset:1,opacity:.01}],S=[{offset:0,opacity:"var(--backdrop-opacity)"},{offset:1-r,opacity:0},{offset:1,opacity:0}],m={WRAPPER_KEYFRAMES:[{offset:0,transform:"translateY(0%)"},{offset:1,transform:"translateY(100%)"}],BACKDROP_KEYFRAMES:r!==0?S:w,CONTENT_KEYFRAMES:[{offset:0,maxHeight:"100%"},{offset:1,maxHeight:"0%"}]},y=t.querySelector("ion-content"),E=n.clientHeight,p=o,R=0,L=!1,x=null,g=null,v=null,C=null,O=.95,q=a[a.length-1],M=a[0],_=i.childAnimations.find(h=>h.id==="wrapperAnimation"),A=i.childAnimations.find(h=>h.id==="backdropAnimation"),B=i.childAnimations.find(h=>h.id==="contentAnimation"),J=()=>{t.style.setProperty("pointer-events","auto"),e.style.setProperty("pointer-events","auto"),t.classList.remove(X)},ut=()=>{t.style.setProperty("pointer-events","none"),e.style.setProperty("pointer-events","none"),t.classList.add(X)},$=h=>{if(!g&&(g=Array.from(t.querySelectorAll("ion-footer")),!g.length))return;let l=t.querySelector(".ion-page");if(C=h,h==="stationary")g.forEach(f=>{f.classList.remove("modal-footer-moving"),f.style.removeProperty("position"),f.style.removeProperty("width"),f.style.removeProperty("height"),f.style.removeProperty("top"),f.style.removeProperty("left"),l?.style.removeProperty("padding-bottom"),l?.appendChild(f)});else{let f=0;g.forEach((c,H)=>{let T=c.getBoundingClientRect(),k=document.body.getBoundingClientRect();f+=c.clientHeight;let G=T.top-k.top,K=T.left-k.left;if(c.style.setProperty("--pinned-width",`${c.clientWidth}px`),c.style.setProperty("--pinned-height",`${c.clientHeight}px`),c.style.setProperty("--pinned-top",`${G}px`),c.style.setProperty("--pinned-left",`${K}px`),H===0){v=G;let ot=t.querySelector("ion-header");ot&&(v-=ot.clientHeight)}}),g.forEach(c=>{l?.style.setProperty("padding-bottom",`${f}px`),c.classList.add("modal-footer-moving"),c.style.setProperty("position","absolute"),c.style.setProperty("width","var(--pinned-width)"),c.style.setProperty("height","var(--pinned-height)"),c.style.setProperty("top","var(--pinned-top)"),c.style.setProperty("left","var(--pinned-left)"),document.body.appendChild(c)})}};_&&A&&(_.keyframes([...m.WRAPPER_KEYFRAMES]),A.keyframes([...m.BACKDROP_KEYFRAMES]),B?.keyframes([...m.CONTENT_KEYFRAMES]),i.progressStart(!0,1-p),p>r?J():ut()),y&&p!==q&&s&&(y.scrollY=!1);let Ft=h=>{let l=tt(h.event.target);if(p=d(),!s&&l)return(W(l)?Y(l).querySelector(".inner-scroll"):l).scrollTop===0;if(p===1&&l){let f=W(l)?Y(l).querySelector(".inner-scroll"):l;return!!!l.querySelector("ion-refresher")&&f.scrollTop===0}return!0},Vt=h=>{if(L=t.canDismiss!==void 0&&t.canDismiss!==!0&&M===0,!s){let l=tt(h.event.target);x=l&&W(l)?Y(l).querySelector(".inner-scroll"):l}s||$("moving"),h.deltaY>0&&y&&(y.scrollY=!1),z(()=>{t.focus()}),i.progressStart(!0,1-p)},Ut=h=>{if(!s&&v!==null&&C!==null&&(h.currentY>=v&&C==="moving"?$("stationary"):h.currentY<v&&C==="stationary"&&$("moving")),!s&&h.deltaY<=0&&x)return;h.deltaY>0&&y&&(y.scrollY=!1);let l=1-p,f=a.length>1?1-a[1]:void 0,c=l+h.deltaY/E,H=f!==void 0&&c>=f&&L,T=H?O:.9999,k=H&&f!==void 0?f+ft((c-f)/(T-f)):c;R=j(1e-4,k,T),i.progressStep(R)},Zt=h=>{if(!s&&h.deltaY<=0&&x&&x.scrollTop>0){$("stationary");return}let l=h.velocityY,f=(h.deltaY+l*350)/E,c=p-f,H=a.reduce((T,k)=>Math.abs(k-c)<Math.abs(T-c)?k:T);gt({breakpoint:H,breakpointOffset:R,canDismiss:L,animated:!0})},gt=h=>{let{breakpoint:l,canDismiss:f,breakpointOffset:c,animated:H}=h,T=f&&l===0,k=T?p:l,G=k!==0;return p=0,_&&A&&(_.keyframes([{offset:0,transform:`translateY(${c*100}%)`},{offset:1,transform:`translateY(${(1-k)*100}%)`}]),A.keyframes([{offset:0,opacity:`calc(var(--backdrop-opacity) * ${pt(1-c,r)})`},{offset:1,opacity:`calc(var(--backdrop-opacity) * ${pt(k,r)})`}]),B&&B.keyframes([{offset:0,maxHeight:`${(1-c)*100}%`},{offset:1,maxHeight:`${k*100}%`}]),i.progressStep(0)),Q.enable(!1),T?Kt(t,i):G||b(),y&&(k===a[a.length-1]||!s)&&(y.scrollY=!0),!s&&k===0&&$("stationary"),new Promise(K=>{i.onFinish(()=>{G?(s||$("stationary"),_&&A?z(()=>{_.keyframes([...m.WRAPPER_KEYFRAMES]),A.keyframes([...m.BACKDROP_KEYFRAMES]),B?.keyframes([...m.CONTENT_KEYFRAMES]),i.progressStart(!0,1-k),p=k,D(p),p>r?J():ut(),Q.enable(!0),K()}):(Q.enable(!0),K())):K()},{oneTimeCallback:!0}).progressEnd(1,0,H?500:0)})},Q=it({el:n,gestureName:"modalSheet",gesturePriority:40,direction:"y",threshold:10,canStart:Ft,onStart:Vt,onMove:Ut,onEnd:Zt});return{gesture:Q,moveSheetToBreakpoint:gt}},se=':host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, var(--ion-background-color-step-350, #c0c0be));cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:""}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host(.modal-sheet.modal-no-expand-scroll) ion-footer{position:absolute;bottom:0;width:var(--width)}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.4)}:host(.modal-card),:host(.modal-sheet){--border-radius:10px}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:10px}}.modal-wrapper{-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0)}@media screen and (max-width: 767px){@supports (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - max(30px, var(--ion-safe-area-top)) - 10px)}}@supports not (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - 40px)}}:host(.modal-card) .modal-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0;border-end-start-radius:0}:host(.modal-card){--backdrop-opacity:0;--width:100%;-ms-flex-align:end;align-items:flex-end}:host(.modal-card) .modal-shadow{display:none}:host(.modal-card) ion-backdrop{pointer-events:none}}@media screen and (min-width: 768px){:host(.modal-card){--width:calc(100% - 120px);--height:calc(100% - (120px + var(--ion-safe-area-top) + var(--ion-safe-area-bottom)));--max-width:720px;--max-height:1000px;--backdrop-opacity:0;--box-shadow:0px 0px 30px 10px rgba(0, 0, 0, 0.1);-webkit-transition:all 0.5s ease-in-out;transition:all 0.5s ease-in-out}:host(.modal-card) .modal-wrapper{-webkit-box-shadow:none;box-shadow:none}:host(.modal-card) .modal-shadow{-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow)}}:host(.modal-sheet) .modal-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0;border-end-start-radius:0}',re=':host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, var(--ion-background-color-step-350, #c0c0be));cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:""}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host(.modal-sheet.modal-no-expand-scroll) ion-footer{position:absolute;bottom:0;width:var(--width)}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.32)}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:2px;--box-shadow:0 28px 48px rgba(0, 0, 0, 0.4)}}.modal-wrapper{-webkit-transform:translate3d(0,  40px,  0);transform:translate3d(0,  40px,  0);opacity:0.01}',ae=class{constructor(t){_t(this,t),this.didPresent=I(this,"ionModalDidPresent",7),this.willPresent=I(this,"ionModalWillPresent",7),this.willDismiss=I(this,"ionModalWillDismiss",7),this.didDismiss=I(this,"ionModalDidDismiss",7),this.ionBreakpointDidChange=I(this,"ionBreakpointDidChange",7),this.didPresentShorthand=I(this,"didPresent",7),this.willPresentShorthand=I(this,"willPresent",7),this.willDismissShorthand=I(this,"willDismiss",7),this.didDismissShorthand=I(this,"didDismiss",7),this.ionMount=I(this,"ionMount",7),this.lockController=wt(),this.triggerController=Tt(),this.coreDelegate=St(),this.isSheetModal=!1,this.inheritedAttributes={},this.inline=!1,this.gestureAnimationDismissing=!1,this.presented=!1,this.hasController=!1,this.keyboardClose=!0,this.expandToScroll=!0,this.backdropBreakpoint=0,this.handleBehavior="none",this.backdropDismiss=!0,this.showBackdrop=!0,this.animated=!0,this.isOpen=!1,this.keepContentsMounted=!1,this.focusTrap=!0,this.canDismiss=!0,this.onHandleClick=()=>{let{sheetTransition:e,handleBehavior:n}=this;n!=="cycle"||e!==void 0||this.moveToNextBreakpoint()},this.onBackdropTap=()=>{let{sheetTransition:e}=this;e===void 0&&this.dismiss(void 0,Bt)},this.onLifecycle=e=>{let n=this.usersElement,o=de[e.type];if(n&&o){let r=new CustomEvent(o,{bubbles:!1,cancelable:!1,detail:e.detail});n.dispatchEvent(r)}},this.onModalFocus=e=>{let{dragHandleEl:n,el:o}=this;e.target===o&&n&&n.tabIndex!==-1&&n.focus()}}onIsOpenChange(t,e){t===!0&&e===!1?this.present():t===!1&&e===!0&&this.dismiss()}triggerChanged(){let{trigger:t,el:e,triggerController:n}=this;t&&n.addClickListener(e,t)}breakpointsChanged(t){t!==void 0&&(this.sortedBreakpoints=t.sort((e,n)=>e-n))}connectedCallback(){let{el:t}=this;At(t),this.triggerChanged()}disconnectedCallback(){this.triggerController.removeClickListener()}componentWillLoad(){var t;let{breakpoints:e,initialBreakpoint:n,el:o,htmlAttributes:r}=this,i=this.isSheetModal=e!==void 0&&n!==void 0,a=["aria-label","role"];this.inheritedAttributes=yt(o,a),r!==void 0&&a.forEach(s=>{r[s]&&(this.inheritedAttributes=Object.assign(Object.assign({},this.inheritedAttributes),{[s]:r[s]}),delete r[s])}),i&&(this.currentBreakpoint=this.initialBreakpoint),e!==void 0&&n!==void 0&&!e.includes(n)&&et("[ion-modal] - Your breakpoints array must include the initialBreakpoint value."),!((t=this.htmlAttributes)===null||t===void 0)&&t.id||Dt(this.el)}componentDidLoad(){this.isOpen===!0&&z(()=>this.present()),this.breakpointsChanged(this.breakpoints),this.triggerChanged()}getDelegate(t=!1){if(this.workingDelegate&&!t)return{delegate:this.workingDelegate,inline:this.inline};let e=this.el.parentNode,n=this.inline=e!==null&&!this.hasController,o=this.workingDelegate=n?this.delegate||this.coreDelegate:this.delegate;return{inline:n,delegate:o}}checkCanDismiss(t,e){return P(this,null,function*(){let{canDismiss:n}=this;return typeof n=="function"?n(t,e):n})}present(){return P(this,null,function*(){let t=yield this.lockController.lock();if(this.presented){t();return}let{presentingElement:e,el:n}=this;this.currentBreakpoint=this.initialBreakpoint;let{inline:o,delegate:r}=this.getDelegate(!0);this.ionMount.emit(),this.usersElement=yield xt(r,n,this.component,["ion-page"],this.componentProps,o),bt(n)?yield Ot(this.usersElement):this.keepContentsMounted||(yield Lt()),ct(()=>this.el.classList.add("show-modal"));let i=e!==void 0;i&&U(this)==="ios"&&(this.statusBarStyle=yield mt.getStyle(),Gt()),yield Ct(this,"modalEnter",Wt,ee,{presentingEl:e,currentBreakpoint:this.initialBreakpoint,backdropBreakpoint:this.backdropBreakpoint,expandToScroll:this.expandToScroll}),typeof window<"u"&&(this.keyboardOpenCallback=()=>{this.gesture&&(this.gesture.enable(!1),z(()=>{this.gesture&&this.gesture.enable(!0)}))},window.addEventListener(dt,this.keyboardOpenCallback)),this.isSheetModal?this.initSheetGesture():i&&this.initSwipeToClose(),t()})}initSwipeToClose(){var t;if(U(this)!=="ios")return;let{el:e}=this,n=this.leaveAnimation||lt.get("modalLeave",qt),o=this.animation=n(e,{presentingEl:this.presentingElement,expandToScroll:this.expandToScroll});if(!Yt(e)){It(e);return}let i=(t=this.statusBarStyle)!==null&&t!==void 0?t:Z.Default;this.gesture=Jt(e,o,i,()=>{this.gestureAnimationDismissing=!0,ht(this.statusBarStyle),this.animation.onFinish(()=>P(this,null,function*(){yield this.dismiss(void 0,V),this.gestureAnimationDismissing=!1}))}),this.gesture.enable(!0)}initSheetGesture(){let{wrapperEl:t,initialBreakpoint:e,backdropBreakpoint:n}=this;if(!t||e===void 0)return;let o=this.enterAnimation||lt.get("modalEnter",Wt),r=this.animation=o(this.el,{presentingEl:this.presentingElement,currentBreakpoint:e,backdropBreakpoint:n,expandToScroll:this.expandToScroll});r.progressStart(!0,1);let{gesture:i,moveSheetToBreakpoint:a}=ie(this.el,this.backdropEl,t,e,n,r,this.sortedBreakpoints,this.expandToScroll,()=>{var s;return(s=this.currentBreakpoint)!==null&&s!==void 0?s:0},()=>this.sheetOnDismiss(),s=>{this.currentBreakpoint!==s&&(this.currentBreakpoint=s,this.ionBreakpointDidChange.emit({breakpoint:s}))});this.gesture=i,this.moveSheetToBreakpoint=a,this.gesture.enable(!0)}sheetOnDismiss(){this.gestureAnimationDismissing=!0,this.animation.onFinish(()=>P(this,null,function*(){this.currentBreakpoint=0,this.ionBreakpointDidChange.emit({breakpoint:this.currentBreakpoint}),yield this.dismiss(void 0,V),this.gestureAnimationDismissing=!1}))}dismiss(t,e){return P(this,null,function*(){var n;if(this.gestureAnimationDismissing&&e!==V)return!1;let o=yield this.lockController.lock();if(e!=="handler"&&!(yield this.checkCanDismiss(t,e)))return o(),!1;let{presentingElement:r}=this;r!==void 0&&U(this)==="ios"&&ht(this.statusBarStyle),typeof window<"u"&&this.keyboardOpenCallback&&(window.removeEventListener(dt,this.keyboardOpenCallback),this.keyboardOpenCallback=void 0);let a=yield Et(this,t,e,"modalLeave",qt,oe,{presentingEl:r,currentBreakpoint:(n=this.currentBreakpoint)!==null&&n!==void 0?n:this.initialBreakpoint,backdropBreakpoint:this.backdropBreakpoint,expandToScroll:this.expandToScroll});if(a){let{delegate:s}=this.getDelegate();yield vt(s,this.usersElement),ct(()=>this.el.classList.remove("show-modal")),this.animation&&this.animation.destroy(),this.gesture&&this.gesture.destroy()}return this.currentBreakpoint=void 0,this.animation=void 0,o(),a})}onDidDismiss(){return st(this.el,"ionModalDidDismiss")}onWillDismiss(){return st(this.el,"ionModalWillDismiss")}setCurrentBreakpoint(t){return P(this,null,function*(){if(!this.isSheetModal){et("[ion-modal] - setCurrentBreakpoint is only supported on sheet modals.");return}if(!this.breakpoints.includes(t)){et(`[ion-modal] - Attempted to set invalid breakpoint value ${t}. Please double check that the breakpoint value is part of your defined breakpoints.`);return}let{currentBreakpoint:e,moveSheetToBreakpoint:n,canDismiss:o,breakpoints:r,animated:i}=this;e!==t&&n&&(this.sheetTransition=n({breakpoint:t,breakpointOffset:1-e,canDismiss:o!==void 0&&o!==!0&&r[0]===0,animated:i}),yield this.sheetTransition,this.sheetTransition=void 0)})}getCurrentBreakpoint(){return P(this,null,function*(){return this.currentBreakpoint})}moveToNextBreakpoint(){return P(this,null,function*(){let{breakpoints:t,currentBreakpoint:e}=this;if(!t||e==null)return!1;let n=t.filter(a=>a!==0),r=(n.indexOf(e)+1)%n.length,i=n[r];return yield this.setCurrentBreakpoint(i),!0})}render(){let{handle:t,isSheetModal:e,presentingElement:n,htmlAttributes:o,handleBehavior:r,inheritedAttributes:i,focusTrap:a,expandToScroll:s}=this,d=t!==!1&&e,b=U(this),D=n!==void 0&&b==="ios",w=r==="cycle";return N(Ht,Object.assign({key:"8add05bb43a2cdb5e3cf180147d31eb85a018fe0","no-router":!0,tabIndex:w&&(e&&d)?0:-1},o,{style:{zIndex:`${2e4+this.overlayIndex}`},class:Object.assign({[b]:!0,"modal-default":!D&&!e,"modal-card":D,"modal-sheet":e,"modal-no-expand-scroll":e&&!s,"overlay-hidden":!0,[X]:a===!1},Pt(this.cssClass)),onIonBackdropTap:this.onBackdropTap,onIonModalDidPresent:this.onLifecycle,onIonModalWillPresent:this.onLifecycle,onIonModalWillDismiss:this.onLifecycle,onIonModalDidDismiss:this.onLifecycle,onFocus:this.onModalFocus}),N("ion-backdrop",{key:"90a6605a9564a699d6f66cf71cf6b506796a2963",ref:m=>this.backdropEl=m,visible:this.showBackdrop,tappable:this.backdropDismiss,part:"backdrop"}),b==="ios"&&N("div",{key:"a97d071395333bf803c0a9347bda000cf7500d8d",class:"modal-shadow"}),N("div",Object.assign({key:"e7b7985c7414a13e3ba8dcecf497b76e92edf53e",role:"dialog"},i,{"aria-modal":"true",class:"modal-wrapper ion-overlay-wrapper",part:"content",ref:m=>this.wrapperEl=m}),d&&N("button",{key:"8258b65570b11a8ee9c9df2537d6419cd2e34536",class:"modal-handle",tabIndex:w?0:-1,"aria-label":"Activate to adjust the size of the dialog overlaying the screen",onClick:w?this.onHandleClick:void 0,part:"handle",ref:m=>this.dragHandleEl=m}),N("slot",{key:"394370d0ed03ee03152f8f8abae7ff7664ca5c13"})))}get el(){return Nt(this)}static get watchers(){return{isOpen:["onIsOpenChange"],trigger:["triggerChanged"]}}},de={ionModalDidPresent:"ionViewDidEnter",ionModalWillPresent:"ionViewWillEnter",ionModalWillDismiss:"ionViewWillLeave",ionModalDidDismiss:"ionViewDidLeave"};ae.style={ios:se,md:re};export{ae as ion_modal};
