.clients-container {
  padding: 0;
  background: transparent;
  min-height: 100vh;
}

// Pagination styles
.pagination-bar {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: flex-end;
  margin-top: 12px;
  padding: 16px 0;
  border-top: 1px solid #e2e8f0;

  .page-size-select {
    min-width: 100px;
  }
}

// Table responsive styles
.clients-table th,
.clients-table td {
  white-space: nowrap;
}

/* Page Header */
.page-header {
  margin-bottom: 32px;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 24px;

    .title-section {
      .page-title {
        font-size: 32px;
        font-weight: 800;
        color: #c8102e;
        margin: 0 0 8px 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .page-subtitle {
        font-size: 16px;
        color: rgba(255, 255, 255, 0.8);
        margin: 0;
        font-weight: 400;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;

      .action-btn {
        padding: 12px 20px;
        border: none;
        border-radius: 12px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;

        ion-icon {
          font-size: 18px;
        }

        &.primary {
          background: rgba(255, 255, 255, 0.2);
          color: white;
          border: 1px solid rgba(255, 255, 255, 0.3);
          backdrop-filter: blur(10px);

          &:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
          }
        }

        &.secondary {
          background: rgba(255, 255, 255, 0.1);
          color: white;
          border: 1px solid rgba(255, 255, 255, 0.2);

          &:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
          }
        }
      }
    }
  }
}

/* Statistics Row */
.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 32px;

  .stat-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    }

    .stat-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

      ion-icon {
        font-size: 24px;
        color: white;
      }

      &.clients {
        background: linear-gradient(135deg, #c8102e, #a00d26);
      }

      &.active {
        background: linear-gradient(135deg, #43e97b, #38f9d7);
      }

      &.new {
        background: linear-gradient(135deg, #f093fb, #f5576c);
      }

      &.rating {
        background: linear-gradient(135deg, #fbbf24, #f59e0b);
      }
    }

    .stat-info {
      h3 {
        font-size: 24px;
        font-weight: 700;
        color: #2d3748;
        margin: 0 0 4px 0;
        line-height: 1;
      }

      p {
        font-size: 14px;
        color: #718096;
        margin: 0;
        font-weight: 500;
      }
    }
  }
}

/* Filters Section */
.filters-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);

  .search-container {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    padding: 12px 16px;
    border: 1px solid rgba(200, 16, 46, 0.2);
    margin-bottom: 20px;
    transition: all 0.3s ease;

    &:focus-within {
      border-color: #c8102e;
      box-shadow: 0 0 0 3px rgba(200, 16, 46, 0.1);
    }

    ion-icon {
      color: #a0aec0;
      margin-right: 12px;
      font-size: 20px;
    }

    .search-input {
      border: none;
      outline: none;
      background: transparent;
      flex: 1;
      color: #4a5568;
      font-size: 14px;

      &::placeholder {
        color: #a0aec0;
      }
    }
  }

  .filters-container {
    display: flex;
    gap: 16px;
    align-items: center;
    flex-wrap: wrap;

    .filter-select {
      padding: 10px 16px;
      border: 1px solid rgba(200, 16, 46, 0.2);
      border-radius: 8px;
      background: white;
      color: #4a5568;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:focus {
        outline: none;
        border-color: #c8102e;
        box-shadow: 0 0 0 3px rgba(200, 16, 46, 0.1);
      }
    }

    .filter-btn {
      padding: 10px 16px;
      background: rgba(200, 16, 46, 0.1);
      border: 1px solid rgba(200, 16, 46, 0.2);
      border-radius: 8px;
      color: #c8102e;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 8px;

      &:hover {
        background: rgba(200, 16, 46, 0.15);
        transform: translateY(-1px);
      }

      ion-icon {
        font-size: 16px;
      }
    }
  }
}

/* Table Container */
.table-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;

  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);

    h3 {
      font-size: 18px;
      font-weight: 700;
      color: #2d3748;
      margin: 0;
    }

    .table-actions {
      .table-btn {
        padding: 8px;
        background: rgba(200, 16, 46, 0.1);
        border: 1px solid rgba(200, 16, 46, 0.2);
        border-radius: 8px;
        color: #c8102e;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(200, 16, 46, 0.15);
          transform: translateY(-1px);
        }

        ion-icon {
          font-size: 20px;
        }
      }
    }
  }

  .table-wrapper {
    overflow-x: auto;

    .clients-table {
      width: 100%;
      border-collapse: collapse;

      thead {
        background: rgba(200, 16, 46, 0.05);

        th {
          padding: 16px;
          text-align: left;
          font-weight: 600;
          color: #4a5568;
          font-size: 14px;
          border-bottom: 1px solid rgba(0, 0, 0, 0.05);
          white-space: nowrap;

          &.sortable {
            cursor: pointer;
            user-select: none;
            transition: all 0.3s ease;

            &:hover {
              background: rgba(200, 16, 46, 0.1);
            }

            ion-icon {
              margin-left: 4px;
              font-size: 12px;
              opacity: 0.6;
            }
          }
        }
      }

      tbody {
        tr {
          transition: all 0.3s ease;

          &:hover {
            background: rgba(200, 16, 46, 0.02);
          }

          &.selected {
            background: rgba(200, 16, 46, 0.05);
          }

          td {
            padding: 16px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            vertical-align: middle;

            .client-info {
              display: flex;
              align-items: center;
              gap: 12px;

              .client-avatar {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                object-fit: cover;
                border: 2px solid #c8102e;
              }

              .client-details {
                h4 {
                  font-size: 14px;
                  font-weight: 600;
                  color: #2d3748;
                  margin: 0 0 2px 0;
                }

                p {
                  font-size: 12px;
                  color: #718096;
                  margin: 0;
                }
              }
            }

            .contact-info {
              p {
                font-size: 13px;
                color: #4a5568;
                margin: 0 0 2px 0;

                &:last-child {
                  margin: 0;
                }
              }
            }

            .trips-count {
              background: rgba(200, 16, 46, 0.1);
              color: #c8102e;
              padding: 4px 8px;
              border-radius: 6px;
              font-size: 12px;
              font-weight: 600;
            }

            .rating {
              display: flex;
              align-items: center;
              gap: 4px;

              .star {
                font-size: 14px;

                &.filled {
                  color: #fbbf24;
                }
              }

              span {
                font-size: 13px;
                font-weight: 600;
                color: #4a5568;
              }
            }

            .no-rating {
              color: #a0aec0;
              font-style: italic;
            }

            .status-badge {
              padding: 4px 12px;
              border-radius: 20px;
              font-size: 12px;
              font-weight: 600;
              text-transform: capitalize;

              &.actif {
                background: rgba(56, 178, 172, 0.1);
                color: #38b2ac;
              }

              &.inactif {
                background: rgba(160, 174, 192, 0.1);
                color: #a0aec0;
              }

              &.suspendu {
                background: rgba(245, 101, 101, 0.1);
                color: #f56565;
              }
            }

            .action-buttons {
              display: flex;
              gap: 8px;

              .action-btn-small {
                width: 32px;
                height: 32px;
                border: none;
                border-radius: 6px;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s ease;

                ion-icon {
                  font-size: 16px;
                }

                &.view {
                  background: rgba(200, 16, 46, 0.1);
                  color: #c8102e;

                  &:hover {
                    background: rgba(200, 16, 46, 0.2);
                    transform: translateY(-1px);
                  }
                }

                &.edit {
                  background: rgba(56, 178, 172, 0.1);
                  color: #38b2ac;

                  &:hover {
                    background: rgba(56, 178, 172, 0.2);
                    transform: translateY(-1px);
                  }
                }

                &.delete {
                  background: rgba(245, 101, 101, 0.1);
                  color: #f56565;

                  &:hover {
                    background: rgba(245, 101, 101, 0.2);
                    transform: translateY(-1px);
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  /* Grid View */
  .grid-view {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    padding: 24px;

    .client-card {
      background: white;
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
      border: 1px solid rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
      }

      .card-header {
        position: relative;
        padding: 20px;
        background: linear-gradient(135deg, #c8102e, #a00d26);
        text-align: center;

        .client-photo {
          width: 80px;
          height: 80px;
          border-radius: 50%;
          object-fit: cover;
          border: 4px solid white;
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .client-status {
          position: absolute;
          top: 16px;
          right: 16px;

          .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: capitalize;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            backdrop-filter: blur(10px);
          }
        }
      }

      .card-content {
        padding: 20px;

        h3 {
          font-size: 18px;
          font-weight: 700;
          color: #2d3748;
          margin: 0 0 12px 0;
          text-align: center;
        }

        .client-email,
        .client-phone,
        .client-city {
          font-size: 14px;
          color: #718096;
          margin: 0 0 8px 0;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;

          &:before {
            content: '';
            width: 4px;
            height: 4px;
            background: #cbd5e0;
            border-radius: 50%;
          }
        }

        .client-stats {
          display: flex;
          justify-content: space-around;
          margin-top: 16px;
          padding-top: 16px;
          border-top: 1px solid rgba(0, 0, 0, 0.05);

          .stat {
            text-align: center;

            .stat-value {
              display: block;
              font-size: 20px;
              font-weight: 700;
              color: #c8102e;
              margin-bottom: 4px;
            }

            .stat-label {
              font-size: 12px;
              color: #a0aec0;
              font-weight: 500;
            }
          }
        }
      }

      .card-actions {
        display: flex;
        justify-content: center;
        gap: 12px;
        padding: 16px 20px 20px;

        .card-btn {
          width: 36px;
          height: 36px;
          border: none;
          border-radius: 8px;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;

          ion-icon {
            font-size: 18px;
          }

          &.view {
            background: rgba(200, 16, 46, 0.1);
            color: #c8102e;

            &:hover {
              background: rgba(200, 16, 46, 0.2);
              transform: translateY(-2px);
            }
          }

          &.edit {
            background: rgba(56, 178, 172, 0.1);
            color: #38b2ac;

            &:hover {
              background: rgba(56, 178, 172, 0.2);
              transform: translateY(-2px);
            }
          }

          &.delete {
            background: rgba(245, 101, 101, 0.1);
            color: #f56565;

            &:hover {
              background: rgba(245, 101, 101, 0.2);
              transform: translateY(-2px);
            }
          }
        }
      }
    }
  }

  /* Pagination */
  .pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    padding: 24px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);

    .page-btn {
      width: 40px;
      height: 40px;
      border: 1px solid rgba(200, 16, 46, 0.2);
      background: white;
      border-radius: 8px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      color: #c8102e;

      &:hover:not(:disabled) {
        background: rgba(200, 16, 46, 0.1);
        transform: translateY(-1px);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      ion-icon {
        font-size: 18px;
      }
    }

    .page-numbers {
      display: flex;
      gap: 4px;

      .page-number {
        width: 40px;
        height: 40px;
        border: 1px solid rgba(200, 16, 46, 0.2);
        background: white;
        border-radius: 8px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        color: #c8102e;
        font-weight: 600;
        font-size: 14px;

        &:hover {
          background: rgba(200, 16, 46, 0.1);
          transform: translateY(-1px);
        }

        &.active {
          background: #c8102e;
          color: white;
          border-color: #c8102e;
        }
      }
    }
  }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .stats-row {
    grid-template-columns: repeat(2, 1fr);
  }

  .grid-view {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .page-header {
    .header-content {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;

      .title-section {
        .page-title {
          font-size: 24px;
        }

        .page-subtitle {
          font-size: 14px;
        }
      }

      .header-actions {
        justify-content: center;

        .action-btn {
          flex: 1;
          justify-content: center;
        }
      }
    }
  }

  .stats-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .filters-section {
    padding: 16px;

    .filters-container {
      flex-direction: column;
      align-items: stretch;

      .filter-select,
      .filter-btn {
        width: 100%;
      }
    }
  }

  .table-container {
    .table-header {
      padding: 16px;
      flex-direction: column;
      gap: 12px;
      align-items: stretch;

      .table-actions {
        display: flex;
        justify-content: center;
      }
    }

    .table-wrapper {
      .clients-table {
        font-size: 12px;

        th,
        td {
          padding: 8px;
        }

        .client-info {
          .client-avatar {
            width: 32px;
            height: 32px;
          }

          .client-details {
            h4 {
              font-size: 12px;
            }

            p {
              font-size: 10px;
            }
          }
        }

        .action-buttons {

          .action-btn-small {
            width: 28px;
            height: 28px;

            ion-icon {
              font-size: 14px;
            }
          }
        }
      }
    }
  }

  .grid-view {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 16px;
  }

  .pagination {
    padding: 16px;
    flex-wrap: wrap;

    .page-numbers {
      order: -1;
      width: 100%;
      justify-content: center;
      margin-bottom: 12px;
    }
  }
}

@media (max-width: 480px) {
  .clients-container {
    padding: 0;
  }

  .filters-section {
    margin: 0 0 16px 0;
    border-radius: 12px;
  }

  .table-container {
    border-radius: 12px;
  }

  .client-card {
    .card-header {
      padding: 16px;

      .client-photo {
        width: 60px;
        height: 60px;
      }
    }

    .card-content {
      padding: 16px;

      h3 {
        font-size: 16px;
      }

      .client-email,
      .client-phone {
        font-size: 12px;
      }
    }

    .card-actions {
      padding: 12px 16px 16px;

      .card-btn {
        width: 32px;
        height: 32px;

        ion-icon {
          font-size: 16px;
        }
      }
    }
  }

  .page-numbers {
    .page-number {
      width: 36px;
      height: 36px;
      font-size: 12px;
    }
  }

  .page-btn {
    width: 36px;
    height: 36px;

    ion-icon {
      font-size: 16px;
    }
  }
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-card,
.client-card {
  animation: fadeInUp 0.6s ease forwards;
}

.clients-table tbody tr {
  animation: fadeInUp 0.4s ease forwards;
}

/* Loading States */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

/* Custom Scrollbar */
.table-wrapper::-webkit-scrollbar {
  height: 8px;
}

.table-wrapper::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.table-wrapper::-webkit-scrollbar-thumb {
  background: rgba(200, 16, 46, 0.3);
  border-radius: 4px;
}

.table-wrapper::-webkit-scrollbar-thumb:hover {
  background: rgba(200, 16, 46, 0.5);
}