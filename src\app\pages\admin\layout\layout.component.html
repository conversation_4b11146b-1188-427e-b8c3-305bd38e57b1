<div class="admin-layout">
  <!-- Top Navbar -->
  <header class="admin-navbar">
    <div class="navbar-content">
      <div class="navbar-left">
        <button class="menu-toggle" (click)="toggleSidebar()">
          <ion-icon name="menu-outline"></ion-icon>
        </button>
        <h1 class="app-title">LouageTunise</h1>
      </div>

      <div class="navbar-right">
          <div class="user-profile" (click)="toggleUserMenu()">
          <img src="assets/img/administrateur.png " alt="Admin" class="avatar">
          <span class="username">Admin</span>
          <ion-icon name="chevron-down-outline"></ion-icon>
        </div>

        <!-- User Dropdown Menu -->
        <div class="user-menu" [class.show]="showUserMenu">
         
          
          <a href="#" class="menu-item logout" (click)="logout()">
            <ion-icon name="log-out-outline"></ion-icon>
            Déconnexion
          </a>
        </div>
      </div>
    </div>
  </header>

  <!-- Sidebar -->
  <aside class="admin-sidebar" [class.collapsed]="sidebarCollapsed">
    <app-sidebar [collapsed]="sidebarCollapsed"></app-sidebar>
  </aside>

  <!-- Main Content -->
  <main class="admin-main" [class.sidebar-collapsed]="sidebarCollapsed">
    <div class="content-wrapper">
      <ion-router-outlet></ion-router-outlet>
    </div>
  </main>
</div>
