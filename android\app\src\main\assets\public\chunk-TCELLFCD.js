import{A as oe,Ba as xe,Bb as xn,Ca as J,D as ft,Da as D,Ea as cn,Eb as bt,F as ie,Fa as W,G as Zt,Ga as G,Ha as Ve,<PERSON>a as dn,<PERSON>a as Ie,<PERSON> as Ce,La as Ze,<PERSON> as Wt,Ma as pn,N as qt,Na as un,Oa as mn,Pa as We,Q as Xt,Qa as qe,Ra as Xe,S as re,Sa as Ge,T as se,Ta as ke,Ua as Ue,V as Gt,Va as Pe,W as Ut,Wa as fn,X as Kt,Xa as hn,Ya as Ee,Za as gn,b as te,d as $t,fa as Qt,h as Ht,ha as ce,i as _t,ia as Jt,l as ye,m as Nt,n as Le,ob as gt,pb as Ke,q as ze,qb as bn,r as Yt,t as mt,tb as vn,u as ne,ua as en,ub as yn,wa as tn,y as S,z as Vt}from"./chunk-XPS4RP6N.js";import{a as De,b as vt,c as Be,d as Qe,g as <PERSON>,h as yt,i as In}from"./chunk-QUM6RZVN.js";import{a as x,g as ln,h as _e,i as Ne}from"./chunk-ICSGBKZQ.js";import{a as q,b as A,c as me,d as K,e as nn,f as c,g as B,j as v,k as ae,l as P}from"./chunk-YSN7XVTD.js";import{a as ht}from"./chunk-R5HL6L5F.js";import{a as wn}from"./chunk-4WFVMWDK.js";import{c as Ye}from"./chunk-M2X7KQLB.js";import{a as xt}from"./chunk-2YSZFPCQ.js";import{a as Cn}from"./chunk-57YRIO75.js";import{a as Ae,b as de,c as He,d as fe,e as on,g as _,h as Z,i as rn,k as he,m as sn,n as an}from"./chunk-REYR55MP.js";import{a as V,e as X,f as $e}from"./chunk-C5RQ2IC2.js";import{a as Q}from"./chunk-42C7ZIID.js";import{a as pt,b as ut,e as g}from"./chunk-JHI3MBHO.js";var kn="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M256 48C141.31 48 48 141.31 48 256s93.31 208 208 208 208-93.31 208-208S370.69 48 256 48zm75.31 260.69a16 16 0 11-22.62 22.62L256 278.63l-52.69 52.68a16 16 0 01-22.62-22.62L233.37 256l-52.68-52.69a16 16 0 0122.62-22.62L256 233.37l52.69-52.68a16 16 0 0122.62 22.62L278.63 256z'/></svg>",En="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M400 145.49L366.51 112 256 222.51 145.49 112 112 145.49 222.51 256 112 366.51 145.49 400 256 289.49 366.51 400 400 366.51 289.49 256 400 145.49z'/></svg>";var wt,Io=()=>{if(typeof window>"u")return new Map;if(!wt){let n=window;n.Ionicons=n.Ionicons||{},wt=n.Ionicons.map=n.Ionicons.map||new Map}return wt},Co=n=>{let e=It(n.src);return e||(e=Sn(n.name,n.icon,n.mode,n.ios,n.md),e?ko(e,n):n.icon&&(e=It(n.icon),e||(e=It(n.icon[n.mode]),e))?e:null)},ko=(n,e)=>{let t=Io().get(n);if(t)return t;try{return nn(`svg/${n}.svg`)}catch{console.warn(`[Ionicons Warning]: Could not load icon with name "${n}". Ensure that the icon is registered using addIcons or that the icon SVG data is passed directly to the icon component.`,e)}},Sn=(n,e,t,o,i)=>(t=(t&&Je(t))==="ios"?"ios":"md",o&&t==="ios"?n=Je(o):i&&t==="md"?n=Je(i):(!n&&e&&!Tn(e)&&(n=e),et(n)&&(n=Je(n))),!et(n)||n.trim()===""||n.replace(/[a-z]|-|\d/gi,"")!==""?null:n),It=n=>et(n)&&(n=n.trim(),Tn(n))?n:null,Tn=n=>n.length>0&&/(\/|\.)/.test(n),et=n=>typeof n=="string",Je=n=>n.toLowerCase(),Eo=(n,e=[])=>{let t={};return e.forEach(o=>{n.hasAttribute(o)&&(n.getAttribute(o)!==null&&(t[o]=n.getAttribute(o)),n.removeAttribute(o))}),t},Do=n=>n&&n.dir!==""?n.dir.toLowerCase()==="rtl":document?.dir.toLowerCase()==="rtl",So=n=>{let e=document.createElement("div");e.innerHTML=n;for(let o=e.childNodes.length-1;o>=0;o--)e.childNodes[o].nodeName.toLowerCase()!=="svg"&&e.removeChild(e.childNodes[o]);let t=e.firstElementChild;if(t&&t.nodeName.toLowerCase()==="svg"){let o=t.getAttribute("class")||"";if(t.setAttribute("class",(o+" s-ion-icon").trim()),jn(t))return e.innerHTML}return""},jn=n=>{if(n.nodeType===1){if(n.nodeName.toLowerCase()==="script")return!1;for(let e=0;e<n.attributes.length;e++){let t=n.attributes[e].name;if(et(t)&&t.toLowerCase().indexOf("on")===0)return!1}for(let e=0;e<n.childNodes.length;e++)if(!jn(n.childNodes[e]))return!1}return!0},To=n=>n.startsWith("data:image/svg+xml"),jo=n=>n.indexOf(";utf8,")!==-1,we=new Map,Dn=new Map,Ct,Mo=(n,e)=>{let t=Dn.get(n);if(!t)if(typeof fetch<"u"&&typeof document<"u")if(To(n)&&jo(n)){Ct||(Ct=new DOMParser);let i=Ct.parseFromString(n,"text/html").querySelector("svg");return i&&we.set(n,i.outerHTML),Promise.resolve()}else t=fetch(n).then(o=>{if(o.ok)return o.text().then(i=>{i&&e!==!1&&(i=So(i)),we.set(n,i||"")});we.set(n,"")}),Dn.set(n,t);else return we.set(n,""),Promise.resolve();return t},zo=":host{display:inline-block;width:1em;height:1em;contain:strict;fill:currentColor;-webkit-box-sizing:content-box !important;box-sizing:content-box !important}:host .ionicon{stroke:currentColor}.ionicon-fill-none{fill:none}.ionicon-stroke-width{stroke-width:32px;stroke-width:var(--ionicon-stroke-width, 32px)}.icon-inner,.ionicon,svg{display:block;height:100%;width:100%}@supports (background: -webkit-named-image(i)){:host(.icon-rtl) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}}@supports not selector(:dir(rtl)) and selector(:host-context([dir='rtl'])){:host(.icon-rtl) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}}:host(.flip-rtl):host-context([dir='rtl']) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}@supports selector(:dir(rtl)){:host(.flip-rtl:dir(rtl)) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}:host(.flip-rtl:dir(ltr)) .icon-inner{-webkit-transform:scaleX(1);transform:scaleX(1)}}:host(.icon-small){font-size:1.125rem !important}:host(.icon-large){font-size:2rem !important}:host(.ion-color){color:var(--ion-color-base) !important}:host(.ion-color-primary){--ion-color-base:var(--ion-color-primary, #3880ff)}:host(.ion-color-secondary){--ion-color-base:var(--ion-color-secondary, #0cd1e8)}:host(.ion-color-tertiary){--ion-color-base:var(--ion-color-tertiary, #f4a942)}:host(.ion-color-success){--ion-color-base:var(--ion-color-success, #10dc60)}:host(.ion-color-warning){--ion-color-base:var(--ion-color-warning, #ffce00)}:host(.ion-color-danger){--ion-color-base:var(--ion-color-danger, #f14141)}:host(.ion-color-light){--ion-color-base:var(--ion-color-light, #f4f5f8)}:host(.ion-color-medium){--ion-color-base:var(--ion-color-medium, #989aa2)}:host(.ion-color-dark){--ion-color-base:var(--ion-color-dark, #222428)}",Ao=P(class extends A{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.iconName=null,this.inheritedAttributes={},this.didLoadIcon=!1,this.svgContent=void 0,this.isVisible=!1,this.mode=Po(),this.color=void 0,this.ios=void 0,this.md=void 0,this.flipRtl=void 0,this.name=void 0,this.src=void 0,this.icon=void 0,this.size=void 0,this.lazy=!1,this.sanitize=!0}componentWillLoad(){this.inheritedAttributes=Eo(this.el,["aria-label"])}connectedCallback(){this.waitUntilVisible(this.el,"50px",()=>{this.isVisible=!0,this.loadIcon()})}componentDidLoad(){this.didLoadIcon||this.loadIcon()}disconnectedCallback(){this.io&&(this.io.disconnect(),this.io=void 0)}waitUntilVisible(e,t,o){if(q.isBrowser&&this.lazy&&typeof window<"u"&&window.IntersectionObserver){let i=this.io=new window.IntersectionObserver(r=>{r[0].isIntersecting&&(i.disconnect(),this.io=void 0,o())},{rootMargin:t});i.observe(e)}else o()}loadIcon(){if(q.isBrowser&&this.isVisible){let e=Co(this);e&&(we.has(e)?this.svgContent=we.get(e):Mo(e,this.sanitize).then(()=>this.svgContent=we.get(e)),this.didLoadIcon=!0)}this.iconName=Sn(this.name,this.icon,this.mode,this.ios,this.md)}render(){let{flipRtl:e,iconName:t,inheritedAttributes:o,el:i}=this,r=this.mode||"md",s=t?(t.includes("arrow")||t.includes("chevron"))&&e!==!1:!1,a=e||s;return c(B,Object.assign({role:"img",class:Object.assign(Object.assign({[r]:!0},Bo(this.color)),{[`icon-${this.size}`]:!!this.size,"flip-rtl":a,"icon-rtl":a&&Do(i)})},o),q.isBrowser&&this.svgContent?c("div",{class:"icon-inner",innerHTML:this.svgContent}):c("div",{class:"icon-inner"}))}static get assetsDirs(){return["svg"]}get el(){return this}static get watchers(){return{name:["loadIcon"],src:["loadIcon"],icon:["loadIcon"],ios:["loadIcon"],md:["loadIcon"]}}static get style(){return zo}},[1,"ion-icon",{mode:[1025],color:[1],ios:[1],md:[1],flipRtl:[4,"flip-rtl"],name:[513],src:[1],icon:[8],size:[1],lazy:[4],sanitize:[4],svgContent:[32],isVisible:[32]},void 0,{name:["loadIcon"],src:["loadIcon"],icon:["loadIcon"],ios:["loadIcon"],md:["loadIcon"]}]),Po=()=>q.isBrowser&&typeof document<"u"&&document.documentElement.getAttribute("mode")||"md",Bo=n=>n?{"ion-color":!0,[`ion-color-${n}`]:!0}:null;function Mn(){if(typeof customElements>"u")return;["ion-icon"].forEach(e=>{switch(e){case"ion-icon":customElements.get(e)||customElements.define(e,Ao);break}})}var Ro=":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:strict;pointer-events:none}:host(.unbounded){contain:layout size style}.ripple-effect{border-radius:50%;position:absolute;background-color:currentColor;color:inherit;contain:strict;opacity:0;-webkit-animation:225ms rippleAnimation forwards, 75ms fadeInAnimation forwards;animation:225ms rippleAnimation forwards, 75ms fadeInAnimation forwards;will-change:transform, opacity;pointer-events:none}.fade-out{-webkit-transform:translate(var(--translate-end)) scale(var(--final-scale, 1));transform:translate(var(--translate-end)) scale(var(--final-scale, 1));-webkit-animation:150ms fadeOutAnimation forwards;animation:150ms fadeOutAnimation forwards}@-webkit-keyframes rippleAnimation{from{-webkit-animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);-webkit-transform:scale(1);transform:scale(1)}to{-webkit-transform:translate(var(--translate-end)) scale(var(--final-scale, 1));transform:translate(var(--translate-end)) scale(var(--final-scale, 1))}}@keyframes rippleAnimation{from{-webkit-animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);-webkit-transform:scale(1);transform:scale(1)}to{-webkit-transform:translate(var(--translate-end)) scale(var(--final-scale, 1));transform:translate(var(--translate-end)) scale(var(--final-scale, 1))}}@-webkit-keyframes fadeInAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0}to{opacity:0.16}}@keyframes fadeInAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0}to{opacity:0.16}}@-webkit-keyframes fadeOutAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0.16}to{opacity:0}}@keyframes fadeOutAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0.16}to{opacity:0}}",Fo=P(class extends A{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.type="bounded"}addRipple(e,t){return g(this,null,function*(){return new Promise(o=>{me(()=>{let i=this.el.getBoundingClientRect(),r=i.width,s=i.height,a=Math.sqrt(r*r+s*s),l=Math.max(s,r),d=this.unbounded?l:a+Lo,p=Math.floor(l*$o),u=d/p,m=e-i.left,f=t-i.top;this.unbounded&&(m=r*.5,f=s*.5);let b=m-p*.5,w=f-p*.5,h=r*.5-m,C=s*.5-f;K(()=>{let k=document.createElement("div");k.classList.add("ripple-effect");let y=k.style;y.top=w+"px",y.left=b+"px",y.width=y.height=p+"px",y.setProperty("--final-scale",`${u}`),y.setProperty("--translate-end",`${h}px, ${C}px`),(this.el.shadowRoot||this.el).appendChild(k),setTimeout(()=>{o(()=>{Oo(k)})},325)})})})})}get unbounded(){return this.type==="unbounded"}render(){let e=D(this);return c(B,{key:"ae9d3b1ed6773a9b9bb2267129f7e9af23b6c9fc",role:"presentation",class:{[e]:!0,unbounded:this.unbounded}})}get el(){return this}static get style(){return Ro}},[1,"ion-ripple-effect",{type:[1],addRipple:[64]}]),Oo=n=>{n.classList.add("fade-out"),setTimeout(()=>{n.remove()},200)},Lo=10,$o=.5;function zn(){if(typeof customElements>"u")return;["ion-ripple-effect"].forEach(e=>{switch(e){case"ion-ripple-effect":customElements.get(e)||customElements.define(e,Fo);break}})}var Se=()=>{let n;return{lock:()=>g(null,null,function*(){let t=n,o;return n=new Promise(i=>o=i),t!==void 0&&(yield t),o})}};var Ho=":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:layout size style;z-index:0}",_o=P(class extends A{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionNavWillLoad=v(this,"ionNavWillLoad",7),this.ionNavWillChange=v(this,"ionNavWillChange",3),this.ionNavDidChange=v(this,"ionNavDidChange",3),this.lockController=Se(),this.gestureOrAnimationInProgress=!1,this.mode=D(this),this.animated=!0}swipeHandlerChanged(){this.gesture&&this.gesture.enable(this.swipeHandler!==void 0)}connectedCallback(){return g(this,null,function*(){let e=()=>{this.gestureOrAnimationInProgress=!0,this.swipeHandler&&this.swipeHandler.onStart()};this.gesture=(yield import("./chunk-3IDTDWAV.js")).createSwipeBackGesture(this.el,()=>!this.gestureOrAnimationInProgress&&!!this.swipeHandler&&this.swipeHandler.canStart(),()=>e(),t=>{var o;return(o=this.ani)===null||o===void 0?void 0:o.progressStep(t)},(t,o,i)=>{if(this.ani){this.ani.onFinish(()=>{this.gestureOrAnimationInProgress=!1,this.swipeHandler&&this.swipeHandler.onEnd(t)},{oneTimeCallback:!0});let r=t?-.001:.001;t?r+=xe([0,0],[.32,.72],[0,1],[1,1],o)[0]:(this.ani.easing("cubic-bezier(1, 0, 0.68, 0.28)"),r+=xe([0,0],[1,0],[.68,.28],[1,1],o)[0]),this.ani.progressEnd(t?1:0,r,i)}else this.gestureOrAnimationInProgress=!1}),this.swipeHandlerChanged()})}componentWillLoad(){this.ionNavWillLoad.emit()}disconnectedCallback(){this.gesture&&(this.gesture.destroy(),this.gesture=void 0)}commit(e,t,o){return g(this,null,function*(){let i=yield this.lockController.lock(),r=!1;try{r=yield this.transition(e,t,o)}catch(s){$e("[ion-router-outlet] - Exception in commit:",s)}return i(),r})}setRouteId(e,t,o,i){return g(this,null,function*(){return{changed:yield this.setRoot(e,t,{duration:o==="root"?0:void 0,direction:o==="back"?"back":"forward",animationBuilder:i}),element:this.activeEl}})}getRouteId(){return g(this,null,function*(){let e=this.activeEl;return e?{id:e.tagName,element:e,params:this.activeParams}:void 0})}setRoot(e,t,o){return g(this,null,function*(){if(this.activeComponent===e&&an(t,this.activeParams))return!1;let i=this.activeEl,r=yield Ie(this.delegate,this.el,e,["ion-page","ion-page-invisible"],t);return this.activeComponent=e,this.activeEl=r,this.activeParams=t,yield this.commit(r,i,o),yield Ce(this.delegate,i),!0})}transition(i,r){return g(this,arguments,function*(e,t,o={}){if(t===e)return!1;this.ionNavWillChange.emit();let{el:s,mode:a}=this,l=this.animated&&V.getBoolean("animated",!0),d=o.animationBuilder||this.animation||V.get("navAnimation");return yield ln(Object.assign(Object.assign({mode:a,animated:l,enteringEl:e,leavingEl:t,baseEl:s,deepWait:de(s),progressCallback:o.progressAnimation?p=>{p!==void 0&&!this.gestureOrAnimationInProgress?(this.gestureOrAnimationInProgress=!0,p.onFinish(()=>{this.gestureOrAnimationInProgress=!1,this.swipeHandler&&this.swipeHandler.onEnd(!1)},{oneTimeCallback:!0}),p.progressEnd(0,0,0)):this.ani=p}:void 0},o),{animationBuilder:d})),this.ionNavDidChange.emit(),!0})}render(){return c("slot",{key:"84b50f1155b0d780dff802ee13223287259fd525"})}get el(){return this}static get watchers(){return{swipeHandler:["swipeHandlerChanged"]}}static get style(){return Ho}},[1,"ion-router-outlet",{mode:[1025],delegate:[16],animated:[4],animation:[16],swipeHandler:[16,"swipe-handler"],commit:[64],setRouteId:[64],getRouteId:[64]},void 0,{swipeHandler:["swipeHandlerChanged"]}]);function No(){if(typeof customElements>"u")return;["ion-router-outlet"].forEach(e=>{switch(e){case"ion-router-outlet":customElements.get(e)||customElements.define(e,_o);break}})}var An=No;var Yo=":host{left:0;right:0;top:0;bottom:0;display:block;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);contain:strict;cursor:pointer;opacity:0.01;-ms-touch-action:none;touch-action:none;z-index:2}:host(.backdrop-hide){background:transparent}:host(.backdrop-no-tappable){cursor:auto}:host{background-color:var(--ion-backdrop-color, #000)}",Vo=":host{left:0;right:0;top:0;bottom:0;display:block;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);contain:strict;cursor:pointer;opacity:0.01;-ms-touch-action:none;touch-action:none;z-index:2}:host(.backdrop-hide){background:transparent}:host(.backdrop-no-tappable){cursor:auto}:host{background-color:var(--ion-backdrop-color, #000)}",Zo=P(class extends A{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionBackdropTap=v(this,"ionBackdropTap",7),this.visible=!0,this.tappable=!0,this.stopPropagation=!0}onMouseDown(e){this.emitTap(e)}emitTap(e){this.stopPropagation&&(e.preventDefault(),e.stopPropagation()),this.tappable&&this.ionBackdropTap.emit()}render(){let e=D(this);return c(B,{key:"7abaf2c310aa399607451b14063265e8a5846938","aria-hidden":"true",class:{[e]:!0,"backdrop-hide":!this.visible,"backdrop-no-tappable":!this.tappable}})}static get style(){return{ios:Yo,md:Vo}}},[33,"ion-backdrop",{visible:[4],tappable:[4],stopPropagation:[4,"stop-propagation"]},[[2,"click","onMouseDown"]]]);function tt(){if(typeof customElements>"u")return;["ion-backdrop"].forEach(e=>{switch(e){case"ion-backdrop":customElements.get(e)||customElements.define(e,Zo);break}})}var Fe=function(n){return n.Dark="DARK",n.Light="LIGHT",n.Default="DEFAULT",n}(Fe||{}),St={getEngine(){let n=Cn();if(n?.isPluginAvailable("StatusBar"))return n.Plugins.StatusBar},setStyle(n){let e=this.getEngine();e&&e.setStyle(n)},getStyle:function(){return g(this,null,function*(){let n=this.getEngine();if(!n)return Fe.Default;let{style:e}=yield n.getInfo();return e})}},kt=(n,e)=>{if(e===1)return 0;let t=1/(1-e),o=-(e*t);return n*t+o},Fn=()=>{!Q||Q.innerWidth>=768||St.setStyle({style:Fe.Dark})},Et=(n=Fe.Default)=>{!Q||Q.innerWidth>=768||St.setStyle({style:n})},On=(n,e)=>g(null,null,function*(){typeof n.canDismiss!="function"||!(yield n.canDismiss(void 0,Pe))||(e.isRunning()?e.onFinish(()=>{n.dismiss(void 0,"handler")},{oneTimeCallback:!0}):n.dismiss(void 0,"handler"))}),Dt=n=>.00255275*2.71828**(-14.9619*n)-1.00255*2.71828**(-.0380968*n)+1,nt={MIN_PRESENTING_SCALE:.915},Wo=(n,e,t,o)=>{let r=n.offsetHeight,s=!1,a=!1,l=null,d=null,p=.2,u=!0,m=0,f=()=>l&&De(l)?l.scrollY:!0,k=Ye({el:n,gestureName:"modalSwipeToClose",gesturePriority:fn,direction:"y",threshold:10,canStart:y=>{let T=y.event.target;return T===null||!T.closest?!0:(l=Qe(T),l?(De(l)?d=_(l).querySelector(".inner-scroll"):d=l,!!!l.querySelector("ion-refresher")&&d.scrollTop===0):T.closest("ion-footer")===null)},onStart:y=>{let{deltaY:T}=y;u=f(),a=n.canDismiss!==void 0&&n.canDismiss!==!0,T>0&&l&&yt(l),e.progressStart(!0,s?1:0)},onMove:y=>{let{deltaY:T}=y;T>0&&l&&yt(l);let R=y.deltaY/r,I=R>=0&&a,L=I?p:.9999,H=I?Dt(R/L):R,$=he(1e-4,H,L);e.progressStep($),$>=.5&&m<.5?Et(t):$<.5&&m>=.5&&Fn(),m=$},onEnd:y=>{let T=y.velocityY,R=y.deltaY/r,I=R>=0&&a,L=I?p:.9999,H=I?Dt(R/L):R,$=he(1e-4,H,L),Y=(y.deltaY+T*1e3)/r,F=!I&&Y>=.5,O=F?-.001:.001;F?(e.easing("cubic-bezier(0.32, 0.72, 0, 1)"),O+=xe([0,0],[.32,.72],[0,1],[1,1],$)[0]):(e.easing("cubic-bezier(1, 0, 0.68, 0.28)"),O+=xe([0,0],[1,0],[.68,.28],[1,1],$)[0]);let ee=Pn(F?R*r:(1-$)*r,T);s=F,k.enable(!1),l&&In(l,u),e.onFinish(()=>{F||k.enable(!0)}).progressEnd(F?1:0,O,ee),I&&$>L/4?On(n,e):F&&o()}});return k},Pn=(n,e)=>he(400,n/Math.abs(e*1.1),500),Ln=n=>{let{currentBreakpoint:e,backdropBreakpoint:t,expandToScroll:o}=n,i=t===void 0||t<e,r=i?`calc(var(--backdrop-opacity) * ${e})`:"0",s=x("backdropAnimation").fromTo("opacity",0,r);i&&s.beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]);let a=x("wrapperAnimation").keyframes([{offset:0,opacity:1,transform:"translateY(100%)"},{offset:1,opacity:1,transform:`translateY(${100-e*100}%)`}]),l=o?void 0:x("contentAnimation").keyframes([{offset:0,opacity:1,maxHeight:`${(1-e)*100}%`},{offset:1,opacity:1,maxHeight:`${e*100}%`}]);return{wrapperAnimation:a,backdropAnimation:s,contentAnimation:l}},$n=n=>{let{currentBreakpoint:e,backdropBreakpoint:t}=n,o=`calc(var(--backdrop-opacity) * ${kt(e,t)})`,i=[{offset:0,opacity:o},{offset:1,opacity:0}],r=[{offset:0,opacity:o},{offset:t,opacity:0},{offset:1,opacity:0}],s=x("backdropAnimation").keyframes(t!==0?r:i);return{wrapperAnimation:x("wrapperAnimation").keyframes([{offset:0,opacity:1,transform:`translateY(${100-e*100}%)`},{offset:1,opacity:1,transform:"translateY(100%)"}]),backdropAnimation:s}},qo=()=>{let n=x().fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),e=x().fromTo("transform","translateY(100vh)","translateY(0vh)");return{backdropAnimation:n,wrapperAnimation:e,contentAnimation:void 0}},Bn=(n,e)=>{let{presentingEl:t,currentBreakpoint:o,expandToScroll:i}=e,r=_(n),{wrapperAnimation:s,backdropAnimation:a,contentAnimation:l}=o!==void 0?Ln(e):qo();a.addElement(r.querySelector("ion-backdrop")),s.addElement(r.querySelectorAll(".modal-wrapper, .modal-shadow")).beforeStyles({opacity:1}),!i&&l?.addElement(n.querySelector(".ion-page"));let d=x("entering-base").addElement(n).easing("cubic-bezier(0.32,0.72,0,1)").duration(500).addAnimation([s]);if(l&&d.addAnimation(l),t){let p=window.innerWidth<768,u=t.tagName==="ION-MODAL"&&t.presentingElement!==void 0,m=_(t),f=x().beforeStyles({transform:"translateY(0)","transform-origin":"top center",overflow:"hidden"}),b=document.body;if(p){let w=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px",h=u?"-10px":w,C=nt.MIN_PRESENTING_SCALE,k=`translateY(${h}) scale(${C})`;f.afterStyles({transform:k}).beforeAddWrite(()=>b.style.setProperty("background-color","black")).addElement(t).keyframes([{offset:0,filter:"contrast(1)",transform:"translateY(0px) scale(1)",borderRadius:"0px"},{offset:1,filter:"contrast(0.85)",transform:k,borderRadius:"10px 10px 0 0"}]),d.addAnimation(f)}else if(d.addAnimation(a),!u)s.fromTo("opacity","0","1");else{let h=`translateY(-10px) scale(${u?nt.MIN_PRESENTING_SCALE:1})`;f.afterStyles({transform:h}).addElement(m.querySelector(".modal-wrapper")).keyframes([{offset:0,filter:"contrast(1)",transform:"translateY(0) scale(1)"},{offset:1,filter:"contrast(0.85)",transform:h}]);let C=x().afterStyles({transform:h}).addElement(m.querySelector(".modal-shadow")).keyframes([{offset:0,opacity:"1",transform:"translateY(0) scale(1)"},{offset:1,opacity:"0",transform:h}]);d.addAnimation([f,C])}}else d.addAnimation(a);return d},Xo=()=>{let n=x().fromTo("opacity","var(--backdrop-opacity)",0),e=x().fromTo("transform","translateY(0vh)","translateY(100vh)");return{backdropAnimation:n,wrapperAnimation:e}},Rn=(n,e,t=500)=>{let{presentingEl:o,currentBreakpoint:i}=e,r=_(n),{wrapperAnimation:s,backdropAnimation:a}=i!==void 0?$n(e):Xo();a.addElement(r.querySelector("ion-backdrop")),s.addElement(r.querySelectorAll(".modal-wrapper, .modal-shadow")).beforeStyles({opacity:1});let l=x("leaving-base").addElement(n).easing("cubic-bezier(0.32,0.72,0,1)").duration(t).addAnimation(s);if(o){let d=window.innerWidth<768,p=o.tagName==="ION-MODAL"&&o.presentingElement!==void 0,u=_(o),m=x().beforeClearStyles(["transform"]).afterClearStyles(["transform"]).onFinish(b=>{if(b!==1)return;o.style.setProperty("overflow",""),Array.from(f.querySelectorAll("ion-modal:not(.overlay-hidden)")).filter(h=>h.presentingElement!==void 0).length<=1&&f.style.setProperty("background-color","")}),f=document.body;if(d){let b=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px",w=p?"-10px":b,h=nt.MIN_PRESENTING_SCALE,C=`translateY(${w}) scale(${h})`;m.addElement(o).keyframes([{offset:0,filter:"contrast(0.85)",transform:C,borderRadius:"10px 10px 0 0"},{offset:1,filter:"contrast(1)",transform:"translateY(0px) scale(1)",borderRadius:"0px"}]),l.addAnimation(m)}else if(l.addAnimation(a),!p)s.fromTo("opacity","1","0");else{let w=`translateY(-10px) scale(${p?nt.MIN_PRESENTING_SCALE:1})`;m.addElement(u.querySelector(".modal-wrapper")).afterStyles({transform:"translate3d(0, 0, 0)"}).keyframes([{offset:0,filter:"contrast(0.85)",transform:w},{offset:1,filter:"contrast(1)",transform:"translateY(0) scale(1)"}]);let h=x().addElement(u.querySelector(".modal-shadow")).afterStyles({transform:"translateY(0) scale(1)"}).keyframes([{offset:0,opacity:"0",transform:w},{offset:1,opacity:"1",transform:"translateY(0) scale(1)"}]);l.addAnimation([m,h])}}else l.addAnimation(a);return l},Go=()=>{let n=x().fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),e=x().keyframes([{offset:0,opacity:.01,transform:"translateY(40px)"},{offset:1,opacity:1,transform:"translateY(0px)"}]);return{backdropAnimation:n,wrapperAnimation:e,contentAnimation:void 0}},Uo=(n,e)=>{let{currentBreakpoint:t,expandToScroll:o}=e,i=_(n),{wrapperAnimation:r,backdropAnimation:s,contentAnimation:a}=t!==void 0?Ln(e):Go();s.addElement(i.querySelector("ion-backdrop")),r.addElement(i.querySelector(".modal-wrapper")),!o&&a?.addElement(n.querySelector(".ion-page"));let l=x().addElement(n).easing("cubic-bezier(0.36,0.66,0.04,1)").duration(280).addAnimation([s,r]);return a&&l.addAnimation(a),l},Ko=()=>{let n=x().fromTo("opacity","var(--backdrop-opacity)",0),e=x().keyframes([{offset:0,opacity:.99,transform:"translateY(0px)"},{offset:1,opacity:0,transform:"translateY(40px)"}]);return{backdropAnimation:n,wrapperAnimation:e}},Qo=(n,e)=>{let{currentBreakpoint:t}=e,o=_(n),{wrapperAnimation:i,backdropAnimation:r}=t!==void 0?$n(e):Ko();return r.addElement(o.querySelector("ion-backdrop")),i.addElement(o.querySelector(".modal-wrapper")),x().easing("cubic-bezier(0.47,0,0.745,0.715)").duration(200).addAnimation([r,i])},Jo=(n,e,t,o,i,r,s=[],a,l,d,p)=>{let u=[{offset:0,opacity:"var(--backdrop-opacity)"},{offset:1,opacity:.01}],m=[{offset:0,opacity:"var(--backdrop-opacity)"},{offset:1-i,opacity:0},{offset:1,opacity:0}],f={WRAPPER_KEYFRAMES:[{offset:0,transform:"translateY(0%)"},{offset:1,transform:"translateY(100%)"}],BACKDROP_KEYFRAMES:i!==0?m:u,CONTENT_KEYFRAMES:[{offset:0,maxHeight:"100%"},{offset:1,maxHeight:"0%"}]},b=n.querySelector("ion-content"),w=t.clientHeight,h=o,C=0,k=!1,y=null,T=null,R=null,I=null,L=.95,H=s[s.length-1],$=s[0],Y=r.childAnimations.find(M=>M.id==="wrapperAnimation"),F=r.childAnimations.find(M=>M.id==="backdropAnimation"),O=r.childAnimations.find(M=>M.id==="contentAnimation"),ee=()=>{n.style.setProperty("pointer-events","auto"),e.style.setProperty("pointer-events","auto"),n.classList.remove(Ee)},be=()=>{n.style.setProperty("pointer-events","none"),e.style.setProperty("pointer-events","none"),n.classList.add(Ee)},ue=M=>{if(!T&&(T=Array.from(n.querySelectorAll("ion-footer")),!T.length))return;let E=n.querySelector(".ion-page");if(I=M,M==="stationary")T.forEach(z=>{z.classList.remove("modal-footer-moving"),z.style.removeProperty("position"),z.style.removeProperty("width"),z.style.removeProperty("height"),z.style.removeProperty("top"),z.style.removeProperty("left"),E?.style.removeProperty("padding-bottom"),E?.appendChild(z)});else{let z=0;T.forEach((j,le)=>{let U=j.getBoundingClientRect(),N=document.body.getBoundingClientRect();z+=j.clientHeight;let je=U.top-N.top,Me=U.left-N.left;if(j.style.setProperty("--pinned-width",`${j.clientWidth}px`),j.style.setProperty("--pinned-height",`${j.clientHeight}px`),j.style.setProperty("--pinned-top",`${je}px`),j.style.setProperty("--pinned-left",`${Me}px`),le===0){R=je;let dt=n.querySelector("ion-header");dt&&(R-=dt.clientHeight)}}),T.forEach(j=>{E?.style.setProperty("padding-bottom",`${z}px`),j.classList.add("modal-footer-moving"),j.style.setProperty("position","absolute"),j.style.setProperty("width","var(--pinned-width)"),j.style.setProperty("height","var(--pinned-height)"),j.style.setProperty("top","var(--pinned-top)"),j.style.setProperty("left","var(--pinned-left)"),document.body.appendChild(j)})}};Y&&F&&(Y.keyframes([...f.WRAPPER_KEYFRAMES]),F.keyframes([...f.BACKDROP_KEYFRAMES]),O?.keyframes([...f.CONTENT_KEYFRAMES]),r.progressStart(!0,1-h),h>i?ee():be()),b&&h!==H&&a&&(b.scrollY=!1);let st=M=>{let E=Qe(M.event.target);if(h=l(),!a&&E)return(De(E)?_(E).querySelector(".inner-scroll"):E).scrollTop===0;if(h===1&&E){let z=De(E)?_(E).querySelector(".inner-scroll"):E;return!!!E.querySelector("ion-refresher")&&z.scrollTop===0}return!0},at=M=>{if(k=n.canDismiss!==void 0&&n.canDismiss!==!0&&$===0,!a){let E=Qe(M.event.target);y=E&&De(E)?_(E).querySelector(".inner-scroll"):E}a||ue("moving"),M.deltaY>0&&b&&(b.scrollY=!1),Z(()=>{n.focus()}),r.progressStart(!0,1-h)},lt=M=>{if(!a&&R!==null&&I!==null&&(M.currentY>=R&&I==="moving"?ue("stationary"):M.currentY<R&&I==="stationary"&&ue("moving")),!a&&M.deltaY<=0&&y)return;M.deltaY>0&&b&&(b.scrollY=!1);let E=1-h,z=s.length>1?1-s[1]:void 0,j=E+M.deltaY/w,le=z!==void 0&&j>=z&&k,U=le?L:.9999,N=le&&z!==void 0?z+Dt((j-z)/(U-z)):j;C=he(1e-4,N,U),r.progressStep(C)},ct=M=>{if(!a&&M.deltaY<=0&&y&&y.scrollTop>0){ue("stationary");return}let E=M.velocityY,z=(M.deltaY+E*350)/w,j=h-z,le=s.reduce((U,N)=>Math.abs(N-j)<Math.abs(U-j)?N:U);Te({breakpoint:le,breakpointOffset:C,canDismiss:k,animated:!0})},Te=M=>{let{breakpoint:E,canDismiss:z,breakpointOffset:j,animated:le}=M,U=z&&E===0,N=U?h:E,je=N!==0;return h=0,Y&&F&&(Y.keyframes([{offset:0,transform:`translateY(${j*100}%)`},{offset:1,transform:`translateY(${(1-N)*100}%)`}]),F.keyframes([{offset:0,opacity:`calc(var(--backdrop-opacity) * ${kt(1-j,i)})`},{offset:1,opacity:`calc(var(--backdrop-opacity) * ${kt(N,i)})`}]),O&&O.keyframes([{offset:0,maxHeight:`${(1-j)*100}%`},{offset:1,maxHeight:`${N*100}%`}]),r.progressStep(0)),ve.enable(!1),U?On(n,r):je||d(),b&&(N===s[s.length-1]||!a)&&(b.scrollY=!0),!a&&N===0&&ue("stationary"),new Promise(Me=>{r.onFinish(()=>{je?(a||ue("stationary"),Y&&F?Z(()=>{Y.keyframes([...f.WRAPPER_KEYFRAMES]),F.keyframes([...f.BACKDROP_KEYFRAMES]),O?.keyframes([...f.CONTENT_KEYFRAMES]),r.progressStart(!0,1-N),h=N,p(h),h>i?ee():be(),ve.enable(!0),Me()}):(ve.enable(!0),Me())):Me()},{oneTimeCallback:!0}).progressEnd(1,0,le?500:0)})},ve=Ye({el:t,gestureName:"modalSheet",gesturePriority:40,direction:"y",threshold:10,canStart:st,onStart:at,onMove:lt,onEnd:ct});return{gesture:ve,moveSheetToBreakpoint:Te}},ei=':host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, var(--ion-background-color-step-350, #c0c0be));cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:""}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host(.modal-sheet.modal-no-expand-scroll) ion-footer{position:absolute;bottom:0;width:var(--width)}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.4)}:host(.modal-card),:host(.modal-sheet){--border-radius:10px}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:10px}}.modal-wrapper{-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0)}@media screen and (max-width: 767px){@supports (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - max(30px, var(--ion-safe-area-top)) - 10px)}}@supports not (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - 40px)}}:host(.modal-card) .modal-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0;border-end-start-radius:0}:host(.modal-card){--backdrop-opacity:0;--width:100%;-ms-flex-align:end;align-items:flex-end}:host(.modal-card) .modal-shadow{display:none}:host(.modal-card) ion-backdrop{pointer-events:none}}@media screen and (min-width: 768px){:host(.modal-card){--width:calc(100% - 120px);--height:calc(100% - (120px + var(--ion-safe-area-top) + var(--ion-safe-area-bottom)));--max-width:720px;--max-height:1000px;--backdrop-opacity:0;--box-shadow:0px 0px 30px 10px rgba(0, 0, 0, 0.1);-webkit-transition:all 0.5s ease-in-out;transition:all 0.5s ease-in-out}:host(.modal-card) .modal-wrapper{-webkit-box-shadow:none;box-shadow:none}:host(.modal-card) .modal-shadow{-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow)}}:host(.modal-sheet) .modal-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0;border-end-start-radius:0}',ti=':host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, var(--ion-background-color-step-350, #c0c0be));cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:""}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host(.modal-sheet.modal-no-expand-scroll) ion-footer{position:absolute;bottom:0;width:var(--width)}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.32)}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:2px;--box-shadow:0 28px 48px rgba(0, 0, 0, 0.4)}}.modal-wrapper{-webkit-transform:translate3d(0,  40px,  0);transform:translate3d(0,  40px,  0);opacity:0.01}',Hn=P(class extends A{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.didPresent=v(this,"ionModalDidPresent",7),this.willPresent=v(this,"ionModalWillPresent",7),this.willDismiss=v(this,"ionModalWillDismiss",7),this.didDismiss=v(this,"ionModalDidDismiss",7),this.ionBreakpointDidChange=v(this,"ionBreakpointDidChange",7),this.didPresentShorthand=v(this,"didPresent",7),this.willPresentShorthand=v(this,"willPresent",7),this.willDismissShorthand=v(this,"willDismiss",7),this.didDismissShorthand=v(this,"didDismiss",7),this.ionMount=v(this,"ionMount",7),this.lockController=Se(),this.triggerController=hn(),this.coreDelegate=Ze(),this.isSheetModal=!1,this.inheritedAttributes={},this.inline=!1,this.gestureAnimationDismissing=!1,this.presented=!1,this.hasController=!1,this.keyboardClose=!0,this.expandToScroll=!0,this.backdropBreakpoint=0,this.handleBehavior="none",this.backdropDismiss=!0,this.showBackdrop=!0,this.animated=!0,this.isOpen=!1,this.keepContentsMounted=!1,this.focusTrap=!0,this.canDismiss=!0,this.onHandleClick=()=>{let{sheetTransition:e,handleBehavior:t}=this;t!=="cycle"||e!==void 0||this.moveToNextBreakpoint()},this.onBackdropTap=()=>{let{sheetTransition:e}=this;e===void 0&&this.dismiss(void 0,Ue)},this.onLifecycle=e=>{let t=this.usersElement,o=ni[e.type];if(t&&o){let i=new CustomEvent(o,{bubbles:!1,cancelable:!1,detail:e.detail});t.dispatchEvent(i)}},this.onModalFocus=e=>{let{dragHandleEl:t,el:o}=this;e.target===o&&t&&t.tabIndex!==-1&&t.focus()}}onIsOpenChange(e,t){e===!0&&t===!1?this.present():e===!1&&t===!0&&this.dismiss()}triggerChanged(){let{trigger:e,el:t,triggerController:o}=this;e&&o.addClickListener(t,e)}breakpointsChanged(e){e!==void 0&&(this.sortedBreakpoints=e.sort((t,o)=>t-o))}connectedCallback(){let{el:e}=this;We(e),this.triggerChanged()}disconnectedCallback(){this.triggerController.removeClickListener()}componentWillLoad(){var e;let{breakpoints:t,initialBreakpoint:o,el:i,htmlAttributes:r}=this,s=this.isSheetModal=t!==void 0&&o!==void 0,a=["aria-label","role"];this.inheritedAttributes=He(i,a),r!==void 0&&a.forEach(l=>{r[l]&&(this.inheritedAttributes=Object.assign(Object.assign({},this.inheritedAttributes),{[l]:r[l]}),delete r[l])}),s&&(this.currentBreakpoint=this.initialBreakpoint),t!==void 0&&o!==void 0&&!t.includes(o)&&X("[ion-modal] - Your breakpoints array must include the initialBreakpoint value."),!((e=this.htmlAttributes)===null||e===void 0)&&e.id||qe(this.el)}componentDidLoad(){this.isOpen===!0&&Z(()=>this.present()),this.breakpointsChanged(this.breakpoints),this.triggerChanged()}getDelegate(e=!1){if(this.workingDelegate&&!e)return{delegate:this.workingDelegate,inline:this.inline};let t=this.el.parentNode,o=this.inline=t!==null&&!this.hasController,i=this.workingDelegate=o?this.delegate||this.coreDelegate:this.delegate;return{inline:o,delegate:i}}checkCanDismiss(e,t){return g(this,null,function*(){let{canDismiss:o}=this;return typeof o=="function"?o(e,t):o})}present(){return g(this,null,function*(){let e=yield this.lockController.lock();if(this.presented){e();return}let{presentingElement:t,el:o}=this;this.currentBreakpoint=this.initialBreakpoint;let{inline:i,delegate:r}=this.getDelegate(!0);this.ionMount.emit(),this.usersElement=yield Ie(r,o,this.component,["ion-page"],this.componentProps,i),de(o)?yield Ne(this.usersElement):this.keepContentsMounted||(yield _e()),K(()=>this.el.classList.add("show-modal"));let s=t!==void 0;s&&D(this)==="ios"&&(this.statusBarStyle=yield St.getStyle(),Fn()),yield Xe(this,"modalEnter",Bn,Uo,{presentingEl:t,currentBreakpoint:this.initialBreakpoint,backdropBreakpoint:this.backdropBreakpoint,expandToScroll:this.expandToScroll}),typeof window<"u"&&(this.keyboardOpenCallback=()=>{this.gesture&&(this.gesture.enable(!1),Z(()=>{this.gesture&&this.gesture.enable(!0)}))},window.addEventListener(xt,this.keyboardOpenCallback)),this.isSheetModal?this.initSheetGesture():s&&this.initSwipeToClose(),e()})}initSwipeToClose(){var e;if(D(this)!=="ios")return;let{el:t}=this,o=this.leaveAnimation||V.get("modalLeave",Rn),i=this.animation=o(t,{presentingEl:this.presentingElement,expandToScroll:this.expandToScroll});if(!Be(t)){Re(t);return}let s=(e=this.statusBarStyle)!==null&&e!==void 0?e:Fe.Default;this.gesture=Wo(t,i,s,()=>{this.gestureAnimationDismissing=!0,Et(this.statusBarStyle),this.animation.onFinish(()=>g(this,null,function*(){yield this.dismiss(void 0,Pe),this.gestureAnimationDismissing=!1}))}),this.gesture.enable(!0)}initSheetGesture(){let{wrapperEl:e,initialBreakpoint:t,backdropBreakpoint:o}=this;if(!e||t===void 0)return;let i=this.enterAnimation||V.get("modalEnter",Bn),r=this.animation=i(this.el,{presentingEl:this.presentingElement,currentBreakpoint:t,backdropBreakpoint:o,expandToScroll:this.expandToScroll});r.progressStart(!0,1);let{gesture:s,moveSheetToBreakpoint:a}=Jo(this.el,this.backdropEl,e,t,o,r,this.sortedBreakpoints,this.expandToScroll,()=>{var l;return(l=this.currentBreakpoint)!==null&&l!==void 0?l:0},()=>this.sheetOnDismiss(),l=>{this.currentBreakpoint!==l&&(this.currentBreakpoint=l,this.ionBreakpointDidChange.emit({breakpoint:l}))});this.gesture=s,this.moveSheetToBreakpoint=a,this.gesture.enable(!0)}sheetOnDismiss(){this.gestureAnimationDismissing=!0,this.animation.onFinish(()=>g(this,null,function*(){this.currentBreakpoint=0,this.ionBreakpointDidChange.emit({breakpoint:this.currentBreakpoint}),yield this.dismiss(void 0,Pe),this.gestureAnimationDismissing=!1}))}dismiss(e,t){return g(this,null,function*(){var o;if(this.gestureAnimationDismissing&&t!==Pe)return!1;let i=yield this.lockController.lock();if(t!=="handler"&&!(yield this.checkCanDismiss(e,t)))return i(),!1;let{presentingElement:r}=this;r!==void 0&&D(this)==="ios"&&Et(this.statusBarStyle),typeof window<"u"&&this.keyboardOpenCallback&&(window.removeEventListener(xt,this.keyboardOpenCallback),this.keyboardOpenCallback=void 0);let a=yield Ge(this,e,t,"modalLeave",Rn,Qo,{presentingEl:r,currentBreakpoint:(o=this.currentBreakpoint)!==null&&o!==void 0?o:this.initialBreakpoint,backdropBreakpoint:this.backdropBreakpoint,expandToScroll:this.expandToScroll});if(a){let{delegate:l}=this.getDelegate();yield Ce(l,this.usersElement),K(()=>this.el.classList.remove("show-modal")),this.animation&&this.animation.destroy(),this.gesture&&this.gesture.destroy()}return this.currentBreakpoint=void 0,this.animation=void 0,i(),a})}onDidDismiss(){return ke(this.el,"ionModalDidDismiss")}onWillDismiss(){return ke(this.el,"ionModalWillDismiss")}setCurrentBreakpoint(e){return g(this,null,function*(){if(!this.isSheetModal){X("[ion-modal] - setCurrentBreakpoint is only supported on sheet modals.");return}if(!this.breakpoints.includes(e)){X(`[ion-modal] - Attempted to set invalid breakpoint value ${e}. Please double check that the breakpoint value is part of your defined breakpoints.`);return}let{currentBreakpoint:t,moveSheetToBreakpoint:o,canDismiss:i,breakpoints:r,animated:s}=this;t!==e&&o&&(this.sheetTransition=o({breakpoint:e,breakpointOffset:1-t,canDismiss:i!==void 0&&i!==!0&&r[0]===0,animated:s}),yield this.sheetTransition,this.sheetTransition=void 0)})}getCurrentBreakpoint(){return g(this,null,function*(){return this.currentBreakpoint})}moveToNextBreakpoint(){return g(this,null,function*(){let{breakpoints:e,currentBreakpoint:t}=this;if(!e||t==null)return!1;let o=e.filter(a=>a!==0),r=(o.indexOf(t)+1)%o.length,s=o[r];return yield this.setCurrentBreakpoint(s),!0})}render(){let{handle:e,isSheetModal:t,presentingElement:o,htmlAttributes:i,handleBehavior:r,inheritedAttributes:s,focusTrap:a,expandToScroll:l}=this,d=e!==!1&&t,p=D(this),u=o!==void 0&&p==="ios",m=r==="cycle";return c(B,Object.assign({key:"8add05bb43a2cdb5e3cf180147d31eb85a018fe0","no-router":!0,tabIndex:m&&(t&&d)?0:-1},i,{style:{zIndex:`${2e4+this.overlayIndex}`},class:Object.assign({[p]:!0,"modal-default":!u&&!t,"modal-card":u,"modal-sheet":t,"modal-no-expand-scroll":t&&!l,"overlay-hidden":!0,[Ee]:a===!1},Ve(this.cssClass)),onIonBackdropTap:this.onBackdropTap,onIonModalDidPresent:this.onLifecycle,onIonModalWillPresent:this.onLifecycle,onIonModalWillDismiss:this.onLifecycle,onIonModalDidDismiss:this.onLifecycle,onFocus:this.onModalFocus}),c("ion-backdrop",{key:"90a6605a9564a699d6f66cf71cf6b506796a2963",ref:b=>this.backdropEl=b,visible:this.showBackdrop,tappable:this.backdropDismiss,part:"backdrop"}),p==="ios"&&c("div",{key:"a97d071395333bf803c0a9347bda000cf7500d8d",class:"modal-shadow"}),c("div",Object.assign({key:"e7b7985c7414a13e3ba8dcecf497b76e92edf53e",role:"dialog"},s,{"aria-modal":"true",class:"modal-wrapper ion-overlay-wrapper",part:"content",ref:b=>this.wrapperEl=b}),d&&c("button",{key:"8258b65570b11a8ee9c9df2537d6419cd2e34536",class:"modal-handle",tabIndex:m?0:-1,"aria-label":"Activate to adjust the size of the dialog overlaying the screen",onClick:m?this.onHandleClick:void 0,part:"handle",ref:b=>this.dragHandleEl=b}),c("slot",{key:"394370d0ed03ee03152f8f8abae7ff7664ca5c13"})))}get el(){return this}static get watchers(){return{isOpen:["onIsOpenChange"],trigger:["triggerChanged"]}}static get style(){return{ios:ei,md:ti}}},[33,"ion-modal",{hasController:[4,"has-controller"],overlayIndex:[2,"overlay-index"],delegate:[16],keyboardClose:[4,"keyboard-close"],enterAnimation:[16,"enter-animation"],leaveAnimation:[16,"leave-animation"],breakpoints:[16],expandToScroll:[4,"expand-to-scroll"],initialBreakpoint:[2,"initial-breakpoint"],backdropBreakpoint:[2,"backdrop-breakpoint"],handle:[4],handleBehavior:[1,"handle-behavior"],component:[1],componentProps:[16,"component-props"],cssClass:[1,"css-class"],backdropDismiss:[4,"backdrop-dismiss"],showBackdrop:[4,"show-backdrop"],animated:[4],presentingElement:[16,"presenting-element"],htmlAttributes:[16,"html-attributes"],isOpen:[4,"is-open"],trigger:[1],keepContentsMounted:[4,"keep-contents-mounted"],focusTrap:[4,"focus-trap"],canDismiss:[4,"can-dismiss"],presented:[32],present:[64],dismiss:[64],onDidDismiss:[64],onWillDismiss:[64],setCurrentBreakpoint:[64],getCurrentBreakpoint:[64]},void 0,{isOpen:["onIsOpenChange"],trigger:["triggerChanged"]}]),ni={ionModalDidPresent:"ionViewDidEnter",ionModalWillPresent:"ionViewWillEnter",ionModalWillDismiss:"ionViewWillLeave",ionModalDidDismiss:"ionViewDidLeave"};function _n(){if(typeof customElements>"u")return;["ion-modal","ion-backdrop"].forEach(e=>{switch(e){case"ion-modal":customElements.get(e)||customElements.define(e,Hn);break;case"ion-backdrop":customElements.get(e)||tt();break}})}var Nn=_n;var oi=n=>{if(!n)return{arrowWidth:0,arrowHeight:0};let{width:e,height:t}=n.getBoundingClientRect();return{arrowWidth:e,arrowHeight:t}},Vn=(n,e,t)=>{let o=e.getBoundingClientRect(),i=o.height,r=o.width;return n==="cover"&&t&&(r=t.getBoundingClientRect().width),{contentWidth:r,contentHeight:i}},ii=(n,e,t,o)=>{let i=[],s=_(o).querySelector(".popover-content");switch(e){case"hover":i=[{eventName:"mouseenter",callback:a=>{document.elementFromPoint(a.clientX,a.clientY)!==n&&t.dismiss(void 0,void 0,!1)}}];break;case"context-menu":case"click":default:i=[{eventName:"click",callback:a=>{if(a.target.closest("[data-ion-popover-trigger]")===n){a.stopPropagation();return}t.dismiss(void 0,void 0,!1)}}];break}return i.forEach(({eventName:a,callback:l})=>s.addEventListener(a,l)),()=>{i.forEach(({eventName:a,callback:l})=>s.removeEventListener(a,l))}},ri=(n,e,t)=>{let o=[];switch(e){case"hover":let i;o=[{eventName:"mouseenter",callback:r=>g(null,null,function*(){r.stopPropagation(),i&&clearTimeout(i),i=setTimeout(()=>{Z(()=>{t.presentFromTrigger(r),i=void 0})},100)})},{eventName:"mouseleave",callback:r=>{i&&clearTimeout(i);let s=r.relatedTarget;s&&s.closest("ion-popover")!==t&&t.dismiss(void 0,void 0,!1)}},{eventName:"click",callback:r=>r.stopPropagation()},{eventName:"ionPopoverActivateTrigger",callback:r=>t.presentFromTrigger(r,!0)}];break;case"context-menu":o=[{eventName:"contextmenu",callback:r=>{r.preventDefault(),t.presentFromTrigger(r)}},{eventName:"click",callback:r=>r.stopPropagation()},{eventName:"ionPopoverActivateTrigger",callback:r=>t.presentFromTrigger(r,!0)}];break;case"click":default:o=[{eventName:"click",callback:r=>t.presentFromTrigger(r)},{eventName:"ionPopoverActivateTrigger",callback:r=>t.presentFromTrigger(r,!0)}];break}return o.forEach(({eventName:i,callback:r})=>n.addEventListener(i,r)),n.setAttribute("data-ion-popover-trigger","true"),()=>{o.forEach(({eventName:i,callback:r})=>n.removeEventListener(i,r)),n.removeAttribute("data-ion-popover-trigger")}},Zn=(n,e)=>!e||e.tagName!=="ION-ITEM"?-1:n.findIndex(t=>t===e),si=(n,e)=>{let t=Zn(n,e);return n[t+1]},ai=(n,e)=>{let t=Zn(n,e);return n[t-1]},ot=n=>{let t=_(n).querySelector("button");t&&Z(()=>t.focus())},li=n=>n.hasAttribute("data-ion-popover-trigger"),ci=n=>{let e=t=>g(null,null,function*(){var o;let i=document.activeElement,r=[],s=(o=t.target)===null||o===void 0?void 0:o.tagName;if(!(s!=="ION-POPOVER"&&s!=="ION-ITEM")){try{r=Array.from(n.querySelectorAll("ion-item:not(ion-popover ion-popover *):not([disabled])"))}catch{}switch(t.key){case"ArrowLeft":(yield n.getParentPopover())&&n.dismiss(void 0,void 0,!1);break;case"ArrowDown":t.preventDefault();let l=si(r,i);l!==void 0&&ot(l);break;case"ArrowUp":t.preventDefault();let d=ai(r,i);d!==void 0&&ot(d);break;case"Home":t.preventDefault();let p=r[0];p!==void 0&&ot(p);break;case"End":t.preventDefault();let u=r[r.length-1];u!==void 0&&ot(u);break;case"ArrowRight":case" ":case"Enter":if(i&&li(i)){let m=new CustomEvent("ionPopoverActivateTrigger");i.dispatchEvent(m)}break}}});return n.addEventListener("keydown",e),()=>n.removeEventListener("keydown",e)},Wn=(n,e,t,o,i,r,s,a,l,d,p)=>{var u;let m={top:0,left:0,width:0,height:0};switch(r){case"event":if(!p)return l;let R=p;m={top:R.clientY,left:R.clientX,width:1,height:1};break;case"trigger":default:let I=p,L=d||((u=I?.detail)===null||u===void 0?void 0:u.ionShadowTarget)||I?.target;if(!L)return l;let H=L.getBoundingClientRect();m={top:H.top,left:H.left,width:H.width,height:H.height};break}let f=ui(s,m,e,t,o,i,n),b=mi(a,s,m,e,t),w=f.top+b.top,h=f.left+b.left,{arrowTop:C,arrowLeft:k}=pi(s,o,i,w,h,e,t,n),{originX:y,originY:T}=di(s,a,n);return{top:w,left:h,referenceCoordinates:m,arrowTop:C,arrowLeft:k,originX:y,originY:T}},di=(n,e,t)=>{switch(n){case"top":return{originX:Yn(e),originY:"bottom"};case"bottom":return{originX:Yn(e),originY:"top"};case"left":return{originX:"right",originY:it(e)};case"right":return{originX:"left",originY:it(e)};case"start":return{originX:t?"left":"right",originY:it(e)};case"end":return{originX:t?"right":"left",originY:it(e)}}},Yn=n=>{switch(n){case"start":return"left";case"center":return"center";case"end":return"right"}},it=n=>{switch(n){case"start":return"top";case"center":return"center";case"end":return"bottom"}},pi=(n,e,t,o,i,r,s,a)=>{let l={arrowTop:o+s/2-e/2,arrowLeft:i+r-e/2},d={arrowTop:o+s/2-e/2,arrowLeft:i-e*1.5};switch(n){case"top":return{arrowTop:o+s,arrowLeft:i+r/2-e/2};case"bottom":return{arrowTop:o-t,arrowLeft:i+r/2-e/2};case"left":return l;case"right":return d;case"start":return a?d:l;case"end":return a?l:d;default:return{arrowTop:0,arrowLeft:0}}},ui=(n,e,t,o,i,r,s)=>{let a={top:e.top,left:e.left-t-i},l={top:e.top,left:e.left+e.width+i};switch(n){case"top":return{top:e.top-o-r,left:e.left};case"right":return l;case"bottom":return{top:e.top+e.height+r,left:e.left};case"left":return a;case"start":return s?l:a;case"end":return s?a:l}},mi=(n,e,t,o,i)=>{switch(n){case"center":return hi(e,t,o,i);case"end":return fi(e,t,o,i);case"start":default:return{top:0,left:0}}},fi=(n,e,t,o)=>{switch(n){case"start":case"end":case"left":case"right":return{top:-(o-e.height),left:0};case"top":case"bottom":default:return{top:0,left:-(t-e.width)}}},hi=(n,e,t,o)=>{switch(n){case"start":case"end":case"left":case"right":return{top:-(o/2-e.height/2),left:0};case"top":case"bottom":default:return{top:0,left:-(t/2-e.width/2)}}},qn=(n,e,t,o,i,r,s,a,l,d,p,u,m=0,f=0,b=0)=>{let w=m,h=f,C=t,k=e,y,T=d,R=p,I=!1,L=!1,H=u?u.top+u.height:r/2-a/2,$=u?u.height:0,Y=!1;return C<o+l?(C=o,I=!0,T="left"):s+o+C+l>i&&(L=!0,C=i-s-o,T="right"),H+$+a>r&&(n==="top"||n==="bottom")&&(H-a>0?(k=Math.max(12,H-a-$-(b-1)),w=k+a,R="bottom",Y=!0):y=o),{top:k,left:C,bottom:y,originX:T,originY:R,checkSafeAreaLeft:I,checkSafeAreaRight:L,arrowTop:w,arrowLeft:h,addPopoverBottomClass:Y}},gi=(n,e=!1,t,o)=>!(!t&&!o||n!=="top"&&n!=="bottom"&&e),bi=5,vi=(n,e)=>{var t;let{event:o,size:i,trigger:r,reference:s,side:a,align:l}=e,d=n.ownerDocument,p=d.dir==="rtl",u=d.defaultView.innerWidth,m=d.defaultView.innerHeight,f=_(n),b=f.querySelector(".popover-content"),w=f.querySelector(".popover-arrow"),h=r||((t=o?.detail)===null||t===void 0?void 0:t.ionShadowTarget)||o?.target,{contentWidth:C,contentHeight:k}=Vn(i,b,h),{arrowWidth:y,arrowHeight:T}=oi(w),R={top:m/2-k/2,left:u/2-C/2,originX:p?"right":"left",originY:"top"},I=Wn(p,C,k,y,T,s,a,l,R,r,o),L=i==="cover"?0:bi,H=i==="cover"?0:25,{originX:$,originY:Y,top:F,left:O,bottom:ee,checkSafeAreaLeft:be,checkSafeAreaRight:ue,arrowTop:st,arrowLeft:at,addPopoverBottomClass:lt}=qn(a,I.top,I.left,L,u,m,C,k,H,I.originX,I.originY,I.referenceCoordinates,I.arrowTop,I.arrowLeft,T),ct=x(),Te=x(),ve=x();return Te.addElement(f.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),ve.addElement(f.querySelector(".popover-arrow")).addElement(f.querySelector(".popover-content")).fromTo("opacity",.01,1),ct.easing("ease").duration(100).beforeAddWrite(()=>{i==="cover"&&n.style.setProperty("--width",`${C}px`),lt&&n.classList.add("popover-bottom"),ee!==void 0&&b.style.setProperty("bottom",`${ee}px`);let M=" + var(--ion-safe-area-left, 0)",E=" - var(--ion-safe-area-right, 0)",z=`${O}px`;if(be&&(z=`${O}px${M}`),ue&&(z=`${O}px${E}`),b.style.setProperty("top",`calc(${F}px + var(--offset-y, 0))`),b.style.setProperty("left",`calc(${z} + var(--offset-x, 0))`),b.style.setProperty("transform-origin",`${Y} ${$}`),w!==null){let j=I.top!==F||I.left!==O;gi(a,j,o,r)?(w.style.setProperty("top",`calc(${st}px + var(--offset-y, 0))`),w.style.setProperty("left",`calc(${at}px + var(--offset-x, 0))`)):w.style.setProperty("display","none")}}).addAnimation([Te,ve])},yi=n=>{let e=_(n),t=e.querySelector(".popover-content"),o=e.querySelector(".popover-arrow"),i=x(),r=x(),s=x();return r.addElement(e.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",0),s.addElement(e.querySelector(".popover-arrow")).addElement(e.querySelector(".popover-content")).fromTo("opacity",.99,0),i.easing("ease").afterAddWrite(()=>{n.style.removeProperty("--width"),n.classList.remove("popover-bottom"),t.style.removeProperty("top"),t.style.removeProperty("left"),t.style.removeProperty("bottom"),t.style.removeProperty("transform-origin"),o&&(o.style.removeProperty("top"),o.style.removeProperty("left"),o.style.removeProperty("display"))}).duration(300).addAnimation([r,s])},xi=12,wi=(n,e)=>{var t;let{event:o,size:i,trigger:r,reference:s,side:a,align:l}=e,d=n.ownerDocument,p=d.dir==="rtl",u=d.defaultView.innerWidth,m=d.defaultView.innerHeight,f=_(n),b=f.querySelector(".popover-content"),w=r||((t=o?.detail)===null||t===void 0?void 0:t.ionShadowTarget)||o?.target,{contentWidth:h,contentHeight:C}=Vn(i,b,w),k={top:m/2-C/2,left:u/2-h/2,originX:p?"right":"left",originY:"top"},y=Wn(p,h,C,0,0,s,a,l,k,r,o),T=i==="cover"?0:xi,{originX:R,originY:I,top:L,left:H,bottom:$}=qn(a,y.top,y.left,T,u,m,h,C,0,y.originX,y.originY,y.referenceCoordinates),Y=x(),F=x(),O=x(),ee=x(),be=x();return F.addElement(f.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),O.addElement(f.querySelector(".popover-wrapper")).duration(150).fromTo("opacity",.01,1),ee.addElement(b).beforeStyles({top:`calc(${L}px + var(--offset-y, 0px))`,left:`calc(${H}px + var(--offset-x, 0px))`,"transform-origin":`${I} ${R}`}).beforeAddWrite(()=>{$!==void 0&&b.style.setProperty("bottom",`${$}px`)}).fromTo("transform","scale(0.8)","scale(1)"),be.addElement(f.querySelector(".popover-viewport")).fromTo("opacity",.01,1),Y.easing("cubic-bezier(0.36,0.66,0.04,1)").duration(300).beforeAddWrite(()=>{i==="cover"&&n.style.setProperty("--width",`${h}px`),I==="bottom"&&n.classList.add("popover-bottom")}).addAnimation([F,O,ee,be])},Ii=n=>{let e=_(n),t=e.querySelector(".popover-content"),o=x(),i=x(),r=x();return i.addElement(e.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",0),r.addElement(e.querySelector(".popover-wrapper")).fromTo("opacity",.99,0),o.easing("ease").afterAddWrite(()=>{n.style.removeProperty("--width"),n.classList.remove("popover-bottom"),t.style.removeProperty("top"),t.style.removeProperty("left"),t.style.removeProperty("bottom"),t.style.removeProperty("transform-origin")}).duration(150).addAnimation([i,r])},Ci=':host{--background:var(--ion-background-color, #fff);--min-width:0;--min-height:0;--max-width:auto;--height:auto;--offset-x:0px;--offset-y:0px;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);z-index:1001}:host(.popover-nested){pointer-events:none}:host(.popover-nested) .popover-wrapper{pointer-events:auto}:host(.overlay-hidden){display:none}.popover-wrapper{z-index:10}.popover-content{display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:auto;z-index:10}::slotted(.popover-viewport){--ion-safe-area-top:0px;--ion-safe-area-right:0px;--ion-safe-area-bottom:0px;--ion-safe-area-left:0px;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}:host(.popover-nested.popover-side-left){--offset-x:5px}:host(.popover-nested.popover-side-right){--offset-x:-5px}:host(.popover-nested.popover-side-start){--offset-x:5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-start),:host-context([dir=rtl]).popover-nested.popover-side-start{--offset-x:-5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-start:dir(rtl)){--offset-x:-5px}}:host(.popover-nested.popover-side-end){--offset-x:-5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-end),:host-context([dir=rtl]).popover-nested.popover-side-end{--offset-x:5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-end:dir(rtl)){--offset-x:5px}}:host{--width:200px;--max-height:90%;--box-shadow:none;--backdrop-opacity:var(--ion-backdrop-opacity, 0.08)}:host(.popover-desktop){--box-shadow:0px 4px 16px 0px rgba(0, 0, 0, 0.12)}.popover-content{border-radius:10px}:host(.popover-desktop) .popover-content{border:0.5px solid var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.popover-arrow{display:block;position:absolute;width:20px;height:10px;overflow:hidden;z-index:11}.popover-arrow::after{top:3px;border-radius:3px;position:absolute;width:14px;height:14px;-webkit-transform:rotate(45deg);transform:rotate(45deg);background:var(--background);content:"";z-index:10}.popover-arrow::after{inset-inline-start:3px}:host(.popover-bottom) .popover-arrow{top:auto;bottom:-10px}:host(.popover-bottom) .popover-arrow::after{top:-6px}:host(.popover-side-left) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}:host(.popover-side-right) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}:host(.popover-side-top) .popover-arrow{-webkit-transform:rotate(180deg);transform:rotate(180deg)}:host(.popover-side-start) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}:host-context([dir=rtl]):host(.popover-side-start) .popover-arrow,:host-context([dir=rtl]).popover-side-start .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}@supports selector(:dir(rtl)){:host(.popover-side-start:dir(rtl)) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}}:host(.popover-side-end) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}:host-context([dir=rtl]):host(.popover-side-end) .popover-arrow,:host-context([dir=rtl]).popover-side-end .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}@supports selector(:dir(rtl)){:host(.popover-side-end:dir(rtl)) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}}.popover-arrow,.popover-content{opacity:0}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){:host(.popover-translucent) .popover-content,:host(.popover-translucent) .popover-arrow::after{background:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8);-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}}',ki=":host{--background:var(--ion-background-color, #fff);--min-width:0;--min-height:0;--max-width:auto;--height:auto;--offset-x:0px;--offset-y:0px;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);z-index:1001}:host(.popover-nested){pointer-events:none}:host(.popover-nested) .popover-wrapper{pointer-events:auto}:host(.overlay-hidden){display:none}.popover-wrapper{z-index:10}.popover-content{display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:auto;z-index:10}::slotted(.popover-viewport){--ion-safe-area-top:0px;--ion-safe-area-right:0px;--ion-safe-area-bottom:0px;--ion-safe-area-left:0px;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}:host(.popover-nested.popover-side-left){--offset-x:5px}:host(.popover-nested.popover-side-right){--offset-x:-5px}:host(.popover-nested.popover-side-start){--offset-x:5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-start),:host-context([dir=rtl]).popover-nested.popover-side-start{--offset-x:-5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-start:dir(rtl)){--offset-x:-5px}}:host(.popover-nested.popover-side-end){--offset-x:-5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-end),:host-context([dir=rtl]).popover-nested.popover-side-end{--offset-x:5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-end:dir(rtl)){--offset-x:5px}}:host{--width:250px;--max-height:90%;--box-shadow:0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);--backdrop-opacity:var(--ion-backdrop-opacity, 0.32)}.popover-content{border-radius:4px;-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]) .popover-content{-webkit-transform-origin:right top;transform-origin:right top}[dir=rtl] .popover-content{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.popover-content:dir(rtl){-webkit-transform-origin:right top;transform-origin:right top}}.popover-viewport{-webkit-transition-delay:100ms;transition-delay:100ms}.popover-wrapper{opacity:0}",Xn=P(class extends A{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.didPresent=v(this,"ionPopoverDidPresent",7),this.willPresent=v(this,"ionPopoverWillPresent",7),this.willDismiss=v(this,"ionPopoverWillDismiss",7),this.didDismiss=v(this,"ionPopoverDidDismiss",7),this.didPresentShorthand=v(this,"didPresent",7),this.willPresentShorthand=v(this,"willPresent",7),this.willDismissShorthand=v(this,"willDismiss",7),this.didDismissShorthand=v(this,"didDismiss",7),this.ionMount=v(this,"ionMount",7),this.parentPopover=null,this.coreDelegate=Ze(),this.lockController=Se(),this.inline=!1,this.focusDescendantOnPresent=!1,this.presented=!1,this.hasController=!1,this.keyboardClose=!0,this.backdropDismiss=!0,this.showBackdrop=!0,this.translucent=!1,this.animated=!0,this.triggerAction="click",this.size="auto",this.dismissOnSelect=!1,this.reference="trigger",this.side="bottom",this.arrow=!0,this.isOpen=!1,this.keyboardEvents=!1,this.focusTrap=!0,this.keepContentsMounted=!1,this.onBackdropTap=()=>{this.dismiss(void 0,Ue)},this.onLifecycle=e=>{let t=this.usersElement,o=Ei[e.type];if(t&&o){let i=new CustomEvent(o,{bubbles:!1,cancelable:!1,detail:e.detail});t.dispatchEvent(i)}},this.configureTriggerInteraction=()=>{let{trigger:e,triggerAction:t,el:o,destroyTriggerInteraction:i}=this;if(i&&i(),e===void 0)return;let r=this.triggerEl=e!==void 0?document.getElementById(e):null;if(!r){X(`[ion-popover] - A trigger element with the ID "${e}" was not found in the DOM. The trigger element must be in the DOM when the "trigger" property is set on ion-popover.`,this.el);return}this.destroyTriggerInteraction=ri(r,t,o)},this.configureKeyboardInteraction=()=>{let{destroyKeyboardInteraction:e,el:t}=this;e&&e(),this.destroyKeyboardInteraction=ci(t)},this.configureDismissInteraction=()=>{let{destroyDismissInteraction:e,parentPopover:t,triggerAction:o,triggerEl:i,el:r}=this;!t||!i||(e&&e(),this.destroyDismissInteraction=ii(i,o,r,t))}}onTriggerChange(){this.configureTriggerInteraction()}onIsOpenChange(e,t){e===!0&&t===!1?this.present():e===!1&&t===!0&&this.dismiss()}connectedCallback(){let{configureTriggerInteraction:e,el:t}=this;We(t),e()}disconnectedCallback(){let{destroyTriggerInteraction:e}=this;e&&e()}componentWillLoad(){var e,t;let{el:o}=this,i=(t=(e=this.htmlAttributes)===null||e===void 0?void 0:e.id)!==null&&t!==void 0?t:qe(o);this.parentPopover=o.closest(`ion-popover:not(#${i})`),this.alignment===void 0&&(this.alignment=D(this)==="ios"?"center":"start")}componentDidLoad(){let{parentPopover:e,isOpen:t}=this;t===!0&&Z(()=>this.present()),e&&on(e,"ionPopoverWillDismiss",()=>{this.dismiss(void 0,void 0,!1)}),this.configureTriggerInteraction()}presentFromTrigger(e,t=!1){return g(this,null,function*(){this.focusDescendantOnPresent=t,yield this.present(e),this.focusDescendantOnPresent=!1})}getDelegate(e=!1){if(this.workingDelegate&&!e)return{delegate:this.workingDelegate,inline:this.inline};let t=this.el.parentNode,o=this.inline=t!==null&&!this.hasController,i=this.workingDelegate=o?this.delegate||this.coreDelegate:this.delegate;return{inline:o,delegate:i}}present(e){return g(this,null,function*(){let t=yield this.lockController.lock();if(this.presented){t();return}let{el:o}=this,{inline:i,delegate:r}=this.getDelegate(!0);this.ionMount.emit(),this.usersElement=yield Ie(r,o,this.component,["popover-viewport"],this.componentProps,i),this.keyboardEvents||this.configureKeyboardInteraction(),this.configureDismissInteraction(),de(o)?yield Ne(this.usersElement):this.keepContentsMounted||(yield _e()),yield Xe(this,"popoverEnter",vi,wi,{event:e||this.event,size:this.size,trigger:this.triggerEl,reference:this.reference,side:this.side,align:this.alignment}),this.focusDescendantOnPresent&&pn(o),t()})}dismiss(e,t,o=!0){return g(this,null,function*(){let i=yield this.lockController.lock(),{destroyKeyboardInteraction:r,destroyDismissInteraction:s}=this;o&&this.parentPopover&&this.parentPopover.dismiss(e,t,o);let a=yield Ge(this,e,t,"popoverLeave",yi,Ii,this.event);if(a){r&&(r(),this.destroyKeyboardInteraction=void 0),s&&(s(),this.destroyDismissInteraction=void 0);let{delegate:l}=this.getDelegate();yield Ce(l,this.usersElement)}return i(),a})}getParentPopover(){return g(this,null,function*(){return this.parentPopover})}onDidDismiss(){return ke(this.el,"ionPopoverDidDismiss")}onWillDismiss(){return ke(this.el,"ionPopoverWillDismiss")}render(){let e=D(this),{onLifecycle:t,parentPopover:o,dismissOnSelect:i,side:r,arrow:s,htmlAttributes:a,focusTrap:l}=this,d=J("desktop"),p=s&&!o;return c(B,Object.assign({key:"16866c02534968c982cf4730d2936d03a5107c8b","aria-modal":"true","no-router":!0,tabindex:"-1"},a,{style:{zIndex:`${2e4+this.overlayIndex}`},class:Object.assign(Object.assign({},Ve(this.cssClass)),{[e]:!0,"popover-translucent":this.translucent,"overlay-hidden":!0,"popover-desktop":d,[`popover-side-${r}`]:!0,[Ee]:l===!1,"popover-nested":!!o}),onIonPopoverDidPresent:t,onIonPopoverWillPresent:t,onIonPopoverWillDismiss:t,onIonPopoverDidDismiss:t,onIonBackdropTap:this.onBackdropTap}),!o&&c("ion-backdrop",{key:"0df258601a4d30df3c27aa8234a7d5e056c3ecbb",tappable:this.backdropDismiss,visible:this.showBackdrop,part:"backdrop"}),c("div",{key:"f94e80ed996b957b5cd09b826472b4f60e8fcc78",class:"popover-wrapper ion-overlay-wrapper",onClick:i?()=>this.dismiss():void 0},p&&c("div",{key:"185ce22f6386e8444a9cc7b8818dbfc16c463c99",class:"popover-arrow",part:"arrow"}),c("div",{key:"206202b299404e110de5397b229678cca18568d3",class:"popover-content",part:"content"},c("slot",{key:"ee543a0b92d6e35a837c0a0e4617c7b0fc4ad0b0"}))))}get el(){return this}static get watchers(){return{trigger:["onTriggerChange"],triggerAction:["onTriggerChange"],isOpen:["onIsOpenChange"]}}static get style(){return{ios:Ci,md:ki}}},[33,"ion-popover",{hasController:[4,"has-controller"],delegate:[16],overlayIndex:[2,"overlay-index"],enterAnimation:[16,"enter-animation"],leaveAnimation:[16,"leave-animation"],component:[1],componentProps:[16,"component-props"],keyboardClose:[4,"keyboard-close"],cssClass:[1,"css-class"],backdropDismiss:[4,"backdrop-dismiss"],event:[8],showBackdrop:[4,"show-backdrop"],translucent:[4],animated:[4],htmlAttributes:[16,"html-attributes"],triggerAction:[1,"trigger-action"],trigger:[1],size:[1],dismissOnSelect:[4,"dismiss-on-select"],reference:[1],side:[1],alignment:[1025],arrow:[4],isOpen:[4,"is-open"],keyboardEvents:[4,"keyboard-events"],focusTrap:[4,"focus-trap"],keepContentsMounted:[4,"keep-contents-mounted"],presented:[32],presentFromTrigger:[64],present:[64],dismiss:[64],getParentPopover:[64],onDidDismiss:[64],onWillDismiss:[64]},void 0,{trigger:["onTriggerChange"],triggerAction:["onTriggerChange"],isOpen:["onIsOpenChange"]}]),Ei={ionPopoverDidPresent:"ionViewDidEnter",ionPopoverWillPresent:"ionViewWillEnter",ionPopoverWillDismiss:"ionViewWillLeave",ionPopoverDidDismiss:"ionViewDidLeave"};function Gn(){if(typeof customElements>"u")return;["ion-popover","ion-backdrop"].forEach(e=>{switch(e){case"ion-popover":customElements.get(e)||customElements.define(e,Xn);break;case"ion-backdrop":customElements.get(e)||tt();break}})}var Un=Gn;var Di="html.plt-mobile ion-app{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}html.plt-mobile ion-app [contenteditable]{-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text}ion-app.force-statusbar-padding{--ion-safe-area-top:20px}",Si=P(class extends A{constructor(){super(),this.__registerHost()}componentDidLoad(){q.isBrowser&&ji(()=>g(this,null,function*(){let e=J(window,"hybrid");if(V.getBoolean("_testing")||import("./chunk-CJYL5T3I.js").then(i=>i.startTapClick(V)),V.getBoolean("statusTap",e)&&import("./chunk-5ZJQD5L7.js").then(i=>i.startStatusTap()),V.getBoolean("inputShims",Ti())){let i=J(window,"ios")?"ios":"android";import("./chunk-EN5Y6YEK.js").then(r=>r.startInputShims(V,i))}let t=yield import("./chunk-VCHLP75Z.js"),o=e||ht();V.getBoolean("hardwareBackButton",o)?t.startHardwareBackButton():(ht()&&X("[ion-app] - experimentalCloseWatcher was set to `true`, but hardwareBackButton was set to `false`. Both config options must be `true` for the Close Watcher API to be used."),t.blockHardwareBackButton()),typeof window<"u"&&import("./chunk-R5SHVWC7.js").then(i=>i.startKeyboardAssist(window)),import("./chunk-FA25AEA4.js").then(i=>this.focusVisible=i.startFocusVisible())}))}setFocus(e){return g(this,null,function*(){this.focusVisible&&this.focusVisible.setFocus(e)})}render(){let e=D(this);return c(B,{key:"9be440c65819e4fa67c2c3c6477ab40b3ad3eed3",class:{[e]:!0,"ion-page":!0,"force-statusbar-padding":V.getBoolean("_forceStatusbarPadding")}})}get el(){return this}static get style(){return Di}},[0,"ion-app",{setFocus:[64]}]),Ti=()=>!!(J(window,"ios")&&J(window,"mobile")||J(window,"android")&&J(window,"mobileweb")),ji=n=>{"requestIdleCallback"in window?window.requestIdleCallback(n):setTimeout(n,32)};function Mi(){if(typeof customElements>"u")return;["ion-app"].forEach(e=>{switch(e){case"ion-app":customElements.get(e)||customElements.define(e,Si);break}})}var Kn=Mi;var zi=':host{--overflow:hidden;--ripple-color:currentColor;--border-width:initial;--border-color:initial;--border-style:initial;--color-activated:var(--color);--color-focused:var(--color);--color-hover:var(--color);--box-shadow:none;display:inline-block;width:auto;color:var(--color);font-family:var(--ion-font-family, inherit);text-align:center;text-decoration:none;white-space:normal;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;vertical-align:top;vertical-align:-webkit-baseline-middle;-webkit-font-kerning:none;font-kerning:none}:host(.button-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.button-solid){--background:var(--ion-color-primary, #0054e9);--color:var(--ion-color-primary-contrast, #fff)}:host(.button-outline){--border-color:var(--ion-color-primary, #0054e9);--background:transparent;--color:var(--ion-color-primary, #0054e9)}:host(.button-clear){--border-width:0;--background:transparent;--color:var(--ion-color-primary, #0054e9)}:host(.button-block){display:block}:host(.button-block) .button-native{margin-left:0;margin-right:0;width:100%;clear:both;contain:content}:host(.button-block) .button-native::after{clear:both}:host(.button-full){display:block}:host(.button-full) .button-native{margin-left:0;margin-right:0;width:100%;contain:content}:host(.button-full:not(.button-round)) .button-native{border-radius:0;border-right-width:0;border-left-width:0}.button-native{border-radius:var(--border-radius);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;height:100%;min-height:inherit;-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);outline:none;background:var(--background);line-height:1;-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);contain:layout style;cursor:pointer;opacity:var(--opacity);overflow:var(--overflow);z-index:0;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-native::-moz-focus-inner{border:0}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;z-index:1}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted(ion-icon){font-size:1.35em;pointer-events:none}::slotted(ion-icon[slot=start]){-webkit-margin-start:-0.3em;margin-inline-start:-0.3em;-webkit-margin-end:0.3em;margin-inline-end:0.3em;margin-top:0;margin-bottom:0}::slotted(ion-icon[slot=end]){-webkit-margin-start:0.3em;margin-inline-start:0.3em;-webkit-margin-end:-0.2em;margin-inline-end:-0.2em;margin-top:0;margin-bottom:0}ion-ripple-effect{color:var(--ripple-color)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:"";opacity:0}:host(.ion-focused){color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}@media (any-hover: hover){:host(:hover){color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}}:host(.ion-activated){color:var(--color-activated)}:host(.ion-activated) .button-native::after{background:var(--background-activated);opacity:var(--background-activated-opacity)}:host(.button-solid.ion-color) .button-native{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.button-outline.ion-color) .button-native{border-color:var(--ion-color-base);background:transparent;color:var(--ion-color-base)}:host(.button-clear.ion-color) .button-native{background:transparent;color:var(--ion-color-base)}:host(.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{color:var(--ion-toolbar-color, var(--color))}:host(.button-outline.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{border-color:var(--ion-toolbar-color, var(--color, var(--border-color)))}:host(.button-solid.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{background:var(--ion-toolbar-color, var(--background));color:var(--ion-toolbar-background, var(--color))}:host{--border-radius:14px;--padding-top:13px;--padding-bottom:13px;--padding-start:1em;--padding-end:1em;--transition:background-color, opacity 100ms linear;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;margin-top:4px;margin-bottom:4px;min-height:3.1em;font-size:min(1rem, 48px);font-weight:500;letter-spacing:0}:host(.button-solid){--background-activated:var(--ion-color-primary-shade, #004acd);--background-focused:var(--ion-color-primary-shade, #004acd);--background-hover:var(--ion-color-primary-tint, #1a65eb);--background-activated-opacity:1;--background-focused-opacity:1;--background-hover-opacity:1}:host(.button-outline){--border-radius:14px;--border-width:1px;--border-style:solid;--background-activated:var(--ion-color-primary, #0054e9);--background-focused:var(--ion-color-primary, #0054e9);--background-hover:transparent;--background-focused-opacity:.1;--color-activated:var(--ion-color-primary-contrast, #fff)}:host(.button-clear){--background-activated:transparent;--background-activated-opacity:0;--background-focused:var(--ion-color-primary, #0054e9);--background-hover:transparent;--background-focused-opacity:.1;font-size:min(1.0625rem, 51px);font-weight:normal}:host(.in-buttons){font-size:clamp(17px, 1.0625rem, 21.08px);font-weight:400}:host(.button-large){--border-radius:16px;--padding-top:17px;--padding-start:1em;--padding-end:1em;--padding-bottom:17px;min-height:3.1em;font-size:min(1.25rem, 60px)}:host(.button-small){--border-radius:6px;--padding-top:4px;--padding-start:0.9em;--padding-end:0.9em;--padding-bottom:4px;min-height:2.1em;font-size:min(0.8125rem, 39px)}:host(.button-round){--border-radius:999px;--padding-top:0;--padding-start:26px;--padding-end:26px;--padding-bottom:0}:host(.button-strong){font-weight:600}:host(.button-has-icon-only){--padding-top:0;--padding-bottom:var(--padding-top);--padding-end:var(--padding-top);--padding-start:var(--padding-end);min-width:clamp(30px, 2.125em, 60px);min-height:clamp(30px, 2.125em, 60px)}::slotted(ion-icon[slot=icon-only]){font-size:clamp(15.12px, 1.125em, 43.02px)}:host(.button-small.button-has-icon-only){min-width:clamp(23px, 2.16em, 54px);min-height:clamp(23px, 2.16em, 54px)}:host(.button-small) ::slotted(ion-icon[slot=icon-only]){font-size:clamp(12.1394px, 1.308125em, 40.1856px)}:host(.button-large.button-has-icon-only){min-width:clamp(46px, 2.5em, 78px);min-height:clamp(46px, 2.5em, 78px)}:host(.button-large) ::slotted(ion-icon[slot=icon-only]){font-size:clamp(15.12px, 0.9em, 43.056px)}:host(.button-outline.ion-focused.ion-color) .button-native,:host(.button-clear.ion-focused.ion-color) .button-native{color:var(--ion-color-base)}:host(.button-outline.ion-focused.ion-color) .button-native::after,:host(.button-clear.ion-focused.ion-color) .button-native::after{background:var(--ion-color-base)}:host(.button-solid.ion-color.ion-focused) .button-native::after{background:var(--ion-color-shade)}@media (any-hover: hover){:host(.button-clear:not(.ion-activated):hover),:host(.button-outline:not(.ion-activated):hover){opacity:0.6}:host(.button-clear.ion-color:hover) .button-native,:host(.button-outline.ion-color:hover) .button-native{color:var(--ion-color-base)}:host(.button-clear.ion-color:hover) .button-native::after,:host(.button-outline.ion-color:hover) .button-native::after{background:transparent}:host(.button-solid.ion-color:hover) .button-native::after{background:var(--ion-color-tint)}:host(:hover.button-solid.in-toolbar:not(.ion-color):not(.in-toolbar-color):not(.ion-activated)) .button-native::after{background:#fff;opacity:0.1}}:host(.button-clear.ion-activated){opacity:0.4}:host(.button-outline.ion-activated.ion-color) .button-native{color:var(--ion-color-contrast)}:host(.button-outline.ion-activated.ion-color) .button-native::after{background:var(--ion-color-base)}:host(.button-solid.ion-color.ion-activated) .button-native::after{background:var(--ion-color-shade)}:host(.button-outline.ion-activated.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{background:var(--ion-toolbar-color, var(--color));color:var(--ion-toolbar-background, var(--background), var(--ion-color-primary-contrast, #fff))}',Ai=`:host{--overflow:hidden;--ripple-color:currentColor;--border-width:initial;--border-color:initial;--border-style:initial;--color-activated:var(--color);--color-focused:var(--color);--color-hover:var(--color);--box-shadow:none;display:inline-block;width:auto;color:var(--color);font-family:var(--ion-font-family, inherit);text-align:center;text-decoration:none;white-space:normal;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;vertical-align:top;vertical-align:-webkit-baseline-middle;-webkit-font-kerning:none;font-kerning:none}:host(.button-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.button-solid){--background:var(--ion-color-primary, #0054e9);--color:var(--ion-color-primary-contrast, #fff)}:host(.button-outline){--border-color:var(--ion-color-primary, #0054e9);--background:transparent;--color:var(--ion-color-primary, #0054e9)}:host(.button-clear){--border-width:0;--background:transparent;--color:var(--ion-color-primary, #0054e9)}:host(.button-block){display:block}:host(.button-block) .button-native{margin-left:0;margin-right:0;width:100%;clear:both;contain:content}:host(.button-block) .button-native::after{clear:both}:host(.button-full){display:block}:host(.button-full) .button-native{margin-left:0;margin-right:0;width:100%;contain:content}:host(.button-full:not(.button-round)) .button-native{border-radius:0;border-right-width:0;border-left-width:0}.button-native{border-radius:var(--border-radius);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;height:100%;min-height:inherit;-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);outline:none;background:var(--background);line-height:1;-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);contain:layout style;cursor:pointer;opacity:var(--opacity);overflow:var(--overflow);z-index:0;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-native::-moz-focus-inner{border:0}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;z-index:1}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted(ion-icon){font-size:1.35em;pointer-events:none}::slotted(ion-icon[slot=start]){-webkit-margin-start:-0.3em;margin-inline-start:-0.3em;-webkit-margin-end:0.3em;margin-inline-end:0.3em;margin-top:0;margin-bottom:0}::slotted(ion-icon[slot=end]){-webkit-margin-start:0.3em;margin-inline-start:0.3em;-webkit-margin-end:-0.2em;margin-inline-end:-0.2em;margin-top:0;margin-bottom:0}ion-ripple-effect{color:var(--ripple-color)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:"";opacity:0}:host(.ion-focused){color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}@media (any-hover: hover){:host(:hover){color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}}:host(.ion-activated){color:var(--color-activated)}:host(.ion-activated) .button-native::after{background:var(--background-activated);opacity:var(--background-activated-opacity)}:host(.button-solid.ion-color) .button-native{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.button-outline.ion-color) .button-native{border-color:var(--ion-color-base);background:transparent;color:var(--ion-color-base)}:host(.button-clear.ion-color) .button-native{background:transparent;color:var(--ion-color-base)}:host(.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{color:var(--ion-toolbar-color, var(--color))}:host(.button-outline.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{border-color:var(--ion-toolbar-color, var(--color, var(--border-color)))}:host(.button-solid.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{background:var(--ion-toolbar-color, var(--background));color:var(--ion-toolbar-background, var(--color))}:host{--border-radius:4px;--padding-top:8px;--padding-bottom:8px;--padding-start:1.1em;--padding-end:1.1em;--transition:box-shadow 280ms cubic-bezier(.4, 0, .2, 1),
                background-color 15ms linear,
                color 15ms linear;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;margin-top:4px;margin-bottom:4px;min-height:36px;font-size:0.875rem;font-weight:500;letter-spacing:0.06em;text-transform:uppercase}:host(.button-solid){--background-activated:transparent;--background-hover:var(--ion-color-primary-contrast, #fff);--background-focused:var(--ion-color-primary-contrast, #fff);--background-activated-opacity:0;--background-focused-opacity:.24;--background-hover-opacity:.08;--box-shadow:0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12)}:host(.button-solid.ion-activated){--box-shadow:0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12)}:host(.button-outline){--border-width:2px;--border-style:solid;--box-shadow:none;--background-activated:transparent;--background-focused:var(--ion-color-primary, #0054e9);--background-hover:var(--ion-color-primary, #0054e9);--background-activated-opacity:0;--background-focused-opacity:.12;--background-hover-opacity:.04}:host(.button-outline.ion-activated.ion-color) .button-native{background:transparent}:host(.button-clear){--background-activated:transparent;--background-focused:var(--ion-color-primary, #0054e9);--background-hover:var(--ion-color-primary, #0054e9);--background-activated-opacity:0;--background-focused-opacity:.12;--background-hover-opacity:.04}:host(.button-round){--border-radius:999px;--padding-top:0;--padding-start:26px;--padding-end:26px;--padding-bottom:0}:host(.button-large){--padding-top:14px;--padding-start:1em;--padding-end:1em;--padding-bottom:14px;min-height:2.8em;font-size:1.25rem}:host(.button-small){--padding-top:4px;--padding-start:0.9em;--padding-end:0.9em;--padding-bottom:4px;min-height:2.1em;font-size:0.8125rem}:host(.button-strong){font-weight:bold}:host(.button-has-icon-only){--padding-top:0;--padding-bottom:var(--padding-top);--padding-end:var(--padding-top);--padding-start:var(--padding-end);min-width:clamp(30px, 2.86em, 60px);min-height:clamp(30px, 2.86em, 60px)}::slotted(ion-icon[slot=icon-only]){font-size:clamp(15.104px, 1.6em, 43.008px)}:host(.button-small.button-has-icon-only){min-width:clamp(23px, 2.16em, 54px);min-height:clamp(23px, 2.16em, 54px)}:host(.button-small) ::slotted(ion-icon[slot=icon-only]){font-size:clamp(13.002px, 1.23125em, 40.385px)}:host(.button-large.button-has-icon-only){min-width:clamp(46px, 2.5em, 78px);min-height:clamp(46px, 2.5em, 78px)}:host(.button-large) ::slotted(ion-icon[slot=icon-only]){font-size:clamp(15.008px, 1.4em, 43.008px)}:host(.button-solid.ion-color.ion-focused) .button-native::after{background:var(--ion-color-contrast)}:host(.button-clear.ion-color.ion-focused) .button-native::after,:host(.button-outline.ion-color.ion-focused) .button-native::after{background:var(--ion-color-base)}@media (any-hover: hover){:host(.button-solid.ion-color:hover) .button-native::after{background:var(--ion-color-contrast)}:host(.button-clear.ion-color:hover) .button-native::after,:host(.button-outline.ion-color:hover) .button-native::after{background:var(--ion-color-base)}}:host(.button-outline.ion-activated.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{background:var(--ion-toolbar-background, var(--color));color:var(--ion-toolbar-color, var(--background), var(--ion-color-primary-contrast, #fff))}`,Qn=P(class extends A{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionFocus=v(this,"ionFocus",7),this.ionBlur=v(this,"ionBlur",7),this.inItem=!1,this.inListHeader=!1,this.inToolbar=!1,this.formButtonEl=null,this.formEl=null,this.inheritedAttributes={},this.isCircle=!1,this.buttonType="button",this.disabled=!1,this.routerDirection="forward",this.strong=!1,this.type="button",this.handleClick=e=>{let{el:t}=this;this.type==="button"?dn(this.href,e,this.routerDirection,this.routerAnimation):rn(t)&&this.submitForm(e)},this.onFocus=()=>{this.ionFocus.emit()},this.onBlur=()=>{this.ionBlur.emit()},this.slotChanged=()=>{this.isCircle=this.hasIconOnly}}disabledChanged(){let{disabled:e}=this;this.formButtonEl&&(this.formButtonEl.disabled=e)}onAriaChanged(e,t,o){this.inheritedAttributes=Object.assign(Object.assign({},this.inheritedAttributes),{[o]:e}),ae(this)}renderHiddenButton(){let e=this.formEl=this.findForm();if(e){let{formButtonEl:t}=this;if(t!==null&&e.contains(t))return;let o=this.formButtonEl=document.createElement("button");o.type=this.type,o.style.display="none",o.disabled=this.disabled,e.appendChild(o)}}componentWillLoad(){this.inToolbar=!!this.el.closest("ion-buttons"),this.inListHeader=!!this.el.closest("ion-list-header"),this.inItem=!!this.el.closest("ion-item")||!!this.el.closest("ion-item-divider"),this.inheritedAttributes=fe(this.el)}get hasIconOnly(){return!!this.el.querySelector('[slot="icon-only"]')}get rippleType(){return(this.fill===void 0||this.fill==="clear")&&this.hasIconOnly&&this.inToolbar?"unbounded":"bounded"}findForm(){let{form:e}=this;if(e instanceof HTMLFormElement)return e;if(typeof e=="string"){let t=document.getElementById(e);return t?t instanceof HTMLFormElement?t:(X(`[ion-button] - Form with selector: "#${e}" could not be found. Verify that the id is attached to a <form> element.`,this.el),null):(X(`[ion-button] - Form with selector: "#${e}" could not be found. Verify that the id is correct and the form is rendered in the DOM.`,this.el),null)}return e!==void 0?(X('[ion-button] - The provided "form" element is invalid. Verify that the form is a HTMLFormElement and rendered in the DOM.',this.el),null):this.el.closest("form")}submitForm(e){this.formEl&&this.formButtonEl&&(e.preventDefault(),this.formButtonEl.click())}render(){let e=D(this),{buttonType:t,type:o,disabled:i,rel:r,target:s,size:a,href:l,color:d,expand:p,hasIconOnly:u,shape:m,strong:f,inheritedAttributes:b}=this,w=a===void 0&&this.inItem?"small":a,h=l===void 0?"button":"a",C=h==="button"?{type:o}:{download:this.download,href:l,rel:r,target:s},k=this.fill;return k==null&&(k=this.inToolbar||this.inListHeader?"clear":"solid"),o!=="button"&&this.renderHiddenButton(),c(B,{key:"b105ad09215adb3ca2298acdadf0dc9154bbb9b0",onClick:this.handleClick,"aria-disabled":i?"true":null,class:G(d,{[e]:!0,[t]:!0,[`${t}-${p}`]:p!==void 0,[`${t}-${w}`]:w!==void 0,[`${t}-${m}`]:m!==void 0,[`${t}-${k}`]:!0,[`${t}-strong`]:f,"in-toolbar":W("ion-toolbar",this.el),"in-toolbar-color":W("ion-toolbar[color]",this.el),"in-buttons":W("ion-buttons",this.el),"button-has-icon-only":u,"button-disabled":i,"ion-activatable":!0,"ion-focusable":!0})},c(h,Object.assign({key:"66b4e7112bcb9e41d5a723fbbadb0a3104f9ee1d"},C,{class:"button-native",part:"native",disabled:i,onFocus:this.onFocus,onBlur:this.onBlur},b),c("span",{key:"1439fc3da280221028dcf7ce8ec9dab273c4d4bb",class:"button-inner"},c("slot",{key:"d5269ae1afc87ec7b99746032f59cbae93720a9f",name:"icon-only",onSlotchange:this.slotChanged}),c("slot",{key:"461c83e97aa246aa86d83e14f1e15a288d35041e",name:"start"}),c("slot",{key:"807170d47101f9f6a333dd4ff489c89284f306fe"}),c("slot",{key:"e67f116dd0349a0d27893e4f3ff0ccef1d402f80",name:"end"})),e==="md"&&c("ion-ripple-effect",{key:"273f0bd9645a36c1bfd18a5c2ab4f81e22b7b989",type:this.rippleType})))}get el(){return this}static get watchers(){return{disabled:["disabledChanged"],"aria-checked":["onAriaChanged"],"aria-label":["onAriaChanged"]}}static get style(){return{ios:zi,md:Ai}}},[33,"ion-button",{color:[513],buttonType:[1025,"button-type"],disabled:[516],expand:[513],fill:[1537],routerDirection:[1,"router-direction"],routerAnimation:[16,"router-animation"],download:[1],href:[1],rel:[1],shape:[513],size:[513],strong:[4],target:[1],type:[1],form:[1],isCircle:[32]},void 0,{disabled:["disabledChanged"],"aria-checked":["onAriaChanged"],"aria-label":["onAriaChanged"]}]);function Jn(){if(typeof customElements>"u")return;["ion-button","ion-ripple-effect"].forEach(e=>{switch(e){case"ion-button":customElements.get(e)||customElements.define(e,Qn);break;case"ion-ripple-effect":customElements.get(e)||zn();break}})}var eo=Jn;var Pi=':host{--background:var(--ion-background-color, #fff);--color:var(--ion-text-color, #000);--padding-top:0px;--padding-bottom:0px;--padding-start:0px;--padding-end:0px;--keyboard-offset:0px;--offset-top:0px;--offset-bottom:0px;--overflow:auto;display:block;position:relative;-ms-flex:1;flex:1;width:100%;height:100%;margin:0 !important;padding:0 !important;font-family:var(--ion-font-family, inherit);contain:size style}:host(.ion-color) .inner-scroll{background:var(--ion-color-base);color:var(--ion-color-contrast)}#background-content{left:0px;right:0px;top:calc(var(--offset-top) * -1);bottom:calc(var(--offset-bottom) * -1);position:absolute;background:var(--background)}.inner-scroll{left:0px;right:0px;top:calc(var(--offset-top) * -1);bottom:calc(var(--offset-bottom) * -1);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:calc(var(--padding-top) + var(--offset-top));padding-bottom:calc(var(--padding-bottom) + var(--keyboard-offset) + var(--offset-bottom));position:absolute;color:var(--color);-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden;-ms-touch-action:pan-x pan-y pinch-zoom;touch-action:pan-x pan-y pinch-zoom}.scroll-y,.scroll-x{-webkit-overflow-scrolling:touch;z-index:0;will-change:scroll-position}.scroll-y{overflow-y:var(--overflow);overscroll-behavior-y:contain}.scroll-x{overflow-x:var(--overflow);overscroll-behavior-x:contain}.overscroll::before,.overscroll::after{position:absolute;width:1px;height:1px;content:""}.overscroll::before{bottom:-1px}.overscroll::after{top:-1px}:host(.content-sizing){display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;min-height:0;contain:none}:host(.content-sizing) .inner-scroll{position:relative;top:0;bottom:0;margin-top:calc(var(--offset-top) * -1);margin-bottom:calc(var(--offset-bottom) * -1)}.transition-effect{display:none;position:absolute;width:100%;height:100vh;opacity:0;pointer-events:none}:host(.content-ltr) .transition-effect{left:-100%;}:host(.content-rtl) .transition-effect{right:-100%;}.transition-cover{position:absolute;right:0;width:100%;height:100%;background:black;opacity:0.1}.transition-shadow{display:block;position:absolute;width:100%;height:100%;-webkit-box-shadow:inset -9px 0 9px 0 rgba(0, 0, 100, 0.03);box-shadow:inset -9px 0 9px 0 rgba(0, 0, 100, 0.03)}:host(.content-ltr) .transition-shadow{right:0;}:host(.content-rtl) .transition-shadow{left:0;-webkit-transform:scaleX(-1);transform:scaleX(-1)}::slotted([slot=fixed]){position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0)}',to=P(class extends A{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionScrollStart=v(this,"ionScrollStart",7),this.ionScroll=v(this,"ionScroll",7),this.ionScrollEnd=v(this,"ionScrollEnd",7),this.watchDog=null,this.isScrolling=!1,this.lastScroll=0,this.queued=!1,this.cTop=-1,this.cBottom=-1,this.isMainContent=!0,this.resizeTimeout=null,this.inheritedAttributes={},this.tabsElement=null,this.detail={scrollTop:0,scrollLeft:0,type:"scroll",event:void 0,startX:0,startY:0,startTime:0,currentX:0,currentY:0,velocityX:0,velocityY:0,deltaX:0,deltaY:0,currentTime:0,data:void 0,isScrolling:!0},this.fullscreen=!1,this.fixedSlotPlacement="after",this.scrollX=!1,this.scrollY=!0,this.scrollEvents=!1}componentWillLoad(){this.inheritedAttributes=fe(this.el)}connectedCallback(){if(this.isMainContent=this.el.closest("ion-menu, ion-popover, ion-modal")===null,de(this.el)){let e=this.tabsElement=this.el.closest("ion-tabs");e!==null&&(this.tabsLoadCallback=()=>this.resize(),e.addEventListener("ionTabBarLoaded",this.tabsLoadCallback))}}disconnectedCallback(){if(this.onScrollEnd(),de(this.el)){let{tabsElement:e,tabsLoadCallback:t}=this;e!==null&&t!==void 0&&e.removeEventListener("ionTabBarLoaded",t),this.tabsElement=null,this.tabsLoadCallback=void 0}}onResize(){this.resizeTimeout&&(clearTimeout(this.resizeTimeout),this.resizeTimeout=null),this.resizeTimeout=setTimeout(()=>{this.el.offsetParent!==null&&this.resize()},100)}shouldForceOverscroll(){let{forceOverscroll:e}=this,t=D(this);return e===void 0?t==="ios"&&J("ios"):e}resize(){q.isBrowser&&(this.fullscreen?me(()=>this.readDimensions()):(this.cTop!==0||this.cBottom!==0)&&(this.cTop=this.cBottom=0,ae(this)))}readDimensions(){let e=Ri(this.el),t=Math.max(this.el.offsetTop,0),o=Math.max(e.offsetHeight-t-this.el.offsetHeight,0);(t!==this.cTop||o!==this.cBottom)&&(this.cTop=t,this.cBottom=o,ae(this))}onScroll(e){let t=Date.now(),o=!this.isScrolling;this.lastScroll=t,o&&this.onScrollStart(),!this.queued&&this.scrollEvents&&(this.queued=!0,me(i=>{this.queued=!1,this.detail.event=e,Fi(this.detail,this.scrollEl,i,o),this.ionScroll.emit(this.detail)}))}getScrollElement(){return g(this,null,function*(){return this.scrollEl||(yield new Promise(e=>Ae(this.el,e))),Promise.resolve(this.scrollEl)})}getBackgroundElement(){return g(this,null,function*(){return this.backgroundContentEl||(yield new Promise(e=>Ae(this.el,e))),Promise.resolve(this.backgroundContentEl)})}scrollToTop(e=0){return this.scrollToPoint(void 0,0,e)}scrollToBottom(e=0){return g(this,null,function*(){let t=yield this.getScrollElement(),o=t.scrollHeight-t.clientHeight;return this.scrollToPoint(void 0,o,e)})}scrollByPoint(e,t,o){return g(this,null,function*(){let i=yield this.getScrollElement();return this.scrollToPoint(e+i.scrollLeft,t+i.scrollTop,o)})}scrollToPoint(e,t,o=0){return g(this,null,function*(){let i=yield this.getScrollElement();if(o<32){t!=null&&(i.scrollTop=t),e!=null&&(i.scrollLeft=e);return}let r,s=0,a=new Promise(f=>r=f),l=i.scrollTop,d=i.scrollLeft,p=t!=null?t-l:0,u=e!=null?e-d:0,m=f=>{let b=Math.min(1,(f-s)/o)-1,w=Math.pow(b,3)+1;p!==0&&(i.scrollTop=Math.floor(w*p+l)),u!==0&&(i.scrollLeft=Math.floor(w*u+d)),w<1?requestAnimationFrame(m):r()};return requestAnimationFrame(f=>{s=f,m(f)}),a})}onScrollStart(){this.isScrolling=!0,this.ionScrollStart.emit({isScrolling:!0}),this.watchDog&&clearInterval(this.watchDog),this.watchDog=setInterval(()=>{this.lastScroll<Date.now()-120&&this.onScrollEnd()},100)}onScrollEnd(){this.watchDog&&clearInterval(this.watchDog),this.watchDog=null,this.isScrolling&&(this.isScrolling=!1,this.ionScrollEnd.emit({isScrolling:!1}))}render(){let{fixedSlotPlacement:e,inheritedAttributes:t,isMainContent:o,scrollX:i,scrollY:r,el:s}=this,a=wn(s)?"rtl":"ltr",l=D(this),d=this.shouldForceOverscroll(),p=l==="ios";return this.resize(),c(B,Object.assign({key:"f2a24aa66dbf5c76f9d4b06f708eb73cadc239df",role:o?"main":void 0,class:G(this.color,{[l]:!0,"content-sizing":W("ion-popover",this.el),overscroll:d,[`content-${a}`]:!0}),style:{"--offset-top":`${this.cTop}px`,"--offset-bottom":`${this.cBottom}px`}},t),c("div",{key:"6480ca7648b278abb36477b3838bccbcd4995e2a",ref:u=>this.backgroundContentEl=u,id:"background-content",part:"background"}),e==="before"?c("slot",{name:"fixed"}):null,c("div",{key:"29a23b663f5f0215bb000820c01e1814c0d55985",class:{"inner-scroll":!0,"scroll-x":i,"scroll-y":r,overscroll:(i||r)&&d},ref:u=>this.scrollEl=u,onScroll:this.scrollEvents?u=>this.onScroll(u):void 0,part:"scroll"},c("slot",{key:"0fe1bd05609a4b88ae2ce9addf5d5dc5dc1806f0"})),p?c("div",{class:"transition-effect"},c("div",{class:"transition-cover"}),c("div",{class:"transition-shadow"})):null,e==="after"?c("slot",{name:"fixed"}):null)}get el(){return this}static get style(){return Pi}},[1,"ion-content",{color:[513],fullscreen:[4],fixedSlotPlacement:[1,"fixed-slot-placement"],forceOverscroll:[1028,"force-overscroll"],scrollX:[4,"scroll-x"],scrollY:[4,"scroll-y"],scrollEvents:[4,"scroll-events"],getScrollElement:[64],getBackgroundElement:[64],scrollToTop:[64],scrollToBottom:[64],scrollByPoint:[64],scrollToPoint:[64]},[[9,"resize","onResize"]]]),Bi=n=>{var e;return n.parentElement?n.parentElement:!((e=n.parentNode)===null||e===void 0)&&e.host?n.parentNode.host:null},Ri=n=>{let e=n.closest("ion-tabs");if(e)return e;let t=n.closest("ion-app, ion-page, .ion-page, page-inner, .popover-content");return t||Bi(n)},Fi=(n,e,t,o)=>{let i=n.currentX,r=n.currentY,s=n.currentTime,a=e.scrollLeft,l=e.scrollTop,d=t-s;if(o&&(n.startTime=t,n.startX=a,n.startY=l,n.velocityX=n.velocityY=0),n.currentTime=t,n.currentX=n.scrollLeft=a,n.currentY=n.scrollTop=l,n.deltaX=a-n.startX,n.deltaY=l-n.startY,d>0&&d<100){let p=(a-i)/d,u=(l-r)/d;n.velocityX=p*.7+n.velocityX*.3,n.velocityY=u*.7+n.velocityY*.3}};function no(){if(typeof customElements>"u")return;["ion-content"].forEach(e=>{switch(e){case"ion-content":customElements.get(e)||customElements.define(e,to);break}})}var oo=no;var Oi="all 0.2s ease-in-out",io=n=>{let e=document.querySelector(`${n}.ion-cloned-element`);if(e!==null)return e;let t=document.createElement(n);return t.classList.add("ion-cloned-element"),t.style.setProperty("display","none"),document.body.appendChild(t),t},ro=n=>{if(!n)return;let e=n.querySelectorAll("ion-toolbar");return{el:n,toolbars:Array.from(e).map(t=>{let o=t.querySelector("ion-title");return{el:t,background:t.shadowRoot.querySelector(".toolbar-background"),ionTitleEl:o,innerTitleEl:o?o.shadowRoot.querySelector(".toolbar-title"):null,ionButtonsEl:Array.from(t.querySelectorAll("ion-buttons"))}})}},Li=(n,e,t)=>{me(()=>{let o=n.scrollTop,i=he(1,1+-o/500,1.1);t.querySelector("ion-refresher.refresher-native")===null&&K(()=>{_i(e.toolbars,i)})})},Tt=(n,e)=>{n.collapse!=="fade"&&(e===void 0?n.style.removeProperty("--opacity-scale"):n.style.setProperty("--opacity-scale",e.toString()))},$i=(n,e,t)=>{if(!n[0].isIntersecting)return;let o=n[0].intersectionRatio>.9||t<=0?0:(1-n[0].intersectionRatio)*100/75;Tt(e.el,o===1?void 0:o)},Hi=(n,e,t,o)=>{K(()=>{let i=o.scrollTop;$i(n,e,i);let r=n[0],s=r.intersectionRect,a=s.width*s.height,l=r.rootBounds.width*r.rootBounds.height,d=a===0&&l===0,p=Math.abs(s.left-r.boundingClientRect.left),u=Math.abs(s.right-r.boundingClientRect.right),m=a>0&&(p>=5||u>=5);d||m||(r.isIntersecting?(Oe(e,!1),Oe(t)):(s.x===0&&s.y===0||s.width!==0&&s.height!==0)&&i>0&&(Oe(e),Oe(t,!1),Tt(e.el)))})},Oe=(n,e=!0)=>{let t=n.el,i=n.toolbars.map(r=>r.ionTitleEl);e?(t.classList.remove("header-collapse-condense-inactive"),i.forEach(r=>{r&&r.removeAttribute("aria-hidden")})):(t.classList.add("header-collapse-condense-inactive"),i.forEach(r=>{r&&r.setAttribute("aria-hidden","true")}))},_i=(n=[],e=1,t=!1)=>{n.forEach(o=>{let i=o.ionTitleEl,r=o.innerTitleEl;!i||i.size!=="large"||(r.style.transition=t?Oi:"",r.style.transform=`scale3d(${e}, ${e}, 1)`)})},so=(n,e,t)=>{me(()=>{let o=n.scrollTop,i=e.clientHeight,r=t?t.clientHeight:0;if(t!==null&&o<r){e.style.setProperty("--opacity-scale","0"),n.style.setProperty("clip-path",`inset(${i}px 0px 0px 0px)`);return}let s=o-r,l=he(0,s/10,1);K(()=>{n.style.removeProperty("clip-path"),e.style.setProperty("--opacity-scale",l.toString())})})},Ni="ion-header{display:block;position:relative;-ms-flex-order:-1;order:-1;width:100%;z-index:10}ion-header ion-toolbar:first-of-type{padding-top:var(--ion-safe-area-top, 0)}.header-ios ion-toolbar:last-of-type{--border-width:0 0 0.55px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.header-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}.header-translucent-ios ion-toolbar{--opacity:.8}.header-collapse-condense-inactive .header-background{-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px)}}.header-ios.ion-no-border ion-toolbar:last-of-type{--border-width:0}.header-collapse-fade ion-toolbar{--opacity-scale:inherit}.header-collapse-condense{z-index:9}.header-collapse-condense ion-toolbar{position:-webkit-sticky;position:sticky;top:0}.header-collapse-condense ion-toolbar:first-of-type{padding-top:0px;z-index:1}.header-collapse-condense ion-toolbar{--background:var(--ion-background-color, #fff);z-index:0}.header-collapse-condense ion-toolbar:last-of-type{--border-width:0px}.header-collapse-condense ion-toolbar ion-searchbar{padding-top:0px;padding-bottom:13px}.header-collapse-main{--opacity-scale:1}.header-collapse-main ion-toolbar{--opacity-scale:inherit}.header-collapse-main ion-toolbar.in-toolbar ion-title,.header-collapse-main ion-toolbar.in-toolbar ion-buttons{-webkit-transition:all 0.2s ease-in-out;transition:all 0.2s ease-in-out}.header-collapse-condense-inactive:not(.header-collapse-condense) ion-toolbar.in-toolbar ion-title,.header-collapse-condense-inactive:not(.header-collapse-condense) ion-toolbar.in-toolbar ion-buttons.buttons-collapse{opacity:0;pointer-events:none}.header-collapse-condense-inactive.header-collapse-condense ion-toolbar.in-toolbar ion-title,.header-collapse-condense-inactive.header-collapse-condense ion-toolbar.in-toolbar ion-buttons.buttons-collapse{visibility:hidden}ion-header.header-ios:not(.header-collapse-main):has(~ion-content ion-header.header-ios[collapse=condense],~ion-content ion-header.header-ios.header-collapse-condense){opacity:0}",Yi="ion-header{display:block;position:relative;-ms-flex-order:-1;order:-1;width:100%;z-index:10}ion-header ion-toolbar:first-of-type{padding-top:var(--ion-safe-area-top, 0)}.header-md{-webkit-box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12)}.header-collapse-condense{display:none}.header-md.ion-no-border{-webkit-box-shadow:none;box-shadow:none}",ao=P(class extends A{constructor(){super(),this.__registerHost(),this.inheritedAttributes={},this.translucent=!1,this.setupFadeHeader=(e,t)=>g(this,null,function*(){let o=this.scrollEl=yield vt(e);this.contentScrollCallback=()=>{so(this.scrollEl,this.el,t)},o.addEventListener("scroll",this.contentScrollCallback),so(this.scrollEl,this.el,t)})}componentWillLoad(){this.inheritedAttributes=fe(this.el)}componentDidLoad(){this.checkCollapsibleHeader()}componentDidUpdate(){this.checkCollapsibleHeader()}disconnectedCallback(){this.destroyCollapsibleHeader()}checkCollapsibleHeader(){return g(this,null,function*(){if(D(this)!=="ios")return;let{collapse:t}=this,o=t==="condense",i=t==="fade";if(this.destroyCollapsibleHeader(),o){let r=this.el.closest("ion-app,ion-page,.ion-page,page-inner"),s=r?Be(r):null;K(()=>{let a=io("ion-title");a.size="large",io("ion-back-button")}),yield this.setupCondenseHeader(s,r)}else if(i){let r=this.el.closest("ion-app,ion-page,.ion-page,page-inner"),s=r?Be(r):null;if(!s){Re(this.el);return}let a=s.querySelector('ion-header[collapse="condense"]');yield this.setupFadeHeader(s,a)}})}destroyCollapsibleHeader(){this.intersectionObserver&&(this.intersectionObserver.disconnect(),this.intersectionObserver=void 0),this.scrollEl&&this.contentScrollCallback&&(this.scrollEl.removeEventListener("scroll",this.contentScrollCallback),this.contentScrollCallback=void 0),this.collapsibleMainHeader&&(this.collapsibleMainHeader.classList.remove("header-collapse-main"),this.collapsibleMainHeader=void 0)}setupCondenseHeader(e,t){return g(this,null,function*(){if(!e||!t){Re(this.el);return}if(typeof IntersectionObserver>"u")return;this.scrollEl=yield vt(e);let o=t.querySelectorAll("ion-header");if(this.collapsibleMainHeader=Array.from(o).find(a=>a.collapse!=="condense"),!this.collapsibleMainHeader)return;let i=ro(this.collapsibleMainHeader),r=ro(this.el);if(!i||!r)return;Oe(i,!1),Tt(i.el,0);let s=a=>{Hi(a,i,r,this.scrollEl)};this.intersectionObserver=new IntersectionObserver(s,{root:e,threshold:[.25,.3,.4,.5,.6,.7,.8,.9,1]}),this.intersectionObserver.observe(r.toolbars[r.toolbars.length-1].el),this.contentScrollCallback=()=>{Li(this.scrollEl,r,e)},this.scrollEl.addEventListener("scroll",this.contentScrollCallback),K(()=>{this.collapsibleMainHeader!==void 0&&this.collapsibleMainHeader.classList.add("header-collapse-main")})})}render(){let{translucent:e,inheritedAttributes:t}=this,o=D(this),i=this.collapse||"none",r=W("ion-menu",this.el)?"none":"banner";return c(B,Object.assign({key:"b6cc27f0b08afc9fcc889683525da765d80ba672",role:r,class:{[o]:!0,[`header-${o}`]:!0,"header-translucent":this.translucent,[`header-collapse-${i}`]:!0,[`header-translucent-${o}`]:this.translucent}},t),o==="ios"&&e&&c("div",{key:"395766d4dcee3398bc91960db21f922095292f14",class:"header-background"}),c("slot",{key:"09a67ece27b258ff1248805d43d92a49b2c6859a"}))}get el(){return this}static get style(){return{ios:Ni,md:Yi}}},[36,"ion-header",{collapse:[1],translucent:[4]}]);function lo(){if(typeof customElements>"u")return;["ion-header"].forEach(e=>{switch(e){case"ion-header":customElements.get(e)||customElements.define(e,ao);break}})}var co=lo;var Vi=":host{--color:initial;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}.toolbar-title{display:block;width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;pointer-events:auto}:host(.title-small) .toolbar-title{white-space:normal}:host{top:0;-webkit-padding-start:90px;padding-inline-start:90px;-webkit-padding-end:90px;padding-inline-end:90px;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);position:absolute;width:100%;height:100%;-webkit-transform:translateZ(0);transform:translateZ(0);font-size:min(1.0625rem, 20.4px);font-weight:600;text-align:center;-webkit-box-sizing:border-box;box-sizing:border-box;pointer-events:none}:host{inset-inline-start:0}:host(.title-small){-webkit-padding-start:9px;padding-inline-start:9px;-webkit-padding-end:9px;padding-inline-end:9px;padding-top:6px;padding-bottom:16px;position:relative;font-size:min(0.8125rem, 23.4px);font-weight:normal}:host(.title-large){-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:2px;padding-bottom:4px;-webkit-transform-origin:left center;transform-origin:left center;position:static;-ms-flex-align:end;align-items:flex-end;min-width:100%;font-size:min(2.125rem, 61.2px);font-weight:700;text-align:start}:host(.title-large.title-rtl){-webkit-transform-origin:right center;transform-origin:right center}:host(.title-large.ion-cloned-element){--color:var(--ion-text-color, #000);font-family:var(--ion-font-family)}:host(.title-large) .toolbar-title{-webkit-transform-origin:inherit;transform-origin:inherit;width:auto}:host-context([dir=rtl]):host(.title-large) .toolbar-title,:host-context([dir=rtl]).title-large .toolbar-title{-webkit-transform-origin:calc(100% - inherit);transform-origin:calc(100% - inherit)}@supports selector(:dir(rtl)){:host(.title-large:dir(rtl)) .toolbar-title{-webkit-transform-origin:calc(100% - inherit);transform-origin:calc(100% - inherit)}}",Zi=":host{--color:initial;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}.toolbar-title{display:block;width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;pointer-events:auto}:host(.title-small) .toolbar-title{white-space:normal}:host{-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:20px;padding-inline-end:20px;padding-top:0;padding-bottom:0;font-size:1.25rem;font-weight:500;letter-spacing:0.0125em}:host(.title-small){width:100%;height:100%;font-size:0.9375rem;font-weight:normal}",po=P(class extends A{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionStyle=v(this,"ionStyle",7)}sizeChanged(){this.emitStyle()}connectedCallback(){this.emitStyle()}emitStyle(){let e=this.getSize();this.ionStyle.emit({[`title-${e}`]:!0})}getSize(){return this.size!==void 0?this.size:"default"}render(){let e=D(this),t=this.getSize();return c(B,{key:"e599c0bf1b0817df3fa8360bdcd6d787f751c371",class:G(this.color,{[e]:!0,[`title-${t}`]:!0,"title-rtl":document.dir==="rtl"})},c("div",{key:"6e7eee9047d6759876bb31d7305b76efc7c4338c",class:"toolbar-title"},c("slot",{key:"bf790eb4c83dd0af4f2fd1f85ab4af5819f46ff4"})))}get el(){return this}static get watchers(){return{size:["sizeChanged"]}}static get style(){return{ios:Vi,md:Zi}}},[33,"ion-title",{color:[513],size:[1]},void 0,{size:["sizeChanged"]}]);function uo(){if(typeof customElements>"u")return;["ion-title"].forEach(e=>{switch(e){case"ion-title":customElements.get(e)||customElements.define(e,po);break}})}var Wi=":host{--border-width:0;--border-style:solid;--opacity:1;--opacity-scale:1;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;width:100%;padding-right:var(--ion-safe-area-right);padding-left:var(--ion-safe-area-left);color:var(--color);font-family:var(--ion-font-family, inherit);contain:content;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-contrast)}:host(.ion-color) .toolbar-background{background:var(--ion-color-base)}.toolbar-container{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:var(--min-height);contain:content;overflow:hidden;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}.toolbar-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;opacity:calc(var(--opacity) * var(--opacity-scale));z-index:-1;pointer-events:none}::slotted(ion-progress-bar){left:0;right:0;bottom:0;position:absolute}:host{--background:var(--ion-toolbar-background, var(--ion-color-step-50, var(--ion-background-color-step-50, #f7f7f7)));--color:var(--ion-toolbar-color, var(--ion-text-color, #000));--border-color:var(--ion-toolbar-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.2)))));--padding-top:3px;--padding-bottom:3px;--padding-start:4px;--padding-end:4px;--min-height:44px}.toolbar-content{-ms-flex:1;flex:1;-ms-flex-order:4;order:4;min-width:0}:host(.toolbar-segment) .toolbar-content{display:-ms-inline-flexbox;display:inline-flex}:host(.toolbar-searchbar) .toolbar-container{padding-top:0;padding-bottom:0}:host(.toolbar-searchbar) ::slotted(*){-ms-flex-item-align:start;align-self:start}:host(.toolbar-searchbar) ::slotted(ion-chip){margin-top:3px}::slotted(ion-buttons){min-height:38px}::slotted([slot=start]){-ms-flex-order:2;order:2}::slotted([slot=secondary]){-ms-flex-order:3;order:3}::slotted([slot=primary]){-ms-flex-order:5;order:5;text-align:end}::slotted([slot=end]){-ms-flex-order:6;order:6;text-align:end}:host(.toolbar-title-large) .toolbar-container{-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:start;align-items:flex-start}:host(.toolbar-title-large) .toolbar-content ion-title{-ms-flex:1;flex:1;-ms-flex-order:8;order:8;min-width:100%}",qi=":host{--border-width:0;--border-style:solid;--opacity:1;--opacity-scale:1;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;width:100%;padding-right:var(--ion-safe-area-right);padding-left:var(--ion-safe-area-left);color:var(--color);font-family:var(--ion-font-family, inherit);contain:content;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-contrast)}:host(.ion-color) .toolbar-background{background:var(--ion-color-base)}.toolbar-container{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:var(--min-height);contain:content;overflow:hidden;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}.toolbar-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;opacity:calc(var(--opacity) * var(--opacity-scale));z-index:-1;pointer-events:none}::slotted(ion-progress-bar){left:0;right:0;bottom:0;position:absolute}:host{--background:var(--ion-toolbar-background, var(--ion-background-color, #fff));--color:var(--ion-toolbar-color, var(--ion-text-color, #424242));--border-color:var(--ion-toolbar-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, #c1c4cd))));--padding-top:0;--padding-bottom:0;--padding-start:0;--padding-end:0;--min-height:56px}.toolbar-content{-ms-flex:1;flex:1;-ms-flex-order:3;order:3;min-width:0;max-width:100%}::slotted(.buttons-first-slot){-webkit-margin-start:4px;margin-inline-start:4px}::slotted(.buttons-last-slot){-webkit-margin-end:4px;margin-inline-end:4px}::slotted([slot=start]){-ms-flex-order:2;order:2}::slotted([slot=secondary]){-ms-flex-order:4;order:4}::slotted([slot=primary]){-ms-flex-order:5;order:5;text-align:end}::slotted([slot=end]){-ms-flex-order:6;order:6;text-align:end}",mo=P(class extends A{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.childrenStyles=new Map}componentWillLoad(){let e=Array.from(this.el.querySelectorAll("ion-buttons")),t=e.find(r=>r.slot==="start");t&&t.classList.add("buttons-first-slot");let o=e.reverse(),i=o.find(r=>r.slot==="end")||o.find(r=>r.slot==="primary")||o.find(r=>r.slot==="secondary");i&&i.classList.add("buttons-last-slot")}childrenStyle(e){e.stopPropagation();let t=e.target.tagName,o=e.detail,i={},r=this.childrenStyles.get(t)||{},s=!1;Object.keys(o).forEach(a=>{let l=`toolbar-${a}`,d=o[a];d!==r[l]&&(s=!0),d&&(i[l]=!0)}),s&&(this.childrenStyles.set(t,i),ae(this))}render(){let e=D(this),t={};return this.childrenStyles.forEach(o=>{Object.assign(t,o)}),c(B,{key:"f6c4f669a6a61c5eac4cbb5ea0aa97c48ae5bd46",class:Object.assign(Object.assign({},t),G(this.color,{[e]:!0,"in-toolbar":W("ion-toolbar",this.el)}))},c("div",{key:"9c81742ffa02de9ba7417025b077d05e67305074",class:"toolbar-background",part:"background"}),c("div",{key:"5fc96d166fa47894a062e41541a9beee38078a36",class:"toolbar-container",part:"container"},c("slot",{key:"b62c0d9d59a70176bdbf769aec6090d7a166853b",name:"start"}),c("slot",{key:"d01d3cc2c50e5aaa49c345b209fe8dbdf3d48131",name:"secondary"}),c("div",{key:"3aaa3a2810aedd38c37eb616158ec7b9638528fc",class:"toolbar-content",part:"content"},c("slot",{key:"357246690f8d5e1cc3ca369611d4845a79edf610"})),c("slot",{key:"06ed3cca4f7ebff4a54cd877dad3cc925ccf9f75",name:"primary"}),c("slot",{key:"e453d43d14a26b0d72f41e1b81a554bab8ece811",name:"end"})))}get el(){return this}static get style(){return{ios:Wi,md:qi}}},[33,"ion-toolbar",{color:[513]},[[0,"ionStyle","childrenStyle"]]]);function fo(){if(typeof customElements>"u")return;["ion-toolbar"].forEach(e=>{switch(e){case"ion-toolbar":customElements.get(e)||customElements.define(e,mo);break}})}var Xi=":host(.ion-color){color:var(--ion-color-base)}",Gi=P(class extends A{constructor(){super(),this.__registerHost(),this.__attachShadow()}render(){let e=D(this);return c(B,{key:"361035eae7b92dc109794348d39bad2f596eb6be",class:G(this.color,{[e]:!0})},c("slot",{key:"c7b8835cf485ba9ecd73298f0529276ce1ea0852"}))}static get style(){return Xi}},[1,"ion-text",{color:[513]}]);function Ui(){if(typeof customElements>"u")return;["ion-text"].forEach(e=>{switch(e){case"ion-text":customElements.get(e)||customElements.define(e,Gi);break}})}var ho=Ui;var go=uo;var bo=fo;var vo=(n,e,t)=>{let o,i=()=>!(e()===void 0||n.label!==void 0||t()===null),r=()=>{i()&&Z(()=>{s()})},s=()=>{let l=e();if(l===void 0)return;if(!i()){l.style.removeProperty("width");return}let d=t().scrollWidth;if(d===0&&l.offsetParent===null&&Q!==void 0&&"IntersectionObserver"in Q){if(o!==void 0)return;let p=o=new IntersectionObserver(u=>{u[0].intersectionRatio===1&&(s(),p.disconnect(),o=void 0)},{threshold:.01,root:n});p.observe(l);return}l.style.setProperty("width",`${d*.75}px`)};return{calculateNotchWidth:r,destroy:()=>{o&&(o.disconnect(),o=void 0)}}};var yo=(n,e,t)=>{let o,i;if(Q!==void 0&&"MutationObserver"in Q){let l=Array.isArray(e)?e:[e];o=new MutationObserver(d=>{for(let p of d)for(let u of p.addedNodes)if(u.nodeType===Node.ELEMENT_NODE&&l.includes(u.slot)){t(),Z(()=>r(u));return}}),o.observe(n,{childList:!0,subtree:!0})}let r=l=>{var d;i&&(i.disconnect(),i=void 0),i=new MutationObserver(p=>{t();for(let u of p)for(let m of u.removedNodes)m.nodeType===Node.ELEMENT_NODE&&m.slot===e&&a()}),i.observe((d=l.parentElement)!==null&&d!==void 0?d:l,{subtree:!0,childList:!0})},s=()=>{o&&(o.disconnect(),o=void 0),a()},a=()=>{i&&(i.disconnect(),i=void 0)};return{destroy:s}},xo=(n,e,t)=>{let o=n==null?0:n.toString().length,i=Ki(o,e);if(t===void 0)return i;try{return t(o,e)}catch(r){return $e("[ion-input] - Exception in provided `counterFormatter`:",r),i}},Ki=(n,e)=>`${n} / ${e}`;var Qi=".sc-ion-input-ios-h{--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--background:transparent;--color:initial;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;padding:0 !important;color:var(--color);font-family:var(--ion-font-family, inherit);z-index:2}ion-item[slot=start].sc-ion-input-ios-h,ion-item [slot=start].sc-ion-input-ios-h,ion-item[slot=end].sc-ion-input-ios-h,ion-item [slot=end].sc-ion-input-ios-h{width:auto}.ion-color.sc-ion-input-ios-h{--highlight-color-focused:var(--ion-color-base)}.input-label-placement-floating.sc-ion-input-ios-h,.input-label-placement-stacked.sc-ion-input-ios-h{min-height:56px}.native-input.sc-ion-input-ios{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:inline-block;position:relative;-ms-flex:1;flex:1;width:100%;max-width:100%;height:100%;max-height:100%;border:0;outline:none;background:transparent;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:1}.native-input.sc-ion-input-ios::-webkit-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios::-moz-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios:-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios::-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios::placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios:-webkit-autofill{background-color:transparent}.native-input.sc-ion-input-ios:invalid{-webkit-box-shadow:none;box-shadow:none}.native-input.sc-ion-input-ios::-ms-clear{display:none}.cloned-input.sc-ion-input-ios{top:0;bottom:0;position:absolute;pointer-events:none}.cloned-input.sc-ion-input-ios{inset-inline-start:0}.cloned-input.sc-ion-input-ios:disabled{opacity:1}.input-clear-icon.sc-ion-input-ios{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;background-position:center;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:30px;height:30px;border:0;outline:none;background-color:transparent;background-repeat:no-repeat;color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));visibility:hidden;-webkit-appearance:none;-moz-appearance:none;appearance:none}.in-item-color.sc-ion-input-ios-h .input-clear-icon.sc-ion-input-ios{color:inherit}.input-clear-icon.sc-ion-input-ios:focus{opacity:0.5}.has-value.sc-ion-input-ios-h .input-clear-icon.sc-ion-input-ios{visibility:visible}.input-wrapper.sc-ion-input-ios{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:stretch;align-items:stretch;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal}.native-wrapper.sc-ion-input-ios{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;width:100%}.ion-touched.ion-invalid.sc-ion-input-ios-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-input-ios-h{--highlight-color:var(--highlight-color-valid)}.input-bottom.sc-ion-input-ios{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem;white-space:normal}.has-focus.ion-valid.sc-ion-input-ios-h,.ion-touched.ion-invalid.sc-ion-input-ios-h{--border-color:var(--highlight-color)}.input-bottom.sc-ion-input-ios .error-text.sc-ion-input-ios{display:none;color:var(--highlight-color-invalid)}.input-bottom.sc-ion-input-ios .helper-text.sc-ion-input-ios{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}.ion-touched.ion-invalid.sc-ion-input-ios-h .input-bottom.sc-ion-input-ios .error-text.sc-ion-input-ios{display:block}.ion-touched.ion-invalid.sc-ion-input-ios-h .input-bottom.sc-ion-input-ios .helper-text.sc-ion-input-ios{display:none}.input-bottom.sc-ion-input-ios .counter.sc-ion-input-ios{-webkit-margin-start:auto;margin-inline-start:auto;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d));white-space:nowrap;-webkit-padding-start:16px;padding-inline-start:16px}.has-focus.sc-ion-input-ios-h input.sc-ion-input-ios{caret-color:var(--highlight-color)}.label-text-wrapper.sc-ion-input-ios{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text.sc-ion-input-ios,.sc-ion-input-ios-s>[slot=label]{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden.sc-ion-input-ios,.input-outline-notch-hidden.sc-ion-input-ios{display:none}.input-wrapper.sc-ion-input-ios input.sc-ion-input-ios{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.input-label-placement-start.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios{-ms-flex-direction:row;flex-direction:row}.input-label-placement-start.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-end.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios{-ms-flex-direction:row-reverse;flex-direction:row-reverse}.input-label-placement-end.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-ios-h .label-text.sc-ion-input-ios{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.input-label-placement-stacked.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}.input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform-origin:left top;transform-origin:left top;max-width:100%;z-index:2}[dir=rtl].sc-ion-input-ios-h -no-combinator.input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .sc-ion-input-ios-h -no-combinator.input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl].input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl].sc-ion-input-ios-h -no-combinator.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .sc-ion-input-ios-h -no-combinator.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl].input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.input-label-placement-stacked.sc-ion-input-ios-h:dir(rtl) .label-text-wrapper.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h:dir(rtl) .label-text-wrapper.sc-ion-input-ios{-webkit-transform-origin:right top;transform-origin:right top}}.input-label-placement-stacked.sc-ion-input-ios-h input.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0}.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios{opacity:0}.has-focus.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios,.has-value.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios{opacity:1}.label-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}.sc-ion-input-ios-s>[slot=start]:last-of-type{-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}.sc-ion-input-ios-s>[slot=end]:first-of-type{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}.sc-ion-input-ios-h[disabled].sc-ion-input-ios-s>ion-input-password-toggle,.sc-ion-input-ios-h[disabled] .sc-ion-input-ios-s>ion-input-password-toggle,.sc-ion-input-ios-h[readonly].sc-ion-input-ios-s>ion-input-password-toggle,.sc-ion-input-ios-h[readonly] .sc-ion-input-ios-s>ion-input-password-toggle{display:none}.sc-ion-input-ios-h{--border-width:0.55px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));--highlight-height:0px;font-size:inherit}.input-clear-icon.sc-ion-input-ios ion-icon.sc-ion-input-ios{width:18px;height:18px}.input-disabled.sc-ion-input-ios-h{opacity:0.3}.sc-ion-input-ios-s>ion-button[slot=start].button-has-icon-only,.sc-ion-input-ios-s>ion-button[slot=end].button-has-icon-only{--border-radius:50%;--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;aspect-ratio:1}",Ji=".sc-ion-input-md-h{--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--background:transparent;--color:initial;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;padding:0 !important;color:var(--color);font-family:var(--ion-font-family, inherit);z-index:2}ion-item[slot=start].sc-ion-input-md-h,ion-item [slot=start].sc-ion-input-md-h,ion-item[slot=end].sc-ion-input-md-h,ion-item [slot=end].sc-ion-input-md-h{width:auto}.ion-color.sc-ion-input-md-h{--highlight-color-focused:var(--ion-color-base)}.input-label-placement-floating.sc-ion-input-md-h,.input-label-placement-stacked.sc-ion-input-md-h{min-height:56px}.native-input.sc-ion-input-md{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:inline-block;position:relative;-ms-flex:1;flex:1;width:100%;max-width:100%;height:100%;max-height:100%;border:0;outline:none;background:transparent;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:1}.native-input.sc-ion-input-md::-webkit-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md::-moz-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md:-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md::-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md::placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md:-webkit-autofill{background-color:transparent}.native-input.sc-ion-input-md:invalid{-webkit-box-shadow:none;box-shadow:none}.native-input.sc-ion-input-md::-ms-clear{display:none}.cloned-input.sc-ion-input-md{top:0;bottom:0;position:absolute;pointer-events:none}.cloned-input.sc-ion-input-md{inset-inline-start:0}.cloned-input.sc-ion-input-md:disabled{opacity:1}.input-clear-icon.sc-ion-input-md{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;background-position:center;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:30px;height:30px;border:0;outline:none;background-color:transparent;background-repeat:no-repeat;color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));visibility:hidden;-webkit-appearance:none;-moz-appearance:none;appearance:none}.in-item-color.sc-ion-input-md-h .input-clear-icon.sc-ion-input-md{color:inherit}.input-clear-icon.sc-ion-input-md:focus{opacity:0.5}.has-value.sc-ion-input-md-h .input-clear-icon.sc-ion-input-md{visibility:visible}.input-wrapper.sc-ion-input-md{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:stretch;align-items:stretch;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal}.native-wrapper.sc-ion-input-md{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;width:100%}.ion-touched.ion-invalid.sc-ion-input-md-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-input-md-h{--highlight-color:var(--highlight-color-valid)}.input-bottom.sc-ion-input-md{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem;white-space:normal}.has-focus.ion-valid.sc-ion-input-md-h,.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}.input-bottom.sc-ion-input-md .error-text.sc-ion-input-md{display:none;color:var(--highlight-color-invalid)}.input-bottom.sc-ion-input-md .helper-text.sc-ion-input-md{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}.ion-touched.ion-invalid.sc-ion-input-md-h .input-bottom.sc-ion-input-md .error-text.sc-ion-input-md{display:block}.ion-touched.ion-invalid.sc-ion-input-md-h .input-bottom.sc-ion-input-md .helper-text.sc-ion-input-md{display:none}.input-bottom.sc-ion-input-md .counter.sc-ion-input-md{-webkit-margin-start:auto;margin-inline-start:auto;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d));white-space:nowrap;-webkit-padding-start:16px;padding-inline-start:16px}.has-focus.sc-ion-input-md-h input.sc-ion-input-md{caret-color:var(--highlight-color)}.label-text-wrapper.sc-ion-input-md{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text.sc-ion-input-md,.sc-ion-input-md-s>[slot=label]{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden.sc-ion-input-md,.input-outline-notch-hidden.sc-ion-input-md{display:none}.input-wrapper.sc-ion-input-md input.sc-ion-input-md{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.input-label-placement-start.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{-ms-flex-direction:row;flex-direction:row}.input-label-placement-start.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-end.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{-ms-flex-direction:row-reverse;flex-direction:row-reverse}.input-label-placement-end.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-md-h .label-text.sc-ion-input-md{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.input-label-placement-stacked.sc-ion-input-md-h .input-wrapper.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:left top;transform-origin:left top;max-width:100%;z-index:2}[dir=rtl].sc-ion-input-md-h -no-combinator.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].sc-ion-input-md-h -no-combinator.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.input-label-placement-stacked.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}}.input-label-placement-stacked.sc-ion-input-md-h input.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0}.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{opacity:0}.has-focus.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md,.has-value.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{opacity:1}.label-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}.sc-ion-input-md-s>[slot=start]:last-of-type{-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}.sc-ion-input-md-s>[slot=end]:first-of-type{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}.sc-ion-input-md-h[disabled].sc-ion-input-md-s>ion-input-password-toggle,.sc-ion-input-md-h[disabled] .sc-ion-input-md-s>ion-input-password-toggle,.sc-ion-input-md-h[readonly].sc-ion-input-md-s>ion-input-password-toggle,.sc-ion-input-md-h[readonly] .sc-ion-input-md-s>ion-input-password-toggle{display:none}.input-fill-solid.sc-ion-input-md-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--border-color:var(--ion-color-step-500, var(--ion-background-color-step-500, gray));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}.input-fill-solid.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{border-bottom:var(--border-width) var(--border-style) var(--border-color)}.has-focus.input-fill-solid.ion-valid.sc-ion-input-md-h,.input-fill-solid.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}.input-fill-solid.sc-ion-input-md-h .input-bottom.sc-ion-input-md{border-top:none}@media (any-hover: hover){.input-fill-solid.sc-ion-input-md-h:hover{--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6));--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}.input-fill-solid.has-focus.sc-ion-input-md-h{--background:var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}.input-fill-solid.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0px;border-end-start-radius:0px}.label-floating.input-fill-solid.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{max-width:calc(100% / 0.75)}.input-fill-outline.sc-ion-input-md-h{--border-color:var(--ion-color-step-300, var(--ion-background-color-step-300, #b3b3b3));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}.input-fill-outline.input-shape-round.sc-ion-input-md-h{--border-radius:28px;--padding-start:32px;--padding-end:32px}.has-focus.input-fill-outline.ion-valid.sc-ion-input-md-h,.input-fill-outline.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}@media (any-hover: hover){.input-fill-outline.sc-ion-input-md-h:hover{--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}.input-fill-outline.has-focus.sc-ion-input-md-h{--border-width:var(--highlight-height);--border-color:var(--highlight-color)}.input-fill-outline.sc-ion-input-md-h .input-bottom.sc-ion-input-md{border-top:none}.input-fill-outline.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{border-bottom:none}.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:left top;transform-origin:left top;position:absolute;max-width:calc(100% - var(--padding-start) - var(--padding-end))}[dir=rtl].sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md,.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}}.input-fill-outline.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{position:relative}.label-floating.input-fill-outline.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform:translateY(-32%) scale(0.75);transform:translateY(-32%) scale(0.75);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;max-width:calc((100% - var(--padding-start) - var(--padding-end) - 8px) / 0.75)}.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h input.sc-ion-input-md,.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{margin-left:0;margin-right:0;margin-top:6px;margin-bottom:6px}.input-fill-outline.sc-ion-input-md-h .input-outline-container.sc-ion-input-md{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;width:100%;height:100%}.input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md,.input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md{pointer-events:none}.input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md,.input-fill-outline.sc-ion-input-md-h .input-outline-notch.sc-ion-input-md,.input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md{border-top:var(--border-width) var(--border-style) var(--border-color);border-bottom:var(--border-width) var(--border-style) var(--border-color)}.input-fill-outline.sc-ion-input-md-h .input-outline-notch.sc-ion-input-md{max-width:calc(100% - var(--padding-start) - var(--padding-end))}.input-fill-outline.sc-ion-input-md-h .notch-spacer.sc-ion-input-md{-webkit-padding-end:8px;padding-inline-end:8px;font-size:calc(1em * 0.75);opacity:0;pointer-events:none;-webkit-box-sizing:content-box;box-sizing:content-box}.input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md{border-start-start-radius:var(--border-radius);border-start-end-radius:0px;border-end-end-radius:0px;border-end-start-radius:var(--border-radius);-webkit-border-start:var(--border-width) var(--border-style) var(--border-color);border-inline-start:var(--border-width) var(--border-style) var(--border-color);width:calc(var(--padding-start) - 4px)}.input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md{-webkit-border-end:var(--border-width) var(--border-style) var(--border-color);border-inline-end:var(--border-width) var(--border-style) var(--border-color);border-start-start-radius:0px;border-start-end-radius:var(--border-radius);border-end-end-radius:var(--border-radius);border-end-start-radius:0px;-ms-flex-positive:1;flex-grow:1}.label-floating.input-fill-outline.sc-ion-input-md-h .input-outline-notch.sc-ion-input-md{border-top:none}.sc-ion-input-md-h{--border-width:1px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));--highlight-height:2px;font-size:inherit}.input-clear-icon.sc-ion-input-md ion-icon.sc-ion-input-md{width:22px;height:22px}.input-disabled.sc-ion-input-md-h{opacity:0.38}.has-focus.ion-valid.sc-ion-input-md-h,.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}.input-bottom.sc-ion-input-md .counter.sc-ion-input-md{letter-spacing:0.0333333333em}.input-label-placement-floating.has-focus.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-stacked.has-focus.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{color:var(--highlight-color)}.has-focus.input-label-placement-floating.ion-valid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-floating.ion-touched.ion-invalid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.has-focus.input-label-placement-stacked.ion-valid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-stacked.ion-touched.ion-invalid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{color:var(--highlight-color)}.input-highlight.sc-ion-input-md{bottom:-1px;position:absolute;width:100%;height:var(--highlight-height);-webkit-transform:scale(0);transform:scale(0);-webkit-transition:-webkit-transform 200ms;transition:-webkit-transform 200ms;transition:transform 200ms;transition:transform 200ms, -webkit-transform 200ms;background:var(--highlight-color)}.input-highlight.sc-ion-input-md{inset-inline-start:0}.has-focus.sc-ion-input-md-h .input-highlight.sc-ion-input-md{-webkit-transform:scale(1);transform:scale(1)}.in-item.sc-ion-input-md-h .input-highlight.sc-ion-input-md{bottom:0}.in-item.sc-ion-input-md-h .input-highlight.sc-ion-input-md{inset-inline-start:0}.input-shape-round.sc-ion-input-md-h{--border-radius:16px}.sc-ion-input-md-s>ion-button[slot=start].button-has-icon-only,.sc-ion-input-md-s>ion-button[slot=end].button-has-icon-only{--border-radius:50%;--padding-start:8px;--padding-end:8px;--padding-top:8px;--padding-bottom:8px;aspect-ratio:1;min-height:40px}",er=P(class extends A{constructor(){super(),this.__registerHost(),this.ionInput=v(this,"ionInput",7),this.ionChange=v(this,"ionChange",7),this.ionBlur=v(this,"ionBlur",7),this.ionFocus=v(this,"ionFocus",7),this.inputId=`ion-input-${tr++}`,this.helperTextId=`${this.inputId}-helper-text`,this.errorTextId=`${this.inputId}-error-text`,this.inheritedAttributes={},this.isComposing=!1,this.didInputClearOnEdit=!1,this.hasFocus=!1,this.autocapitalize="off",this.autocomplete="off",this.autocorrect="off",this.autofocus=!1,this.clearInput=!1,this.counter=!1,this.disabled=!1,this.labelPlacement="start",this.name=this.inputId,this.readonly=!1,this.required=!1,this.spellcheck=!1,this.type="text",this.value="",this.onInput=e=>{let t=e.target;t&&(this.value=t.value||""),this.emitInputChange(e)},this.onChange=e=>{this.emitValueChange(e)},this.onBlur=e=>{this.hasFocus=!1,this.focusedValue!==this.value&&this.emitValueChange(e),this.didInputClearOnEdit=!1,this.ionBlur.emit(e)},this.onFocus=e=>{this.hasFocus=!0,this.focusedValue=this.value,this.ionFocus.emit(e)},this.onKeydown=e=>{this.checkClearOnEdit(e)},this.onCompositionStart=()=>{this.isComposing=!0},this.onCompositionEnd=()=>{this.isComposing=!1},this.clearTextInput=e=>{this.clearInput&&!this.readonly&&!this.disabled&&e&&(e.preventDefault(),e.stopPropagation(),this.setFocus()),this.value="",this.emitInputChange(e)},this.onLabelClick=e=>{e.target===e.currentTarget&&e.stopPropagation()}}debounceChanged(){let{ionInput:e,debounce:t,originalIonInput:o}=this;this.ionInput=t===void 0?o??e:sn(e,t)}onTypeChange(){let e=this.el.querySelector("ion-input-password-toggle");e&&(e.type=this.type)}valueChanged(){let e=this.nativeInput,t=this.getValue();e&&e.value!==t&&!this.isComposing&&(e.value=t)}onDirChanged(e){this.inheritedAttributes=Object.assign(Object.assign({},this.inheritedAttributes),{dir:e}),ae(this)}onClickCapture(e){let t=this.nativeInput;t&&e.target===t&&(e.stopPropagation(),this.el.click())}componentWillLoad(){this.inheritedAttributes=Object.assign(Object.assign({},fe(this.el)),He(this.el,["tabindex","title","data-form-type","dir"]))}connectedCallback(){let{el:e}=this;this.slotMutationController=yo(e,["label","start","end"],()=>ae(this)),this.notchController=vo(e,()=>this.notchSpacerEl,()=>this.labelSlot),this.debounceChanged(),q.isBrowser&&document.dispatchEvent(new CustomEvent("ionInputDidLoad",{detail:this.el}))}componentDidLoad(){this.originalIonInput=this.ionInput,this.onTypeChange(),this.debounceChanged()}componentDidRender(){var e;(e=this.notchController)===null||e===void 0||e.calculateNotchWidth()}disconnectedCallback(){q.isBrowser&&document.dispatchEvent(new CustomEvent("ionInputDidUnload",{detail:this.el})),this.slotMutationController&&(this.slotMutationController.destroy(),this.slotMutationController=void 0),this.notchController&&(this.notchController.destroy(),this.notchController=void 0)}setFocus(){return g(this,null,function*(){this.nativeInput&&this.nativeInput.focus()})}getInputElement(){return g(this,null,function*(){return this.nativeInput||(yield new Promise(e=>Ae(this.el,e))),Promise.resolve(this.nativeInput)})}emitValueChange(e){let{value:t}=this,o=t==null?t:t.toString();this.focusedValue=o,this.ionChange.emit({value:o,event:e})}emitInputChange(e){let{value:t}=this,o=t==null?t:t.toString();this.ionInput.emit({value:o,event:e})}shouldClearOnEdit(){let{type:e,clearOnEdit:t}=this;return t===void 0?e==="password":t}getValue(){return typeof this.value=="number"?this.value.toString():(this.value||"").toString()}checkClearOnEdit(e){if(!this.shouldClearOnEdit())return;let o=["Enter","Tab","Shift","Meta","Alt","Control"].includes(e.key);!this.didInputClearOnEdit&&this.hasValue()&&!o&&(this.value="",this.emitInputChange(e)),o||(this.didInputClearOnEdit=!0)}hasValue(){return this.getValue().length>0}renderHintText(){let{helperText:e,errorText:t,helperTextId:o,errorTextId:i}=this;return[c("div",{id:o,class:"helper-text"},e),c("div",{id:i,class:"error-text"},t)]}getHintTextID(){let{el:e,helperText:t,errorText:o,helperTextId:i,errorTextId:r}=this;if(e.classList.contains("ion-touched")&&e.classList.contains("ion-invalid")&&o)return r;if(t)return i}renderCounter(){let{counter:e,maxlength:t,counterFormatter:o,value:i}=this;if(!(e!==!0||t===void 0))return c("div",{class:"counter"},xo(i,t,o))}renderBottomContent(){let{counter:e,helperText:t,errorText:o,maxlength:i}=this;if(!(!(t||o)&&!(e===!0&&i!==void 0)))return c("div",{class:"input-bottom"},this.renderHintText(),this.renderCounter())}renderLabel(){let{label:e}=this;return c("div",{class:{"label-text-wrapper":!0,"label-text-wrapper-hidden":!this.hasLabel}},e===void 0?c("slot",{name:"label"}):c("div",{class:"label-text"},e))}get labelSlot(){return this.el.querySelector('[slot="label"]')}get hasLabel(){return this.label!==void 0||this.labelSlot!==null}renderLabelContainer(){return D(this)==="md"&&this.fill==="outline"?[c("div",{class:"input-outline-container"},c("div",{class:"input-outline-start"}),c("div",{class:{"input-outline-notch":!0,"input-outline-notch-hidden":!this.hasLabel}},c("div",{class:"notch-spacer","aria-hidden":"true",ref:o=>this.notchSpacerEl=o},this.label)),c("div",{class:"input-outline-end"})),this.renderLabel()]:this.renderLabel()}render(){let{disabled:e,fill:t,readonly:o,shape:i,inputId:r,labelPlacement:s,el:a,hasFocus:l,clearInputIcon:d}=this,p=D(this),u=this.getValue(),m=W("ion-item",this.el),f=p==="md"&&t!=="outline"&&!m,w=d??(p==="ios"?kn:En),h=this.hasValue(),C=a.querySelector('[slot="start"], [slot="end"]')!==null,k=s==="stacked"||s==="floating"&&(h||l||C);return c(B,{key:"41b2526627e7d2773a80f011b123284203a71ca0",class:G(this.color,{[p]:!0,"has-value":h,"has-focus":l,"label-floating":k,[`input-fill-${t}`]:t!==void 0,[`input-shape-${i}`]:i!==void 0,[`input-label-placement-${s}`]:!0,"in-item":m,"in-item-color":W("ion-item.ion-color",this.el),"input-disabled":e})},c("label",{key:"9ab078363e32528102b441ad1791d83f86fdcbdc",class:"input-wrapper",htmlFor:r,onClick:this.onLabelClick},this.renderLabelContainer(),c("div",{key:"e34b594980ec62e4c618e827fadf7669a39ad0d8",class:"native-wrapper",onClick:this.onLabelClick},c("slot",{key:"12dc04ead5502e9e5736240e918bf9331bf7b5d9",name:"start"}),c("input",Object.assign({key:"df356eb4ced23109b2c0242f36dc043aba8782d6",class:"native-input",ref:y=>this.nativeInput=y,id:r,disabled:e,autoCapitalize:this.autocapitalize,autoComplete:this.autocomplete,autoCorrect:this.autocorrect,autoFocus:this.autofocus,enterKeyHint:this.enterkeyhint,inputMode:this.inputmode,min:this.min,max:this.max,minLength:this.minlength,maxLength:this.maxlength,multiple:this.multiple,name:this.name,pattern:this.pattern,placeholder:this.placeholder||"",readOnly:o,required:this.required,spellcheck:this.spellcheck,step:this.step,type:this.type,value:u,onInput:this.onInput,onChange:this.onChange,onBlur:this.onBlur,onFocus:this.onFocus,onKeyDown:this.onKeydown,onCompositionstart:this.onCompositionStart,onCompositionend:this.onCompositionEnd,"aria-describedby":this.getHintTextID(),"aria-invalid":this.getHintTextID()===this.errorTextId},this.inheritedAttributes)),this.clearInput&&!o&&!e&&c("button",{key:"f79f68cabcd4ea99419331174a377827db0c0741","aria-label":"reset",type:"button",class:"input-clear-icon",onPointerDown:y=>{y.preventDefault()},onClick:this.clearTextInput},c("ion-icon",{key:"237ec07ec2e10f08818a332bb596578c2c49f770","aria-hidden":"true",icon:w})),c("slot",{key:"1f0a3624aa3e8dc3c307a6762230ab698768a5e5",name:"end"})),f&&c("div",{key:"8a8cbb82695a722a0010b53dd0b1f1f97534a20b",class:"input-highlight"})),this.renderBottomContent())}get el(){return this}static get watchers(){return{debounce:["debounceChanged"],type:["onTypeChange"],value:["valueChanged"],dir:["onDirChanged"]}}static get style(){return{ios:Qi,md:Ji}}},[38,"ion-input",{color:[513],autocapitalize:[1],autocomplete:[1],autocorrect:[1],autofocus:[4],clearInput:[4,"clear-input"],clearInputIcon:[1,"clear-input-icon"],clearOnEdit:[4,"clear-on-edit"],counter:[4],counterFormatter:[16,"counter-formatter"],debounce:[2],disabled:[516],enterkeyhint:[1],errorText:[1,"error-text"],fill:[1],inputmode:[1],helperText:[1,"helper-text"],label:[1],labelPlacement:[1,"label-placement"],max:[8],maxlength:[2],min:[8],minlength:[2],multiple:[4],name:[1],pattern:[1],placeholder:[1],readonly:[516],required:[4],shape:[1],spellcheck:[4],step:[1],type:[1],value:[1032],hasFocus:[32],setFocus:[64],getInputElement:[64]},[[2,"click","onClickCapture"]],{debounce:["debounceChanged"],type:["onTypeChange"],value:["valueChanged"],dir:["onDirChanged"]}]),tr=0;function nr(){if(typeof customElements>"u")return;["ion-input","ion-icon"].forEach(e=>{switch(e){case"ion-input":customElements.get(e)||customElements.define(e,er);break;case"ion-icon":customElements.get(e)||Mn();break}})}var wo=nr;var ir=["outletContent"],pe=["*"];var mc=(()=>{let n=class rt extends vn{parentOutlet;outletContent;constructor(t,o,i,r,s,a,l,d){super(t,o,i,r,s,a,l,d),this.parentOutlet=d}static \u0275fac=function(o){return new(o||rt)(mt("name"),mt("tabs"),S(Jt),S(ne),S(tn),S(ie),S(en),S(rt,12))};static \u0275cmp=oe({type:rt,selectors:[["ion-router-outlet"]],viewQuery:function(o,i){if(o&1&&Gt(ir,7,Vt),o&2){let r;Ut(r=Kt())&&(i.outletContent=r.first)}},features:[ft],ngContentSelectors:pe,decls:3,vars:0,consts:[["outletContent",""]],template:function(o,i){o&1&&(re(),Wt(0,null,0),se(2),qt())},encapsulation:2})};return n=te([bn({defineCustomElementFn:An})],n),n})();var rr=(n,e)=>{let t=n.prototype;e.forEach(o=>{Object.defineProperty(t,o,{get(){return this.el[o]},set(i){this.z.runOutsideAngular(()=>this.el[o]=i)},configurable:!0})})},sr=(n,e)=>{let t=n.prototype;e.forEach(o=>{t[o]=function(){let i=arguments;return this.z.runOutsideAngular(()=>this.el[o].apply(this.el,i))}})},Lt=(n,e,t)=>{t.forEach(o=>n[o]=$t(e,o))};function ge(n){return function(t){let{defineCustomElementFn:o,inputs:i,methods:r}=n;return o!==void 0&&o(),i&&rr(t,i),r&&sr(t,r),t}}var fc=(()=>{let n=class jt{z;el;constructor(t,o,i){this.z=i,t.detach(),this.el=o.nativeElement}static \u0275fac=function(o){return new(o||jt)(S(ce),S(ne),S(ie))};static \u0275cmp=oe({type:jt,selectors:[["ion-app"]],ngContentSelectors:pe,decls:1,vars:0,template:function(o,i){o&1&&(re(),se(0))},encapsulation:2,changeDetection:0})};return n=te([ge({defineCustomElementFn:Kn,methods:["setFocus"]})],n),n})();var hc=(()=>{let n=class Mt{z;el;constructor(t,o,i){this.z=i,t.detach(),this.el=o.nativeElement,Lt(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(o){return new(o||Mt)(S(ce),S(ne),S(ie))};static \u0275cmp=oe({type:Mt,selectors:[["ion-button"]],inputs:{buttonType:"buttonType",color:"color",disabled:"disabled",download:"download",expand:"expand",fill:"fill",form:"form",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",shape:"shape",size:"size",strong:"strong",target:"target",type:"type"},ngContentSelectors:pe,decls:1,vars:0,template:function(o,i){o&1&&(re(),se(0))},encapsulation:2,changeDetection:0})};return n=te([ge({defineCustomElementFn:eo,inputs:["buttonType","color","disabled","download","expand","fill","form","href","mode","rel","routerAnimation","routerDirection","shape","size","strong","target","type"]})],n),n})();var gc=(()=>{let n=class zt{z;el;constructor(t,o,i){this.z=i,t.detach(),this.el=o.nativeElement,Lt(this,this.el,["ionScrollStart","ionScroll","ionScrollEnd"])}static \u0275fac=function(o){return new(o||zt)(S(ce),S(ne),S(ie))};static \u0275cmp=oe({type:zt,selectors:[["ion-content"]],inputs:{color:"color",fixedSlotPlacement:"fixedSlotPlacement",forceOverscroll:"forceOverscroll",fullscreen:"fullscreen",scrollEvents:"scrollEvents",scrollX:"scrollX",scrollY:"scrollY"},ngContentSelectors:pe,decls:1,vars:0,template:function(o,i){o&1&&(re(),se(0))},encapsulation:2,changeDetection:0})};return n=te([ge({defineCustomElementFn:oo,inputs:["color","fixedSlotPlacement","forceOverscroll","fullscreen","scrollEvents","scrollX","scrollY"],methods:["getScrollElement","scrollToTop","scrollToBottom","scrollByPoint","scrollToPoint"]})],n),n})();var bc=(()=>{let n=class At{z;el;constructor(t,o,i){this.z=i,t.detach(),this.el=o.nativeElement}static \u0275fac=function(o){return new(o||At)(S(ce),S(ne),S(ie))};static \u0275cmp=oe({type:At,selectors:[["ion-header"]],inputs:{collapse:"collapse",mode:"mode",translucent:"translucent"},ngContentSelectors:pe,decls:1,vars:0,template:function(o,i){o&1&&(re(),se(0))},encapsulation:2,changeDetection:0})};return n=te([ge({defineCustomElementFn:co,inputs:["collapse","mode","translucent"]})],n),n})();var vc=(()=>{let n=class Pt{z;el;constructor(t,o,i){this.z=i,t.detach(),this.el=o.nativeElement}static \u0275fac=function(o){return new(o||Pt)(S(ce),S(ne),S(ie))};static \u0275cmp=oe({type:Pt,selectors:[["ion-text"]],inputs:{color:"color",mode:"mode"},ngContentSelectors:pe,decls:1,vars:0,template:function(o,i){o&1&&(re(),se(0))},encapsulation:2,changeDetection:0})};return n=te([ge({defineCustomElementFn:ho,inputs:["color","mode"]})],n),n})();var yc=(()=>{let n=class Bt{z;el;constructor(t,o,i){this.z=i,t.detach(),this.el=o.nativeElement}static \u0275fac=function(o){return new(o||Bt)(S(ce),S(ne),S(ie))};static \u0275cmp=oe({type:Bt,selectors:[["ion-title"]],inputs:{color:"color",size:"size"},ngContentSelectors:pe,decls:1,vars:0,template:function(o,i){o&1&&(re(),se(0))},encapsulation:2,changeDetection:0})};return n=te([ge({defineCustomElementFn:go,inputs:["color","size"]})],n),n})();var xc=(()=>{let n=class Rt{z;el;constructor(t,o,i){this.z=i,t.detach(),this.el=o.nativeElement}static \u0275fac=function(o){return new(o||Rt)(S(ce),S(ne),S(ie))};static \u0275cmp=oe({type:Rt,selectors:[["ion-toolbar"]],inputs:{color:"color",mode:"mode"},ngContentSelectors:pe,decls:1,vars:0,template:function(o,i){o&1&&(re(),se(0))},encapsulation:2,changeDetection:0})};return n=te([ge({defineCustomElementFn:bo,inputs:["color","mode"]})],n),n})();var ar=(()=>{class n extends bt{angularDelegate=ye(Ke);injector=ye(ze);environmentInjector=ye(Le);constructor(){super(un),Nn()}create(t){return super.create(ut(pt({},t),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"modal")}))}static \u0275fac=function(o){return new(o||n)};static \u0275prov=_t({token:n,factory:n.\u0275fac})}return n})(),Ft=class extends bt{angularDelegate=ye(Ke);injector=ye(ze);environmentInjector=ye(Le);constructor(){super(mn),Un()}create(e){return super.create(ut(pt({},e),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"popover")}))}},wc=(n={})=>Nt([{provide:gt,useValue:n},{provide:Zt,useFactory:lr,multi:!0,deps:[gt,Yt]},yn(),Ke,ar,Ft]),lr=(n,e)=>()=>{e.documentElement.classList.add("ion-ce"),cn(n)};var cr=["accept","autocapitalize","autocomplete","autocorrect","autofocus","clearInput","clearOnEdit","color","counter","counterFormatter","debounce","disabled","enterkeyhint","errorText","fill","helperText","inputmode","label","labelPlacement","max","maxlength","min","minlength","mode","multiple","name","pattern","placeholder","readonly","required","shape","size","spellcheck","step","type","value"],dr={provide:gn,useExisting:Ht(()=>pr),multi:!0},pr=(()=>{let n=class Ot extends xn{z;el;constructor(t,o,i,r){super(r,o),this.z=i,t.detach(),this.el=o.nativeElement,Lt(this,this.el,["ionInput","ionChange","ionBlur","ionFocus"])}handleIonInput(t){this.handleValueChange(t,t.value)}registerOnChange(t){super.registerOnChange(o=>{this.type==="number"?t(o===""?null:parseFloat(o)):t(o)})}static \u0275fac=function(o){return new(o||Ot)(S(ce),S(ne),S(ie),S(ze))};static \u0275cmp=oe({type:Ot,selectors:[["ion-input"]],hostBindings:function(o,i){o&1&&Xt("ionInput",function(s){return i.handleIonInput(s.target)})},inputs:{accept:"accept",autocapitalize:"autocapitalize",autocomplete:"autocomplete",autocorrect:"autocorrect",autofocus:"autofocus",clearInput:"clearInput",clearOnEdit:"clearOnEdit",color:"color",counter:"counter",counterFormatter:"counterFormatter",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",errorText:"errorText",fill:"fill",helperText:"helperText",inputmode:"inputmode",label:"label",labelPlacement:"labelPlacement",max:"max",maxlength:"maxlength",min:"min",minlength:"minlength",mode:"mode",multiple:"multiple",name:"name",pattern:"pattern",placeholder:"placeholder",readonly:"readonly",required:"required",shape:"shape",size:"size",spellcheck:"spellcheck",step:"step",type:"type",value:"value"},features:[Qt([dr]),ft],ngContentSelectors:pe,decls:1,vars:0,template:function(o,i){o&1&&(re(),se(0))},encapsulation:2,changeDetection:0})};return n=te([ge({defineCustomElementFn:wo,inputs:cr,methods:["setFocus","getInputElement"]})],n),n})();export{mc as a,fc as b,hc as c,gc as d,bc as e,vc as f,yc as g,xc as h,wc as i,pr as j};
